@echo off
setlocal enabledelayedexpansion

echo ****************************************************
echo * OwordTools Deployment Script                     *
echo ****************************************************

:: Check if WORKSPACE variable is set
if "%WORKSPACE%"=="" (
    echo ERROR: WORKSPACE environment variable is not set
    echo This script must be run from <PERSON>
    goto :error
)
echo Using workspace: %WORKSPACE%

:: Set variables
set SOURCE_DIR=%WORKSPACE%\target
set TARGET_DIR=D:\OwordTools\apps
set SERVICE_NAME=OwordToolsApp
set JAR_NAME=oAdmin.jar

:: Get current date and time for backup filename
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%"
set "Min=%dt:~10,2%"
set "Sec=%dt:~12,2%"
set "TIMESTAMP=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

echo Current timestamp: %TIMESTAMP%

:: Check if source directory exists
if not exist "%SOURCE_DIR%" (
    echo ERROR: Source directory does not exist: %SOURCE_DIR%
    echo Make sure Maven build completed successfully
    goto :error
)

:: Check if source JAR exists
if not exist "%SOURCE_DIR%\*.jar" (
    echo ERROR: No JAR file found in %SOURCE_DIR%
    echo Listing contents of source directory:
    dir "%SOURCE_DIR%" /b
    goto :error
)

:: Get the source JAR file (assuming there's only one)
for %%f in ("%SOURCE_DIR%\*.jar") do set SOURCE_JAR=%%f
echo Source JAR: %SOURCE_JAR%

:: Verify the JAR file exists
if not exist "%SOURCE_JAR%" (
    echo ERROR: JAR file not found: %SOURCE_JAR%
    goto :error
)

:: Check if target directory exists, create if not
if not exist "%TARGET_DIR%" (
    echo Creating target directory %TARGET_DIR%...
    mkdir "%TARGET_DIR%"
)

echo Stopping %SERVICE_NAME% service...
net stop %SERVICE_NAME%
if %ERRORLEVEL% neq 0 (
    echo WARNING: Failed to stop service. It might not be running.
)

:: Wait for service to fully stop
timeout /t 5

:: Backup existing JAR if it exists
if exist "%TARGET_DIR%\%JAR_NAME%" (
    echo Backing up existing JAR file...
    move "%TARGET_DIR%\%JAR_NAME%" "%TARGET_DIR%\%JAR_NAME%.%TIMESTAMP%.bak"
)

:: Copy new JAR
echo Copying new JAR file...
copy "%SOURCE_JAR%" "%TARGET_DIR%\%JAR_NAME%"
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to copy JAR file.
    goto :error
)

:: Start service
echo Starting %SERVICE_NAME% service...
net start %SERVICE_NAME%
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to start service.
    goto :error
)

echo ****************************************************
echo * Deployment completed successfully!               *
echo ****************************************************
goto :end

:error
echo ****************************************************
echo * Deployment failed!                               *
echo ****************************************************
exit /b 1

:end
exit /b 0