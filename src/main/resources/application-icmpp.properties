app.version=1.0.2.ICM-PP

## FlyWay Config

spring.flyway.locations=classpath:db/migrations/oracle/icmpp
spring.flyway.schemas=OTOOLS_ICM_PREPROD
spring.flyway.placeholders.schema.name=OTOOLS_ICM_PREPROD

#Accesanyware PP datasource
spring.datasource.aaw.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.aaw.url=*********************************************************
spring.datasource.aaw.username=STRM_OWORD_ICM_PREPROD

# Primary DataSource OWord PP
spring.datasource.osearch.url=************************************************************
spring.datasource.osearch.username=OWORD_ICM_PREPROD
spring.datasource.osearch.driver-class-name=oracle.jdbc.driver.OracleDriver

# Secondary DataSource OWord_Tools PP
spring.datasource.owordtools.url=***********************************************************************************
spring.datasource.owordtools.username=OTOOLS_ICM_PREPROD
spring.datasource.owordtools.driver-class-name=oracle.jdbc.driver.OracleDriver

server.port=9090
spring.config.import=file:D:/OwordTools/apps/config/icm/secret.properties
