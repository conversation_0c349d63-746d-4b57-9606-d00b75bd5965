/*! © SpryMedia Ltd, Shodhan Save - datatables.net/license */
import jQuery from"jquery";import DataTable from"datatables.net";let $=jQuery;DataTable.ext.type.order["natural-time-delta-pre"]=function(e){var a=0,m=e.match(/(\d+\s*decades?\s*)?(\d+\s*years?\s*)?(\d+\s*months?\s*)?(\d+\s*weeks?\s*)?(\d+\s*days?\s*)?(\d+\s*hours?\s*)?(\d+\s*minutes?\s*)?(\d+\s*seconds?\s*)?(\d+\s*milliseconds?\s*)?(\d+\s*microseconds?\s*)?/i);m.reverse();return[{splitter:"us",name:"microsecond",mul:1e-6},{splitter:"ms",name:"millisecond",mul:.001},{splitter:"s",name:"second",mul:1},{splitter:"m",name:"minute",mul:60},{splitter:"h",name:"hour",mul:3600},{splitter:"d",name:"day",mul:86400},{splitter:"w",name:"week",mul:604800},{splitter:"w",name:"month",mul:18144e3},{splitter:"w",name:"year",mul:217728e3},{splitter:"w",name:"decade",mul:217728e4}].forEach(function(e,s){s=m[s],t=e.splitter,e=e.mul;var t,s=void 0===s?0:parseFloat(s.split(t)[0].trim())*e;a+=s}),a||-1};export default DataTable;