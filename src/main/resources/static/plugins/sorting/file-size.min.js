/*! © SpryMedia Ltd - datatables.net/license */
!function(n){var o,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),i=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||o(e),i(e,t),n(t,0,e.document)}:(i(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(e,t,n){"use strict";e=e.fn.dataTable;return e.ext.type.order["file-size-pre"]=function(e){var t;return null===e||""===e?0:(e=e.match(/^(\d+(?:\.\d+)?)\s*([a-z]+)/i))?(t={b:1,bytes:1,kb:1e3,kib:1024,mb:1e6,mib:1048576,gb:1e9,gib:**********,tb:1e12,tib:1099511627776,pb:1e15,pib:0x4000000000000}[e[2].toLowerCase()],parseFloat(e[1])*t):-1},e});