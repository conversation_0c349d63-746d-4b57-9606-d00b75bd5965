/*! © SpryMedia Ltd, Behrooz Janfada - datatables.net/license */
!function(r){var t,u;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return r(e,window,document)}):"object"==typeof exports?(t=require("jquery"),u=function(e,n){n.fn.dataTable||require("datatables.net")(e,n)},"undefined"==typeof window?module.exports=function(e,n){return e=e||window,n=n||t(e),u(e,n),r(n,0,e.document)}:(u(window,t),module.exports=r(t,window,window.document))):r(jQuery,window,document)}(function(e,n,r){"use strict";e=e.fn.dataTable;return e.ext.type.order["farsi-numbers-pre"]=function(e){for(var n="",r=0;r<e.length;r++)n+=function(e){switch(e){case"۰":return 0;case"۱":return 1;case"۲":return 2;case"۳":return 3;case"۴":return 4;case"۵":return 5;case"۶":return 6;case"۷":return 7;case"۸":return 8;case"۹":return 9;default:return 0}}(e.charAt(r));return parseInt(n)},e});