/*! © SpryMedia Ltd, Jumpy - datatables.net/license */
!function(t){var o,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return t(e,window,document)}):"object"==typeof exports?(o=require("jquery"),r=function(e,n){n.fn.dataTable||require("datatables.net")(e,n)},"undefined"==typeof window?module.exports=function(e,n){return e=e||window,n=n||o(e),r(e,n),t(n,0,e.document)}:(r(window,o),module.exports=t(o,window,window.document))):t(jQuery,window,document)}(function(e,n,t){"use strict";e=e.fn.dataTable;return e.ext.type.order["alt-string-pre"]=function(e){return e.match(/alt="(.*?)"/)[1].toLowerCase()},e});