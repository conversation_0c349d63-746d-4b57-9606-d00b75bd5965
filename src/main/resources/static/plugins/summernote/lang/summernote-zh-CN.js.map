{"version": 3, "file": "lang/summernote-zh-CN.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,IADF;AAEJC,QAAAA,MAAM,EAAE,IAFJ;AAGJC,QAAAA,SAAS,EAAE,KAHP;AAIJC,QAAAA,KAAK,EAAE,MAJH;AAKJC,QAAAA,MAAM,EAAE,IALJ;AAMJC,QAAAA,IAAI,EAAE,IANF;AAOJC,QAAAA,aAAa,EAAE,KAPX;AAQJC,QAAAA,SAAS,EAAE,IARP;AASJC,QAAAA,WAAW,EAAE,IATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,MAAM,EAAE,MAFH;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,UAAU,EAAE,SAJP;AAKLC,QAAAA,aAAa,EAAE,SALV;AAMLC,QAAAA,SAAS,EAAE,MANN;AAOLC,QAAAA,UAAU,EAAE,MAPP;AAQLC,QAAAA,SAAS,EAAE,MARN;AASLC,QAAAA,YAAY,EAAE,QATT;AAULC,QAAAA,WAAW,EAAE,OAVR;AAWLC,QAAAA,cAAc,EAAE,SAXX;AAYLC,QAAAA,SAAS,EAAE,OAZN;AAaLC,QAAAA,aAAa,EAAE,UAbV;AAcLC,QAAAA,SAAS,EAAE,SAdN;AAeLC,QAAAA,eAAe,EAAE,OAfZ;AAgBLC,QAAAA,eAAe,EAAE,SAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,YAjBjB;AAkBLC,QAAAA,GAAG,EAAE,MAlBA;AAmBLC,QAAAA,MAAM,EAAE,MAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,SAAS,EAAE,MAFN;AAGLpB,QAAAA,MAAM,EAAE,MAHH;AAILgB,QAAAA,GAAG,EAAE,MAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,IADF;AAEJtB,QAAAA,MAAM,EAAE,MAFJ;AAGJuB,QAAAA,MAAM,EAAE,MAHJ;AAIJC,QAAAA,IAAI,EAAE,MAJF;AAKJC,QAAAA,aAAa,EAAE,MALX;AAMJT,QAAAA,GAAG,EAAE,MAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,WAAW,EAAE,QAFR;AAGLC,QAAAA,WAAW,EAAE,QAHR;AAILC,QAAAA,UAAU,EAAE,QAJP;AAKLC,QAAAA,WAAW,EAAE,QALR;AAMLC,QAAAA,MAAM,EAAE,KANH;AAOLC,QAAAA,MAAM,EAAE,KAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,IADF;AAELC,QAAAA,CAAC,EAAE,IAFE;AAGLC,QAAAA,UAAU,EAAE,IAHP;AAILC,QAAAA,GAAG,EAAE,IAJA;AAKLC,QAAAA,EAAE,EAAE,MALC;AAMLC,QAAAA,EAAE,EAAE,MANC;AAOLC,QAAAA,EAAE,EAAE,MAPC;AAQLC,QAAAA,EAAE,EAAE,MARC;AASLC,QAAAA,EAAE,EAAE,MATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,MADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,IADC;AAEPC,QAAAA,UAAU,EAAE,IAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,IADF;AAETC,QAAAA,OAAO,EAAE,MAFA;AAGTC,QAAAA,MAAM,EAAE,MAHC;AAITC,QAAAA,IAAI,EAAE,KAJG;AAKTC,QAAAA,MAAM,EAAE,MALC;AAMTC,QAAAA,KAAK,EAAE,KANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,MADH;AAELC,QAAAA,IAAI,EAAE,IAFD;AAGLC,QAAAA,UAAU,EAAE,IAHP;AAILC,QAAAA,UAAU,EAAE,IAJP;AAKLC,QAAAA,WAAW,EAAE,IALR;AAMLC,QAAAA,cAAc,EAAE,IANX;AAOLC,QAAAA,KAAK,EAAE,IAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,KADH;AAERC,QAAAA,KAAK,EAAE,IAFC;AAGRC,QAAAA,cAAc,EAAE,MAHR;AAIRC,QAAAA,MAAM,EAAE,IAJA;AAKRC,QAAAA,mBAAmB,EAAE,MALb;AAMRC,QAAAA,aAAa,EAAE,MANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ2B,QAAAA,eAAe,EAAE,MADb;AAEJC,QAAAA,IAAI,EAAE,IAFF;AAGJC,QAAAA,IAAI,EAAE,IAHF;AAIJC,QAAAA,GAAG,EAAE,MAJD;AAKJC,QAAAA,KAAK,EAAE,MALH;AAMJ5F,QAAAA,IAAI,EAAE,IANF;AAOJC,QAAAA,MAAM,EAAE,IAPJ;AAQJC,QAAAA,SAAS,EAAE,KARP;AASJI,QAAAA,aAAa,EAAE,KATX;AAUJuF,QAAAA,YAAY,EAAE,MAVV;AAWJC,QAAAA,WAAW,EAAE,KAXT;AAYJC,QAAAA,aAAa,EAAE,MAZX;AAaJC,QAAAA,YAAY,EAAE,KAbV;AAcJC,QAAAA,WAAW,EAAE,MAdT;AAeJC,QAAAA,mBAAmB,EAAE,MAfjB;AAgBJC,QAAAA,iBAAiB,EAAE,MAhBf;AAiBJlC,QAAAA,OAAO,EAAE,MAjBL;AAkBJC,QAAAA,MAAM,EAAE,MAlBJ;AAmBJkC,QAAAA,UAAU,EAAE,cAnBR;AAoBJC,QAAAA,QAAQ,EAAE,eApBN;AAqBJC,QAAAA,QAAQ,EAAE,eArBN;AAsBJC,QAAAA,QAAQ,EAAE,eAtBN;AAuBJC,QAAAA,QAAQ,EAAE,eAvBN;AAwBJC,QAAAA,QAAQ,EAAE,eAxBN;AAyBJC,QAAAA,QAAQ,EAAE,eAzBN;AA0BJC,QAAAA,oBAAoB,EAAE,OA1BlB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IPC,MAAAA,OAAO,EAAE;AACPnB,QAAAA,IAAI,EAAE,IADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPmB,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,MADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-zh-CN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'zh-CN': {\n      font: {\n        bold: '粗体',\n        italic: '斜体',\n        underline: '下划线',\n        clear: '清除格式',\n        height: '行高',\n        name: '字体',\n        strikethrough: '删除线',\n        subscript: '下标',\n        superscript: '上标',\n        size: '字号',\n      },\n      image: {\n        image: '图片',\n        insert: '插入图片',\n        resizeFull: '缩放至 100%',\n        resizeHalf: '缩放至 50%',\n        resizeQuarter: '缩放至 25%',\n        floatLeft: '靠左浮动',\n        floatRight: '靠右浮动',\n        floatNone: '取消浮动',\n        shapeRounded: '形状: 圆角',\n        shapeCircle: '形状: 圆',\n        shapeThumbnail: '形状: 缩略图',\n        shapeNone: '形状: 无',\n        dragImageHere: '将图片拖拽至此处',\n        dropImage: '拖拽图片或文本',\n        selectFromFiles: '从本地上传',\n        maximumFileSize: '文件大小最大值',\n        maximumFileSizeError: '文件大小超出最大值。',\n        url: '图片地址',\n        remove: '移除图片',\n        original: '原始图片',\n      },\n      video: {\n        video: '视频',\n        videoLink: '视频链接',\n        insert: '插入视频',\n        url: '视频地址',\n        providers: '(优酷, 腾讯, Instagram, DailyMotion, Youtube等)',\n      },\n      link: {\n        link: '链接',\n        insert: '插入链接',\n        unlink: '去除链接',\n        edit: '编辑链接',\n        textToDisplay: '显示文本',\n        url: '链接地址',\n        openInNewWindow: '在新窗口打开',\n      },\n      table: {\n        table: '表格',\n        addRowAbove: '在上方插入行',\n        addRowBelow: '在下方插入行',\n        addColLeft: '在左侧插入列',\n        addColRight: '在右侧插入列',\n        delRow: '删除行',\n        delCol: '删除列',\n        delTable: '删除表格',\n      },\n      hr: {\n        insert: '水平线',\n      },\n      style: {\n        style: '样式',\n        p: '普通',\n        blockquote: '引用',\n        pre: '代码',\n        h1: '标题 1',\n        h2: '标题 2',\n        h3: '标题 3',\n        h4: '标题 4',\n        h5: '标题 5',\n        h6: '标题 6',\n      },\n      lists: {\n        unordered: '无序列表',\n        ordered: '有序列表',\n      },\n      options: {\n        help: '帮助',\n        fullscreen: '全屏',\n        codeview: '源代码',\n      },\n      paragraph: {\n        paragraph: '段落',\n        outdent: '减少缩进',\n        indent: '增加缩进',\n        left: '左对齐',\n        center: '居中对齐',\n        right: '右对齐',\n        justify: '两端对齐',\n      },\n      color: {\n        recent: '最近使用',\n        more: '更多',\n        background: '背景',\n        foreground: '前景',\n        transparent: '透明',\n        setTransparent: '透明',\n        reset: '重置',\n        resetToDefault: '默认',\n      },\n      shortcut: {\n        shortcuts: '快捷键',\n        close: '关闭',\n        textFormatting: '文本格式',\n        action: '动作',\n        paragraphFormatting: '段落格式',\n        documentStyle: '文档样式',\n        extraKeys: '额外按键',\n      },\n      help: {\n        insertParagraph: '插入段落',\n        undo: '撤销',\n        redo: '重做',\n        tab: '增加缩进',\n        untab: '减少缩进',\n        bold: '粗体',\n        italic: '斜体',\n        underline: '下划线',\n        strikethrough: '删除线',\n        removeFormat: '清除格式',\n        justifyLeft: '左对齐',\n        justifyCenter: '居中对齐',\n        justifyRight: '右对齐',\n        justifyFull: '两端对齐',\n        insertUnorderedList: '无序列表',\n        insertOrderedList: '有序列表',\n        outdent: '减少缩进',\n        indent: '增加缩进',\n        formatPara: '设置选中内容样式为 普通',\n        formatH1: '设置选中内容样式为 标题1',\n        formatH2: '设置选中内容样式为 标题2',\n        formatH3: '设置选中内容样式为 标题3',\n        formatH4: '设置选中内容样式为 标题4',\n        formatH5: '设置选中内容样式为 标题5',\n        formatH6: '设置选中内容样式为 标题6',\n        insertHorizontalRule: '插入水平线',\n        'linkDialog.show': '显示链接对话框',\n      },\n      history: {\n        undo: '撤销',\n        redo: '重做',\n      },\n      specialChar: {\n        specialChar: '特殊字符',\n        select: '选取特殊字符',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}