{"version": 3, "file": "lang/summernote-ru-RU.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,YADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,cAHP;AAIJC,QAAAA,KAAK,EAAE,qBAJH;AAKJC,QAAAA,MAAM,EAAE,cALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,aAPX;AAQJC,QAAAA,SAAS,EAAE,eARP;AASJC,QAAAA,WAAW,EAAE,gBATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,MAAM,EAAE,mBAFH;AAGLC,QAAAA,UAAU,EAAE,qBAHP;AAILC,QAAAA,UAAU,EAAE,kBAJP;AAKLC,QAAAA,aAAa,EAAE,kBALV;AAMLC,QAAAA,SAAS,EAAE,mBANN;AAOLC,QAAAA,UAAU,EAAE,oBAPP;AAQLC,QAAAA,SAAS,EAAE,2BARN;AASLC,QAAAA,YAAY,EAAE,qBATT;AAULC,QAAAA,WAAW,EAAE,aAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,YAZN;AAaLC,QAAAA,aAAa,EAAE,0BAbV;AAcLC,QAAAA,SAAS,EAAE,qBAdN;AAeLC,QAAAA,eAAe,EAAE,mBAfZ;AAgBLC,QAAAA,eAAe,EAAE,2BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,oCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,cAlBA;AAmBLC,QAAAA,MAAM,EAAE,kBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,iBAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,WAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJtB,QAAAA,MAAM,EAAE,iBAFJ;AAGJuB,QAAAA,MAAM,EAAE,eAHJ;AAIJC,QAAAA,IAAI,EAAE,eAJF;AAKJC,QAAAA,aAAa,EAAE,oBALX;AAMJT,QAAAA,GAAG,EAAE,kBAND;AAOJU,QAAAA,eAAe,EAAE,wBAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA1CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,sBAFR;AAGLC,QAAAA,WAAW,EAAE,sBAHR;AAILC,QAAAA,UAAU,EAAE,wBAJP;AAKLC,QAAAA,WAAW,EAAE,yBALR;AAMLC,QAAAA,MAAM,EAAE,gBANH;AAOLC,QAAAA,MAAM,EAAE,iBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,YAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,KAJA;AAKLC,QAAAA,EAAE,EAAE,aALC;AAMLC,QAAAA,EAAE,EAAE,aANC;AAOLC,QAAAA,EAAE,EAAE,aAPC;AAQLC,QAAAA,EAAE,EAAE,aARC;AASLC,QAAAA,EAAE,EAAE,aATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAjEA;AA6EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,sBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA7EA;AAiFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,UAAU,EAAE,eAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAjFF;AAsFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,kBAHC;AAITC,QAAAA,IAAI,EAAE,0BAJG;AAKTC,QAAAA,MAAM,EAAE,qBALC;AAMTC,QAAAA,KAAK,EAAE,2BANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAtFJ;AA+FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,WAFD;AAGLC,QAAAA,UAAU,EAAE,WAHP;AAILC,QAAAA,UAAU,EAAE,aAJP;AAKLC,QAAAA,WAAW,EAAE,YALR;AAMLC,QAAAA,cAAc,EAAE,oBANX;AAOLC,QAAAA,KAAK,EAAE,OAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA/FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,kBADH;AAERC,QAAAA,KAAK,EAAE,SAFC;AAGRC,QAAAA,cAAc,EAAE,uBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,0BALb;AAMRC,QAAAA,aAAa,EAAE,iBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,gBADf;AAEJ,gBAAQ,4BAFJ;AAGJ,gBAAQ,6BAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,2BANJ;AAOJ,kBAAU,8BAPN;AAQJ,qBAAa,iCART;AASJ,yBAAiB,gCATb;AAUJ,wBAAgB,gBAVZ;AAWJ,uBAAe,0BAXX;AAYJ,yBAAiB,qBAZb;AAaJ,wBAAgB,2BAbZ;AAcJ,uBAAe,yBAdX;AAeJ,+BAAuB,yCAfnB;AAgBJ,6BAAqB,wCAhBjB;AAiBJ,mBAAW,mCAjBP;AAkBJ,kBAAU,qCAlBN;AAmBJ,sBAAc,iDAnBV;AAoBJ,oBAAY,mCApBR;AAqBJ,oBAAY,mCArBR;AAsBJ,oBAAY,mCAtBR;AAuBJ,oBAAY,mCAvBR;AAwBJ,oBAAY,mCAxBR;AAyBJ,oBAAY,mCAzBR;AA0BJ,gCAAwB,+BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ru-RU.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'ru-RU': {\n      font: {\n        bold: 'Полужирный',\n        italic: 'Кур<PERSON>ив',\n        underline: 'Подчёркнутый',\n        clear: 'Убрать стили шрифта',\n        height: 'Высота линии',\n        name: 'Шриф<PERSON>',\n        strikethrough: 'Зачёркнутый',\n        subscript: 'Нижний индекс',\n        superscript: 'Верхний индекс',\n        size: 'Размер шрифта',\n      },\n      image: {\n        image: 'Картинка',\n        insert: 'Вставить картинку',\n        resizeFull: 'Восстановить размер',\n        resizeHalf: 'Уменьшить до 50%',\n        resizeQuarter: 'Уменьшить до 25%',\n        floatLeft: 'Расположить слева',\n        floatRight: 'Расположить справа',\n        floatNone: 'Расположение по-умолчанию',\n        shapeRounded: 'Форма: Закругленная',\n        shapeCircle: 'Форма: Круг',\n        shapeThumbnail: 'Форма: Миниатюра',\n        shapeNone: 'Форма: Нет',\n        dragImageHere: 'Перетащите сюда картинку',\n        dropImage: 'Перетащите картинку',\n        selectFromFiles: 'Выбрать из файлов',\n        maximumFileSize: 'Максимальный размер файла',\n        maximumFileSizeError: 'Превышен максимальный размер файла',\n        url: 'URL картинки',\n        remove: 'Удалить картинку',\n        original: 'Оригинал',\n      },\n      video: {\n        video: 'Видео',\n        videoLink: 'Ссылка на видео',\n        insert: 'Вставить видео',\n        url: 'URL видео',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion или Youku)',\n      },\n      link: {\n        link: 'Ссылка',\n        insert: 'Вставить ссылку',\n        unlink: 'Убрать ссылку',\n        edit: 'Редактировать',\n        textToDisplay: 'Отображаемый текст',\n        url: 'URL для перехода',\n        openInNewWindow: 'Открывать в новом окне',\n        useProtocol: 'Использовать протокол по умолчанию',\n      },\n      table: {\n        table: 'Таблица',\n        addRowAbove: 'Добавить строку выше',\n        addRowBelow: 'Добавить строку ниже',\n        addColLeft: 'Добавить столбец слева',\n        addColRight: 'Добавить столбец справа',\n        delRow: 'Удалить строку',\n        delCol: 'Удалить столбец',\n        delTable: 'Удалить таблицу',\n      },\n      hr: {\n        insert: 'Вставить горизонтальную линию',\n      },\n      style: {\n        style: 'Стиль',\n        p: 'Нормальный',\n        blockquote: 'Цитата',\n        pre: 'Код',\n        h1: 'Заголовок 1',\n        h2: 'Заголовок 2',\n        h3: 'Заголовок 3',\n        h4: 'Заголовок 4',\n        h5: 'Заголовок 5',\n        h6: 'Заголовок 6',\n      },\n      lists: {\n        unordered: 'Маркированный список',\n        ordered: 'Нумерованный список',\n      },\n      options: {\n        help: 'Помощь',\n        fullscreen: 'На весь экран',\n        codeview: 'Исходный код',\n      },\n      paragraph: {\n        paragraph: 'Параграф',\n        outdent: 'Уменьшить отступ',\n        indent: 'Увеличить отступ',\n        left: 'Выровнять по левому краю',\n        center: 'Выровнять по центру',\n        right: 'Выровнять по правому краю',\n        justify: 'Растянуть по ширине',\n      },\n      color: {\n        recent: 'Последний цвет',\n        more: 'Еще цвета',\n        background: 'Цвет фона',\n        foreground: 'Цвет шрифта',\n        transparent: 'Прозрачный',\n        setTransparent: 'Сделать прозрачным',\n        reset: 'Сброс',\n        resetToDefault: 'Восстановить умолчания',\n      },\n      shortcut: {\n        shortcuts: 'Сочетания клавиш',\n        close: 'Закрыть',\n        textFormatting: 'Форматирование текста',\n        action: 'Действие',\n        paragraphFormatting: 'Форматирование параграфа',\n        documentStyle: 'Стиль документа',\n        extraKeys: 'Дополнительные комбинации',\n      },\n      help: {\n        'insertParagraph': 'Новый параграф',\n        'undo': 'Отменить последнюю команду',\n        'redo': 'Повторить последнюю команду',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Установить стиль \"Жирный\"',\n        'italic': 'Установить стиль \"Наклонный\"',\n        'underline': 'Установить стиль \"Подчеркнутый\"',\n        'strikethrough': 'Установить стиль \"Зачеркнутый\"',\n        'removeFormat': 'Сборсить стили',\n        'justifyLeft': 'Выровнять по левому краю',\n        'justifyCenter': 'Выровнять по центру',\n        'justifyRight': 'Выровнять по правому краю',\n        'justifyFull': 'Растянуть на всю ширину',\n        'insertUnorderedList': 'Включить/отключить маркированный список',\n        'insertOrderedList': 'Включить/отключить нумерованный список',\n        'outdent': 'Убрать отступ в текущем параграфе',\n        'indent': 'Вставить отступ в текущем параграфе',\n        'formatPara': 'Форматировать текущий блок как параграф (тег P)',\n        'formatH1': 'Форматировать текущий блок как H1',\n        'formatH2': 'Форматировать текущий блок как H2',\n        'formatH3': 'Форматировать текущий блок как H3',\n        'formatH4': 'Форматировать текущий блок как H4',\n        'formatH5': 'Форматировать текущий блок как H5',\n        'formatH6': 'Форматировать текущий блок как H6',\n        'insertHorizontalRule': 'Вставить горизонтальную черту',\n        'linkDialog.show': 'Показать диалог \"Ссылка\"',\n      },\n      history: {\n        undo: 'Отменить',\n        redo: 'Повтор',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}