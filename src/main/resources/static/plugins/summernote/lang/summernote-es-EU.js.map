{"version": 3, "file": "lang/summernote-es-EU.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,cAHP;AAIJC,QAAAA,KAAK,EAAE,eAJH;AAKJC,QAAAA,MAAM,EAAE,eALJ;AAMJC,QAAAA,IAAI,EAAE,YANF;AAOJC,QAAAA,aAAa,EAAE,UAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,oBAFH;AAGLC,QAAAA,UAAU,EAAE,4BAHP;AAILC,QAAAA,UAAU,EAAE,uBAJP;AAKLC,QAAAA,aAAa,EAAE,2BALV;AAMLC,QAAAA,SAAS,EAAE,kBANN;AAOLC,QAAAA,UAAU,EAAE,kBAPP;AAQLC,QAAAA,SAAS,EAAE,qBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,wBAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,6BAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,wBAlBA;AAmBLC,QAAAA,MAAM,EAAE,cAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,SAAS,EAAE,kBAFN;AAGLpB,QAAAA,MAAM,EAAE,0BAHH;AAILgB,QAAAA,GAAG,EAAE,wBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJtB,QAAAA,MAAM,EAAE,qBAFJ;AAGJuB,QAAAA,MAAM,EAAE,gBAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,kBALX;AAMJT,QAAAA,GAAG,EAAE,wBAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,GAAG,EAAE,OAJA;AAKLC,QAAAA,EAAE,EAAE,cALC;AAMLC,QAAAA,EAAE,EAAE,cANC;AAOLC,QAAAA,EAAE,EAAE,cAPC;AAQLC,QAAAA,EAAE,EAAE,cARC;AASLC,QAAAA,EAAE,EAAE,cATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,0BADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,UAAU,EAAE,eAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,YADF;AAETC,QAAAA,OAAO,EAAE,iBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,kBAJG;AAKTC,QAAAA,MAAM,EAAE,eALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,eADH;AAELC,QAAAA,IAAI,EAAE,gBAFD;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,gBAJP;AAKLC,QAAAA,WAAW,EAAE,SALR;AAMLC,QAAAA,cAAc,EAAE,UANX;AAOLC,QAAAA,KAAK,EAAE,aAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,cADH;AAERC,QAAAA,KAAK,EAAE,MAFC;AAGRC,QAAAA,cAAc,EAAE,oBAHR;AAIRC,QAAAA,MAAM,EAAE,SAJA;AAKRC,QAAAA,mBAAmB,EAAE,wBALb;AAMRC,QAAAA,aAAa,EAAE;AANP,OAxGH;AAgHPzB,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAhHC;AA6IP0B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA7IF;AAiJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAjJN;AADiB,GAA5B;AAwJD,CAzJD,EAyJGC,MAzJH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-es-EU.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'es-EU': {\n      font: {\n        bold: 'Lodia',\n        italic: 'Etz<PERSON>',\n        underline: 'Azpimarrat<PERSON>',\n        clear: 'Estiloa kendu',\n        height: '<PERSON>rro altuera',\n        name: 'Tipogra<PERSON>',\n        strikethrough: '<PERSON><PERSON><PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Letren neurria',\n      },\n      image: {\n        image: 'I<PERSON><PERSON>',\n        insert: 'Irudi bat txertatu',\n        resizeFull: 'Jatorrizko neurrira aldatu',\n        resizeHalf: 'Neurria erdira aldatu',\n        resizeQuarter: 'Neurria laurdenera aldatu',\n        floatLeft: 'Ezkerrean kokatu',\n        floatRight: 'Eskuinean kokatu',\n        floatNone: 'Kokapenik ez ezarri',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'I<PERSON>i bat ezarri hemen',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Zure fitxategi bat aukeratu',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'Irudiaren URL helbidea',\n        remove: 'Remove Image',\n        original: 'Original',\n      },\n      video: {\n        video: 'Bideoa',\n        videoLink: 'Bideorako esteka',\n        insert: 'Bideo berri bat txertatu',\n        url: 'Bideoaren URL helbidea',\n        providers: '(YouTube, Vimeo, Vine, Instagram edo DailyMotion)',\n      },\n      link: {\n        link: 'Esteka',\n        insert: 'Esteka bat txertatu',\n        unlink: 'Esteka ezabatu',\n        edit: 'Editatu',\n        textToDisplay: 'Estekaren testua',\n        url: 'Estekaren URL helbidea',\n        openInNewWindow: 'Leiho berri batean ireki',\n      },\n      table: {\n        table: 'Taula',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Marra horizontala txertatu',\n      },\n      style: {\n        style: 'Estiloa',\n        p: 'p',\n        blockquote: 'Aipamena',\n        pre: 'Kodea',\n        h1: '1. izenburua',\n        h2: '2. izenburua',\n        h3: '3. izenburua',\n        h4: '4. izenburua',\n        h5: '5. izenburua',\n        h6: '6. izenburua',\n      },\n      lists: {\n        unordered: 'Ordenatu gabeko zerrenda',\n        ordered: 'Zerrenda ordenatua',\n      },\n      options: {\n        help: 'Laguntza',\n        fullscreen: 'Pantaila osoa',\n        codeview: 'Kodea ikusi',\n      },\n      paragraph: {\n        paragraph: 'Paragrafoa',\n        outdent: 'Koska txikiagoa',\n        indent: 'Koska handiagoa',\n        left: 'Ezkerrean kokatu',\n        center: 'Erdian kokatu',\n        right: 'Eskuinean kokatu',\n        justify: 'Justifikatu',\n      },\n      color: {\n        recent: 'Azken kolorea',\n        more: 'Kolore gehiago',\n        background: 'Atzeko planoa',\n        foreground: 'Aurreko planoa',\n        transparent: 'Gardena',\n        setTransparent: 'Gardendu',\n        reset: 'Lehengoratu',\n        resetToDefault: 'Berrezarri lehenetsia',\n      },\n      shortcut: {\n        shortcuts: 'Lasterbideak',\n        close: 'Itxi',\n        textFormatting: 'Testuaren formatua',\n        action: 'Ekintza',\n        paragraphFormatting: 'Paragrafoaren formatua',\n        documentStyle: 'Dokumentuaren estiloa',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Desegin',\n        redo: 'Berregin',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}