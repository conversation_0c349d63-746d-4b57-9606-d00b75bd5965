# Feather

[Feather](http://colebemis.com/feather) is a growing collection of beautifully simple icons by [@colebe<PERSON>](http://twitter.com/colebemis).

Each icon was carefully designed with usability and consistency in mind. The set is packaged in PSD, CSH, SVG and Webfont format.

## Installation

Using [component](https://github.com/component/component)

    $ component install puse/icons-feather

Using [npm](http://npmjs.org/) for [browserify](http://browserify.org/), [npm-css](https://www.npmjs.org/package/npm-css), [rework](https://github.com/reworkcss/rework), ..

    $ npm install puse-icons-feather

Manually

1. Copy the entire `fonts` directory into your project

2. Include `feather.css` in the `<head>` of your HTML document

```html
<link rel="stylsheet" href="path/to/feather.css">
```

## Usage

Insert the icon's class name into any HTML element or insert a `data-icon` attribute with the icon's HTML entity into any HTML element

```html
<span class="icon-eye"></span>
<span data-icon="&#xe000"></span>
```


## Changelog

**v1.0** - 11/27/2013

- initial release

**v1.1** - 4/27/2014

- added CSH, SVG and Webfont formats
- added 30 new icons
- changed all PSD icon layers to vector shapes

## License

Feather is released under the [MIT License](http://opensource.org/licenses/MIT). In short, you are free to use Feather in any personal, open-source or commercial work. Attribution is optional but appreciated.

## Support

If you have any questions, issues or suggestions for new icons, let me know: [<EMAIL>](mailto:<EMAIL>)
