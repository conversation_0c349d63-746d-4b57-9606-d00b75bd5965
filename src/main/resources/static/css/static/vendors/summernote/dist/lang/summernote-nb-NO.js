(function ($) {
  $.extend($.summernote.lang, {
    'nb-NO': {
      font: {
        bold: 'Fet',
        italic: 'Kursiv',
        underline: 'Understrek',
        clear: 'Fjern formatering',
        height: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        name: 'Skrifttype',
        strikethrough: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        size: 'Skriftstørrelse'
      },
      image: {
        image: 'Bilde',
        insert: 'Sett inn bilde',
        resizeFull: 'Sett full størrelse',
        resizeHalf: 'Sett halv størrelse',
        resizeQuarter: 'Sett kvart størrelse',
        floatLeft: 'Flyt til venstre',
        floatRight: 'Flyt til høyre',
        floatNone: 'Fjern flyt',
        dragImageHere: 'Dra et bilde hit',
        selectFromFiles: 'Velg fra filer',
        url: 'Bilde-URL',
        remove: 'Fjern bilde'
      },
      video: {
        video: 'Video',
        videoLink: 'Videolenke',
        insert: 'Sett inn video',
        url: 'Video-URL',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)'
      },
      link: {
        link: '<PERSON><PERSON>',
        insert: 'Sett inn lenke',
        unlink: '<PERSON>jern lenke',
        edit: 'Rediger',
        textToDisplay: 'Visningstekst',
        url: 'Til hvilken URL skal denne lenken peke?',
        openInNewWindow: 'Åpne i nytt vindu'
      },
      table: {
        table: 'Tabell'
      },
      hr: {
        insert: 'Sett inn horisontal linje'
      },
      style: {
        style: 'Stil',
        p: 'p',
        blockquote: 'Sitat',
        pre: 'Kode',
        h1: 'Overskrift 1',
        h2: 'Overskrift 2',
        h3: 'Overskrift 3',
        h4: 'Overskrift 4',
        h5: 'Overskrift 5',
        h6: 'Overskrift 6'
      },
      lists: {
        unordered: 'Punktliste',
        ordered: 'Nummerert liste'
      },
      options: {
        help: 'Hjelp',
        fullscreen: 'Fullskjerm',
        codeview: 'HTML-visning'
      },
      paragraph: {
        paragraph: 'Avsnitt',
        outdent: 'Tilbakerykk',
        indent: 'Innrykk',
        left: 'Venstrejustert',
        center: 'Midtstilt',
        right: 'Høyrejustert',
        justify: 'Blokkjustert'
      },
      color: {
        recent: 'Nylig valgt farge',
        more: 'Flere farger',
        background: 'Bakgrunnsfarge',
        foreground: 'Skriftfarge',
        transparent: 'Gjennomsiktig',
        setTransparent: 'Sett gjennomsiktig',
        reset: 'Nullstill',
        resetToDefault: 'Nullstill til standard'
      },
      shortcut: {
        shortcuts: 'Hurtigtaster',
        close: 'Lukk',
        textFormatting: 'Tekstformatering',
        action: 'Handling',
        paragraphFormatting: 'Avsnittsformatering',
        documentStyle: 'Dokumentstil'
      },
      history: {
        undo: 'Angre',
        redo: 'Gjør om'
      }
    }
  });
})(jQuery);
