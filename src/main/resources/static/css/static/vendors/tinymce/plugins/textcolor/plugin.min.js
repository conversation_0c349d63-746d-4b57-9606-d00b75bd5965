!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("5",tinymce.util.Tools.resolve),g("1",["5"],function(a){return a("tinymce.dom.DOMUtils")}),g("2",["5"],function(a){return a("tinymce.PluginManager")}),g("3",["5"],function(a){return a("tinymce.util.I18n")}),g("4",["5"],function(a){return a("tinymce.util.Tools")}),g("0",["1","2","3","4"],function(a,b,c,d){var e=c.translate;return b.add("textcolor",function(b){function c(a){var c;return b.dom.getParents(b.selection.getStart(),function(b){var d;(d=b.style["forecolor"==a?"color":"background-color"])&&(c=d)}),c}function f(a){var c,d,e=[];for(d=["000000","Black","993300","Burnt orange","333300","Dark olive","003300","Dark green","003366","Dark azure","000080","Navy Blue","333399","Indigo","333333","Very dark gray","800000","Maroon","FF6600","Orange","808000","Olive","008000","Green","008080","Teal","0000FF","Blue","666699","Grayish blue","808080","Gray","FF0000","Red","FF9900","Amber","99CC00","Yellow green","339966","Sea green","33CCCC","Turquoise","3366FF","Royal blue","800080","Purple","999999","Medium gray","FF00FF","Magenta","FFCC00","Gold","FFFF00","Yellow","00FF00","Lime","00FFFF","Aqua","00CCFF","Sky blue","993366","Red violet","FFFFFF","White","FF99CC","Pink","FFCC99","Peach","FFFF99","Light yellow","CCFFCC","Pale green","CCFFFF","Pale cyan","99CCFF","Light sky blue","CC99FF","Plum"],d=b.settings.textcolor_map||d,d=b.settings[a+"_map"]||d,c=0;c<d.length;c+=2)e.push({text:d[c+1],color:"#"+d[c]});return e}function g(){function a(a,b){var c="transparent"==a;return'<td class="mce-grid-cell'+(c?" mce-colorbtn-trans":"")+'"><div id="'+p+"-"+q++ +'" data-mce-color="'+(a?a:"")+'" role="option" tabIndex="-1" style="'+(a?"background-color: "+a:"")+'" title="'+e(b)+'">'+(c?"&#215;":"")+"</div></td>"}var c,d,g,h,i,j,k,n,o=this,p=o._id,q=0;for(n=o.settings.origin,c=f(n),c.push({text:e("No color"),color:"transparent"}),g='<table class="mce-grid mce-grid-border mce-colorbutton-grid" role="list" cellspacing="0"><tbody>',h=c.length-1,j=0;j<m[n];j++){for(g+="<tr>",i=0;i<l[n];i++)k=j*l[n]+i,k>h?g+="<td></td>":(d=c[k],g+=a(d.color,d.text));g+="</tr>"}if(b.settings.color_picker_callback){for(g+='<tr><td colspan="'+l[n]+'" class="mce-custom-color-btn"><div id="'+p+'-c" class="mce-widget mce-btn mce-btn-small mce-btn-flat" role="button" tabindex="-1" aria-labelledby="'+p+'-c" style="width: 100%"><button type="button" role="presentation" tabindex="-1">'+e("Custom...")+"</button></div></td></tr>",g+="<tr>",i=0;i<l[n];i++)g+=a("","Custom color");g+="</tr>"}return g+="</tbody></table>"}function h(a,c){b.undoManager.transact(function(){b.focus(),b.formatter.apply(a,{value:c}),b.nodeChanged()})}function i(a){b.undoManager.transact(function(){b.focus(),b.formatter.remove(a,{value:null},null,!0),b.nodeChanged()})}function j(e){function f(a){n.hidePanel(),n.color(a),h(n.settings.format,a)}function g(){n.hidePanel(),n.resetColor(),i(n.settings.format)}function j(a,b){a.style.background=b,a.setAttribute("data-mce-color",b)}var k,m,n=this.parent();m=n.settings.origin,a.DOM.getParent(e.target,".mce-custom-color-btn")&&(n.hidePanel(),b.settings.color_picker_callback.call(b,function(a){var b,c,e,g=n.panel.getEl().getElementsByTagName("table")[0];for(b=d.map(g.rows[g.rows.length-1].childNodes,function(a){return a.firstChild}),e=0;e<b.length&&(c=b[e],c.getAttribute("data-mce-color"));e++);if(e==l[m])for(e=0;e<l[m]-1;e++)j(b[e],b[e+1].getAttribute("data-mce-color"));j(c,a),f(a)},c(n.settings.format))),k=e.target.getAttribute("data-mce-color"),k?(this.lastId&&document.getElementById(this.lastId).setAttribute("aria-selected",!1),e.target.setAttribute("aria-selected",!0),this.lastId=e.target.id,"transparent"==k?g():f(k)):null!==k&&n.hidePanel()}function k(){var a=this;a._color?h(a.settings.format,a._color):i(a.settings.format)}var l,m;m={forecolor:b.settings.forecolor_rows||b.settings.textcolor_rows||5,backcolor:b.settings.backcolor_rows||b.settings.textcolor_rows||5},l={forecolor:b.settings.forecolor_cols||b.settings.textcolor_cols||8,backcolor:b.settings.backcolor_cols||b.settings.textcolor_cols||8},b.addButton("forecolor",{type:"colorbutton",tooltip:"Text color",format:"forecolor",panel:{origin:"forecolor",role:"application",ariaRemember:!0,html:g,onclick:j},onclick:k}),b.addButton("backcolor",{type:"colorbutton",tooltip:"Background color",format:"hilitecolor",panel:{origin:"backcolor",role:"application",ariaRemember:!0,html:g,onclick:j},onclick:k})}),function(){}}),d("0")()}();