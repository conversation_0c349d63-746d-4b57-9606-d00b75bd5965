("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.core",["jquery"],function(){return function(t,e,i){var n,s=e.kendo=e.kendo||{cultures:{}},r=t.extend,a=t.each,o=t.isArray,l=t.proxy,h=t.noop,u=Math,c=e.JSON||{},d={},p=/%/,f=/\{(\d+)(:[^\}]+)?\}/g,m=/(\d+(?:\.?)\d*)px\s*(\d+(?:\.?)\d*)px\s*(\d+(?:\.?)\d*)px\s*(\d+)?/i,g=/^(\+|-?)\d+(\.?)\d*$/,_="function",v="string",w="number",b="object",y="null",k="boolean",x="undefined",C={},T={},S=[].slice;function I(){}s.version="2018.2.620".replace(/^\s+|\s+$/g,""),I.extend=function(t){var e,i,n=function(){},s=this,a=t&&t.init?t.init:function(){s.apply(this,arguments)};n.prototype=s.prototype,i=a.fn=a.prototype=new n;for(e in t)null!=t[e]&&t[e].constructor===Object?i[e]=r(!0,{},n.prototype[e],t[e]):i[e]=t[e];return i.constructor=a,a.extend=s.extend,a},I.prototype._initOptions=function(t){this.options=N({},this.options,t)};var D=s.isFunction=function(t){return"function"==typeof t},F=function(){this._defaultPrevented=!0},E=function(){return!0===this._defaultPrevented},O=I.extend({init:function(){this._events={}},bind:function(t,e,n){var s,r,a,o,l=this,h=typeof t===v?[t]:t,u=typeof e===_;if(e===i){for(s in t)l.bind(s,t[s]);return l}for(s=0,r=h.length;s<r;s++)t=h[s],(o=u?e:e[t])&&(n&&(a=o,(o=function(){l.unbind(t,o),a.apply(l,arguments)}).original=a),(l._events[t]=l._events[t]||[]).push(o));return l},one:function(t,e){return this.bind(t,e,!0)},first:function(t,e){var i,n,s,r=typeof t===v?[t]:t,a=typeof e===_;for(i=0,n=r.length;i<n;i++)t=r[i],(s=a?e:e[t])&&(this._events[t]=this._events[t]||[]).unshift(s);return this},trigger:function(t,e){var i,n,s=this._events[t];if(s){for((e=e||{}).sender=this,e._defaultPrevented=!1,e.preventDefault=F,e.isDefaultPrevented=E,i=0,n=(s=s.slice()).length;i<n;i++)s[i].call(this,e);return!0===e._defaultPrevented}return!1},unbind:function(t,e){var n,s=this._events[t];if(t===i)this._events={};else if(s)if(e)for(n=s.length-1;n>=0;n--)s[n]!==e&&s[n].original!==e||s.splice(n,1);else this._events[t]=[];return this}});function A(t,e){if(e)return"'"+t.split("'").join("\\'").split('\\"').join('\\\\\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t")+"'";var i=t.charAt(0),n=t.substring(1);return"="===i?"+("+n+")+":":"===i?"+$kendoHtmlEncode("+n+")+":";"+t+";$kendoOutput+="}var M=/^\w+/,H=/\$\{([^}]*)\}/g,P=/\\\}/g,z=/__CURLY__/g,V=/\\#/g,R=/__SHARP__/g,B=["","0","00","000","0000"];function L(t,e,i){return t+="",(i=(e=e||2)-t.length)?B[e].substring(0,i)+t:t}function N(t){var e=1,i=arguments.length;for(e=1;e<i;e++)W(t,arguments[e]);return t}function W(t,e){var i,n,r,a,o,l=s.data.ObservableArray,h=s.data.LazyObservableArray,u=s.data.DataSource,c=s.data.HierarchicalDataSource;for(i in e)(a=(r=typeof(n=e[i]))===b&&null!==n?n.constructor:null)&&a!==Array&&a!==l&&a!==h&&a!==u&&a!==c&&a!==RegExp?n instanceof Date?t[i]=new Date(n.getTime()):D(n.clone)?t[i]=n.clone():(o=t[i],t[i]=typeof o===b?o||{}:{},W(t[i],n)):r!==x&&(t[i]=n);return t}function U(t,e,n){for(var s in e)if(e.hasOwnProperty(s)&&e[s].test(t))return s;return n!==i?n:t}function q(t){return t.replace(/\-(\w)/g,function(t,e){return e.toUpperCase()})}function j(e,i){var n,r={};return document.defaultView&&document.defaultView.getComputedStyle?(n=document.defaultView.getComputedStyle(e,""),i&&t.each(i,function(t,e){r[e]=n.getPropertyValue(e)})):(n=e.currentStyle,i&&t.each(i,function(t,e){r[e]=n[q(e)]})),s.size(r)||(r=n),r}n={paramName:"data",useWithBlock:!0,render:function(t,e){var i,n,s="";for(i=0,n=e.length;i<n;i++)s+=t(e[i]);return s},compile:function(t,e){var i,n,a,o=r({},this,e),l=o.paramName,h=l.match(M)[0],u=o.useWithBlock,c="var $kendoOutput, $kendoHtmlEncode = kendo.htmlEncode;";if(D(t))return t;for(c+=u?"with("+l+"){":"",c+="$kendoOutput=",n=t.replace(P,"__CURLY__").replace(H,"#=$kendoHtmlEncode($1)#").replace(z,"}").replace(V,"__SHARP__").split("#"),a=0;a<n.length;a++)c+=A(n[a],a%2==0);c+=u?";}":";",c=(c+="return $kendoOutput;").replace(R,"#");try{return(i=new Function(h,c))._slotCount=Math.floor(n.length/2),i}catch(e){throw new Error(s.format("Invalid template:'{0}' Generated code:'{1}'",t,c))}}},function(){var t,e,i,n=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,s={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},r={}.toString;function a(t){return n.lastIndex=0,n.test(t)?'"'+t.replace(n,function(t){var e=s[t];return typeof e===v?e:"\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+t+'"'}typeof Date.prototype.toJSON!==_&&(Date.prototype.toJSON=function(){return isFinite(this.valueOf())?L(this.getUTCFullYear(),4)+"-"+L(this.getUTCMonth()+1)+"-"+L(this.getUTCDate())+"T"+L(this.getUTCHours())+":"+L(this.getUTCMinutes())+":"+L(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(){return this.valueOf()}),typeof c.stringify!==_&&(c.stringify=function(n,s,o){var l;if(t="",e="",typeof o===w)for(l=0;l<o;l+=1)e+=" ";else typeof o===v&&(e=o);if(i=s,s&&typeof s!==_&&(typeof s!==b||typeof s.length!==w))throw new Error("JSON.stringify");return function n(s,o){var l,h,u,c,d,p,f=t,m=o[s];if(m&&typeof m===b&&typeof m.toJSON===_&&(m=m.toJSON(s)),typeof i===_&&(m=i.call(o,s,m)),(p=typeof m)===v)return a(m);if(p===w)return isFinite(m)?String(m):y;if(p===k||p===y)return String(m);if(p===b){if(!m)return y;if(t+=e,d=[],"[object Array]"===r.apply(m)){for(c=m.length,l=0;l<c;l++)d[l]=n(l,m)||y;return u=0===d.length?"[]":t?"[\n"+t+d.join(",\n"+t)+"\n"+f+"]":"["+d.join(",")+"]",t=f,u}if(i&&typeof i===b)for(c=i.length,l=0;l<c;l++)typeof i[l]===v&&(u=n(h=i[l],m))&&d.push(a(h)+(t?": ":":")+u);else for(h in m)Object.hasOwnProperty.call(m,h)&&(u=n(h,m))&&d.push(a(h)+(t?": ":":")+u);return u=0===d.length?"{}":t?"{\n"+t+d.join(",\n"+t)+"\n"+f+"}":"{"+d.join(",")+"}",t=f,u}}("",{"":n})})}(),function(){var e=/dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|"[^"]*"|'[^']*'/g,n=/^(n|c|p|e)(\d*)$/i,r=/(\\.)|(['][^']*[']?)|(["][^"]*["]?)/g,a=/\,/g,o="",l=".",h=",",c="#",d="0",p="??",m={}.toString;function g(t){if(t){if(t.numberFormat)return t;if(typeof t===v){var e=s.cultures;return e[t]||e[t.split("-")[0]]||null}return null}return null}function _(t){return t&&(t=g(t)),t||s.cultures.current}s.cultures["en-US"]={name:"en-US",numberFormat:{pattern:["-n"],decimals:2,",":",",".":".",groupSize:[3],percent:{pattern:["-n %","n %"],decimals:2,",":",",".":".",groupSize:[3],symbol:"%"},currency:{name:"US Dollar",abbr:"USD",pattern:["($n)","$n"],decimals:2,",":",",".":".",groupSize:[3],symbol:"$"}},calendars:{standard:{days:{names:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],namesAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],namesShort:["Su","Mo","Tu","We","Th","Fr","Sa"]},months:{names:["January","February","March","April","May","June","July","August","September","October","November","December"],namesAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]},AM:["AM","am","AM"],PM:["PM","pm","PM"],patterns:{d:"M/d/yyyy",D:"dddd, MMMM dd, yyyy",F:"dddd, MMMM dd, yyyy h:mm:ss tt",g:"M/d/yyyy h:mm tt",G:"M/d/yyyy h:mm:ss tt",m:"MMMM dd",M:"MMMM dd",s:"yyyy'-'MM'-'ddTHH':'mm':'ss",t:"h:mm tt",T:"h:mm:ss tt",u:"yyyy'-'MM'-'dd HH':'mm':'ss'Z'",y:"MMMM, yyyy",Y:"MMMM, yyyy"},"/":"/",":":":",firstDay:0,twoDigitYearMax:2029}}},s.culture=function(t){var e,n=s.cultures;if(t===i)return n.current;(e=g(t)||n["en-US"]).calendar=e.calendars.standard,n.current=e},s.findCulture=g,s.getCulture=_,s.culture("en-US");var b=function(t,e,n,s){var r,a,o,u,c,d,p=t.indexOf(s[l]),f=s.groupSize.slice(),m=f.shift();if(n=-1!==p?p:n+1,(a=(r=t.substring(e,n)).length)>=m){for(o=a,u=[];o>-1;)if((c=r.substring(o-m,o))&&u.push(c),o-=m,0===(m=(d=f.shift())!==i?d:m)){o>0&&u.push(r.substring(0,o));break}r=u.reverse().join(s[h]),t=t.substring(0,e)+r+t.substring(n)}return t},y=function(t,e,i){return e=e||0,t=t.toString().split("e"),t=Math.round(+(t[0]+"e"+(t[1]?+t[1]+e:e))),i&&(t=-t),(t=+((t=t.toString().split("e"))[0]+"e"+(t[1]?+t[1]-e:-e))).toFixed(Math.min(e,20))},k=function(t,s,f){if(s){if("[object Date]"===m.call(t))return g=t,v=s,k=_(f).calendars.standard,x=k.days,C=k.months,(v=k.patterns[v]||v).replace(e,function(t){var e,n,s;return"d"===t?n=g.getDate():"dd"===t?n=L(g.getDate()):"ddd"===t?n=x.namesAbbr[g.getDay()]:"dddd"===t?n=x.names[g.getDay()]:"M"===t?n=g.getMonth()+1:"MM"===t?n=L(g.getMonth()+1):"MMM"===t?n=C.namesAbbr[g.getMonth()]:"MMMM"===t?n=C.names[g.getMonth()]:"yy"===t?n=L(g.getFullYear()%100):"yyyy"===t?n=L(g.getFullYear(),4):"h"===t?n=g.getHours()%12||12:"hh"===t?n=L(g.getHours()%12||12):"H"===t?n=g.getHours():"HH"===t?n=L(g.getHours()):"m"===t?n=g.getMinutes():"mm"===t?n=L(g.getMinutes()):"s"===t?n=g.getSeconds():"ss"===t?n=L(g.getSeconds()):"f"===t?n=u.floor(g.getMilliseconds()/100):"ff"===t?((n=g.getMilliseconds())>99&&(n=u.floor(n/10)),n=L(n)):"fff"===t?n=L(g.getMilliseconds(),3):"tt"===t?n=g.getHours()<12?k.AM[0]:k.PM[0]:"zzz"===t?(s=(e=g.getTimezoneOffset())<0,n=u.abs(e/60).toString().split(".")[0],e=u.abs(e)-60*n,n=(s?"+":"-")+L(n),n+=":"+L(e)):"zz"!==t&&"z"!==t||(s=(n=g.getTimezoneOffset()/60)<0,n=u.abs(n).toString().split(".")[0],n=(s?"+":"-")+("zz"===t?L(n):n)),n!==i?n:t.slice(1,t.length-1)});if(typeof t===w)return function(t,e,s){var u,f,m,g,v,w,k,x,C,T,S,I,D,F,E,O,A,M,H,P,z,V=(s=_(s)).numberFormat,R=V[l],B=V.decimals,L=V.pattern[0],N=[],W=t<0,U=o,q=o,j=-1;if(t===i)return o;if(!isFinite(t))return t;if(!e)return s.name.length?t.toLocaleString():t.toString();if(v=n.exec(e)){if(m="p"===(e=v[1].toLowerCase()),((f="c"===e)||m)&&(R=(V=f?V.currency:V.percent)[l],B=V.decimals,u=V.symbol,L=V.pattern[W?0:1]),(g=v[2])&&(B=+g),"e"===e)return g?t.toExponential(B):t.toExponential();if(m&&(t*=100),W=(t=y(t,B))<0,w=(t=t.split(l))[0],k=t[1],W&&(w=w.substring(1)),q=b(w,0,w.length,V),k&&(q+=R+k),"n"===e&&!W)return q;for(t=o,C=0,T=L.length;C<T;C++)t+="n"===(S=L.charAt(C))?q:"$"===S||"%"===S?u:S;return t}if((e.indexOf("'")>-1||e.indexOf('"')>-1||e.indexOf("\\")>-1)&&(e=e.replace(r,function(t){var e=t.charAt(0).replace("\\",""),i=t.slice(1).replace(e,"");return N.push(i),p})),e=e.split(";"),W&&e[1])e=e[1],D=!0;else if(0===t){if(-1==(e=e[2]||e[0]).indexOf(c)&&-1==e.indexOf(d))return e}else e=e[0];if(H=e.indexOf("%"),f=-1!=(P=e.indexOf("$")),(m=-1!=H)&&(t*=100),f&&"\\"===e[P-1]&&(e=e.split("\\").join(""),f=!1),(f||m)&&(R=(V=f?V.currency:V.percent)[l],B=V.decimals,u=V.symbol),(I=e.indexOf(h)>-1)&&(e=e.replace(a,o)),F=e.indexOf(l),T=e.length,-1!=F&&(k=(k=(k=t.toString().split("e"))[1]?y(t,Math.abs(k[1])):k[0]).split(l)[1]||o,A=(O=e.lastIndexOf(d)-F)>-1,M=(E=e.lastIndexOf(c)-F)>-1,C=k.length,A||M||(T=(e=e.substring(0,F)+e.substring(F+1)).length,F=-1,C=0),A&&O>E?C=O:E>O&&(M&&C>E?C=E:A&&C<O&&(C=O))),t=y(t,C,W),E=e.indexOf(c),O=e.indexOf(d),j=-1==E&&-1!=O?O:-1!=E&&-1==O?E:E>O?O:E,E=e.lastIndexOf(c),O=e.lastIndexOf(d),z=-1==E&&-1!=O?O:-1!=E&&-1==O?E:E>O?E:O,j==T&&(z=j),-1!=j){for(w=(q=t.toString().split(l))[0],k=q[1]||o,x=w.length,k.length,W&&-1*t>=0&&(W=!1),t=e.substring(0,j),W&&!D&&(t+="-"),C=j;C<T;C++){if(S=e.charAt(C),-1==F){if(z-C<x){t+=w;break}}else if(-1!=O&&O<C&&(U=o),F-C<=x&&F-C>-1&&(t+=w,C=F),F===C){t+=(k?R:o)+k,C+=z-F+1;continue}S===d?(t+=S,U=S):S===c&&(t+=U)}if(I&&(t=b(t,j+(W&&!D?1:0),Math.max(z,x+j),V)),z>=j&&(t+=e.substring(z+1)),f||m){for(q=o,C=0,T=t.length;C<T;C++)q+="$"===(S=t.charAt(C))||"%"===S?u:S;t=q}if(T=N.length)for(C=0;C<T;C++)t=t.replace(p,N[C])}return t}(t,s,f)}var g,v,k,x,C;return t!==i?t:""};s.format=function(t){var e=arguments;return t.replace(f,function(t,i,n){var s=e[parseInt(i,10)+1];return k(s,n?n.substring(1):"")})},s._extractFormat=function(t){return"{0:"===t.slice(0,3)&&(t=t.slice(3,t.length-1)),t},s._activeElement=function(){try{return document.activeElement}catch(t){return document.documentElement.activeElement}},s._round=y,s._outerWidth=function(e,i){return t(e).outerWidth(i||!1)||0},s._outerHeight=function(e,i){return t(e).outerHeight(i||!1)||0},s.toString=k}(),function(){var e=/\u00A0/g,n=/[eE][\-+]?[0-9]+/,r=/[+|\-]\d{1,2}/,a=/[+|\-]\d{1,2}:?\d{2}/,l=/^\/Date\((.*?)\)\/$/,h=/[+-]\d*/,c=[[],["G","g","F"],["D","d","y","m","T","t"]],d=[["yyyy-MM-ddTHH:mm:ss.fffffffzzz","yyyy-MM-ddTHH:mm:ss.fffffff","yyyy-MM-ddTHH:mm:ss.fffzzz","yyyy-MM-ddTHH:mm:ss.fff","ddd MMM dd yyyy HH:mm:ss","yyyy-MM-ddTHH:mm:sszzz","yyyy-MM-ddTHH:mmzzz","yyyy-MM-ddTHH:mmzz","yyyy-MM-ddTHH:mm:ss","yyyy-MM-dd HH:mm:ss","yyyy/MM/dd HH:mm:ss"],["yyyy-MM-ddTHH:mm","yyyy-MM-dd HH:mm","yyyy/MM/dd HH:mm"],["yyyy/MM/dd","yyyy-MM-dd","HH:mm:ss","HH:mm"]],p={2:/^\d{1,2}/,3:/^\d{1,3}/,4:/^\d{4}/},f={}.toString;function m(t,e,i){return!(t>=e&&t<=i)}function g(t){return t.charAt(0)}function _(e){return t.map(e,g)}function v(t){for(var e=0,i=t.length,n=[];e<i;e++)n[e]=(t[e]+"").toLowerCase();return n}function b(t){var e,i={};for(e in t)i[e]=v(t[e]);return i}function y(t,e,n,o){if(!t)return null;var l,h,u,c,d,f,g,v,w,y,k,x,C,T=function(t){for(var i=0;e[V]===t;)i++,V++;return i>0&&(V-=1),i},S=function(e){var i=p[e]||new RegExp("^\\d{1,"+e+"}"),n=t.substr(R,e).match(i);return n?(n=n[0],R+=n.length,parseInt(n,10)):null},I=function(e,i){for(var n,s,r,a=0,o=e.length,l=0,h=0;a<o;a++)s=(n=e[a]).length,r=t.substr(R,s),i&&(r=r.toLowerCase()),r==n&&s>l&&(l=s,h=a);return l?(R+=l,h+1):null},D=function(){var i=!1;return t.charAt(R)===e[V]&&(R++,i=!0),i},F=n.calendars.standard,E=null,O=null,A=null,M=null,H=null,P=null,z=null,V=0,R=0,B=!1,L=new Date,N=F.twoDigitYearMax||2029,W=L.getFullYear();for(e||(e="d"),(c=F.patterns[e])&&(e=c),u=(e=e.split("")).length;V<u;V++)if(l=e[V],B)"'"===l?B=!1:D();else if("d"===l){if(h=T("d"),F._lowerDays||(F._lowerDays=b(F.days)),null!==A&&h>2)continue;if(null===(A=h<3?S(2):I(F._lowerDays[3==h?"namesAbbr":"names"],!0))||m(A,1,31))return null}else if("M"===l){if(h=T("M"),F._lowerMonths||(F._lowerMonths=b(F.months)),null===(O=h<3?S(2):I(F._lowerMonths[3==h?"namesAbbr":"names"],!0))||m(O,1,12))return null;O-=1}else if("y"===l){if(null===(E=S(h=T("y"))))return null;2==h&&("string"==typeof N&&(N=W+parseInt(N,10)),(E=W-W%100+E)>N&&(E-=100))}else if("h"===l){if(T("h"),12==(M=S(2))&&(M=0),null===M||m(M,0,11))return null}else if("H"===l){if(T("H"),null===(M=S(2))||m(M,0,23))return null}else if("m"===l){if(T("m"),null===(H=S(2))||m(H,0,59))return null}else if("s"===l){if(T("s"),null===(P=S(2))||m(P,0,59))return null}else if("f"===l){if(h=T("f"),x=t.substr(R,h).match(p[3]),null!==(z=S(h))&&(z=parseFloat("0."+x[0],10),z=s._round(z,3),z*=1e3),null===z||m(z,0,999))return null}else if("t"===l){if(h=T("t"),v=F.AM,w=F.PM,1===h&&(v=_(v),w=_(w)),!(d=I(w))&&!I(v))return null}else if("z"===l){if(f=!0,h=T("z"),"Z"===t.substr(R,1)){D();continue}if(!(g=t.substr(R,6).match(h>2?a:r)))return null;if(y=(g=g[0].split(":"))[0],!(k=g[1])&&y.length>3&&(R=y.length-2,k=y.substring(R),y=y.substring(0,R)),m(y=parseInt(y,10),-12,13))return null;if(h>2&&(k=g[0][0]+k,k=parseInt(k,10),isNaN(k)||m(k,-59,59)))return null}else if("'"===l)B=!0,D();else if(!D())return null;return o&&!/^\s*$/.test(t.substr(R))?null:(null!==E||null!==O||null!==A||null===M&&null===H&&!P?(null===E&&(E=W),null===A&&(A=1)):(E=W,O=L.getMonth(),A=L.getDate()),d&&M<12&&(M+=12),f?(y&&(M+=-y),k&&(H+=-k),t=new Date(Date.UTC(E,O,A,M,H,P,z))):(t=new Date(E,O,A,M,H,P,z),C=t,M||23!==C.getHours()||C.setHours(C.getHours()+2)),E<100&&t.setFullYear(E),t.getDate()!==A&&f===i?null:t)}function k(t,e,i,n){if("[object Date]"===f.call(t))return t;var r,a,p,m,g=0,_=null;if(t&&0===t.indexOf("/D")&&(_=l.exec(t)))return _=_[1],a=h.exec(_.substring(1)),_=new Date(parseInt(_,10)),a&&(p=a[0],m="-"===p.substr(0,1)?-1:1,p=p.substring(1),a=m*(p=60*parseInt(p.substr(0,2),10)+parseInt(p.substring(2),10)),_=s.timezone.apply(_,0),_=s.timezone.convert(_,0,-1*a)),_;for(i=s.getCulture(i),e||(e=function(t){var e,i,n,s=u.max(c.length,d.length),r=t.calendar.patterns,a=[];for(n=0;n<s;n++){for(e=c[n],i=0;i<e.length;i++)a.push(r[e[i]]);a=a.concat(d[n])}return a}(i)),r=(e=o(e)?e:[e]).length;g<r;g++)if(_=y(t,e[g],i,n))return _;return _}s.parseDate=function(t,e,i){return k(t,e,i,!1)},s.parseExactDate=function(t,e,i){return k(t,e,i,!0)},s.parseInt=function(t,e){var i=s.parseFloat(t,e);return i&&(i|=0),i},s.parseFloat=function(t,i,r){if(!t&&0!==t)return null;if(typeof t===w)return t;t=t.toString();var a,o,l=(i=s.getCulture(i)).numberFormat,h=l.percent,u=l.currency,c=u.symbol,d=h.symbol,p=t.indexOf("-");return n.test(t)?(t=parseFloat(t.replace(l["."],".")),isNaN(t)&&(t=null),t):p>0?null:(p=p>-1,t.indexOf(c)>-1||r&&r.toLowerCase().indexOf("c")>-1?(a=(l=u).pattern[0].replace("$",c).split("n"),t.indexOf(a[0])>-1&&t.indexOf(a[1])>-1&&(t=t.replace(a[0],"").replace(a[1],""),p=!0)):t.indexOf(d)>-1&&(o=!0,l=h,c=d),t=t.replace("-","").replace(c,"").replace(e," ").split(l[","].replace(e," ")).join("").replace(l["."],"."),t=parseFloat(t),isNaN(t)?t=null:p&&(t*=-1),t&&o&&(t/=100),t)}}(),function(){d._scrollbar=i,d.scrollbar=function(t){if(isNaN(d._scrollbar)||t){var e,i=document.createElement("div");return i.style.cssText="overflow:scroll;overflow-x:hidden;zoom:1;clear:both;display:block",i.innerHTML="&nbsp;",document.body.appendChild(i),d._scrollbar=e=i.offsetWidth-i.scrollWidth,document.body.removeChild(i),e}return d._scrollbar},d.isRtl=function(e){return t(e).closest(".k-rtl").length>0};var n=document.createElement("table");try{n.innerHTML="<tr><td></td></tr>",d.tbodyInnerHtml=!0}catch(t){d.tbodyInnerHtml=!1}d.touch="ontouchstart"in e;var s=document.documentElement.style,r=d.transitions=!1,o=d.transforms=!1,l="HTMLElement"in e?HTMLElement.prototype:[];d.hasHW3D="WebKitCSSMatrix"in e&&"m11"in new e.WebKitCSSMatrix||"MozPerspective"in s||"msPerspective"in s,d.cssFlexbox="flexWrap"in s||"WebkitFlexWrap"in s||"msFlexWrap"in s,a(["Moz","webkit","O","ms"],function(){var t=this.toString(),e=typeof n.style[t+"Transition"]===v;if(e||typeof n.style[t+"Transform"]===v){var i=t.toLowerCase();return o={css:"ms"!=i?"-"+i+"-":"",prefix:t,event:"o"===i||"webkit"===i?i:""},e&&((r=o).event=r.event?r.event+"TransitionEnd":"transitionend"),!1}}),n=null,d.transforms=o,d.transitions=r,d.devicePixelRatio=e.devicePixelRatio===i?1:e.devicePixelRatio;try{d.screenWidth=e.outerWidth||e.screen?e.screen.availWidth:e.innerWidth,d.screenHeight=e.outerHeight||e.screen?e.screen.availHeight:e.innerHeight}catch(t){d.screenWidth=e.screen.availWidth,d.screenHeight=e.screen.availHeight}d.detectOS=function(t){var i,n=!1,s=[],r=!/mobile safari/i.test(t),a={wp:/(Windows Phone(?: OS)?)\s(\d+)\.(\d+(\.\d+)?)/,fire:/(Silk)\/(\d+)\.(\d+(\.\d+)?)/,android:/(Android|Android.*(?:Opera|Firefox).*?\/)\s*(\d+)\.(\d+(\.\d+)?)/,iphone:/(iPhone|iPod).*OS\s+(\d+)[\._]([\d\._]+)/,ipad:/(iPad).*OS\s+(\d+)[\._]([\d_]+)/,meego:/(MeeGo).+NokiaBrowser\/(\d+)\.([\d\._]+)/,webos:/(webOS)\/(\d+)\.(\d+(\.\d+)?)/,blackberry:/(BlackBerry|BB10).*?Version\/(\d+)\.(\d+(\.\d+)?)/,playbook:/(PlayBook).*?Tablet\s*OS\s*(\d+)\.(\d+(\.\d+)?)/,windows:/(MSIE)\s+(\d+)\.(\d+(\.\d+)?)/,tizen:/(tizen).*?Version\/(\d+)\.(\d+(\.\d+)?)/i,sailfish:/(sailfish).*rv:(\d+)\.(\d+(\.\d+)?).*firefox/i,ffos:/(Mobile).*rv:(\d+)\.(\d+(\.\d+)?).*Firefox/},o={ios:/^i(phone|pad|pod)$/i,android:/^android|fire$/i,blackberry:/^blackberry|playbook/i,windows:/windows/,wp:/wp/,flat:/sailfish|ffos|tizen/i,meego:/meego/},l={tablet:/playbook|ipad|fire/i},h={omini:/Opera\sMini/i,omobile:/Opera\sMobi/i,firefox:/Firefox|Fennec/i,mobilesafari:/version\/.*safari/i,ie:/MSIE|Windows\sPhone/i,chrome:/chrome|crios/i,webkit:/webkit/i};for(var u in a)if(a.hasOwnProperty(u)&&(s=t.match(a[u]))){if("windows"==u&&"plugins"in navigator)return!1;(n={}).device=u,n.tablet=U(u,l,!1),n.browser=U(t,h,"default"),n.name=U(u,o),n[n.name]=!0,n.majorVersion=s[2],n.minorVersion=s[3].replace("_","."),i=n.minorVersion.replace(".","").substr(0,2),n.flatVersion=n.majorVersion+i+new Array(3-(i.length<3?i.length:2)).join("0"),n.cordova=typeof e.PhoneGap!==x||typeof e.cordova!==x,n.appMode=e.navigator.standalone||/file|local|wmapp/.test(e.location.protocol)||n.cordova,n.android&&(d.devicePixelRatio<1.5&&n.flatVersion<400||r)&&(d.screenWidth>800||d.screenHeight>800)&&(n.tablet=u);break}return n};var h,u,c,p,f=d.mobileOS=d.detectOS(navigator.userAgent);d.wpDevicePixelRatio=f.wp?screen.width/320:0,d.hasNativeScrolling=!1,(f.ios||f.android&&f.majorVersion>2||f.wp)&&(d.hasNativeScrolling=f),d.delayedClick=function(){if(d.touch){if(f.ios)return!0;if(f.android)return!d.browser.chrome||!(d.browser.version<32)&&!(t("meta[name=viewport]").attr("content")||"").match(/user-scalable=no/i)}return!1},d.mouseAndTouchPresent=d.touch&&!(d.mobileOS.ios||d.mobileOS.android),d.detectBrowser=function(t){var e=!1,i=[],n={edge:/(edge)[ \/]([\w.]+)/i,webkit:/(chrome|crios)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,msie:/(msie\s|trident.*? rv:)([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var s in n)if(n.hasOwnProperty(s)&&(i=t.match(n[s]))){(e={})[s]=!0,e[i[1].toLowerCase().split(" ")[0].split("/")[0]]=!0,e.version=parseInt(document.documentMode||i[2],10);break}return e},d.browser=d.detectBrowser(navigator.userAgent),d.detectClipboardAccess=function(){var t={copy:!!document.queryCommandSupported&&document.queryCommandSupported("copy"),cut:!!document.queryCommandSupported&&document.queryCommandSupported("cut"),paste:!!document.queryCommandSupported&&document.queryCommandSupported("paste")};return d.browser.chrome&&(t.paste=!1,d.browser.version>=43&&(t.copy=!0,t.cut=!0)),t},d.clipboard=d.detectClipboardAccess(),d.zoomLevel=function(){try{var t=d.browser,i=0,n=document.documentElement;return t.msie&&11==t.version&&n.scrollHeight>n.clientHeight&&!d.touch&&(i=d.scrollbar()),d.touch?n.clientWidth/e.innerWidth:t.msie&&t.version>=10?((top||e).document.documentElement.offsetWidth+i)/(top||e).innerWidth:1}catch(t){return 1}},d.cssBorderSpacing=void 0!==s.borderSpacing&&!(d.browser.msie&&d.browser.version<8),h=d.browser,u="",c=t(document.documentElement),p=parseInt(h.version,10),h.msie?u="ie":h.mozilla?u="ff":h.safari?u="safari":h.webkit?u="webkit":h.opera?u="opera":h.edge&&(u="edge"),u&&(u="k-"+u+" k-"+u+p),d.mobileOS&&(u+=" k-mobile"),d.cssFlexbox||(u+=" k-no-flexbox"),c.addClass(u),d.eventCapture=document.documentElement.addEventListener;var m=document.createElement("input");d.placeholder="placeholder"in m,d.propertyChangeEvent="onpropertychange"in m,d.input=function(){for(var t,e=["number","date","time","month","week","datetime","datetime-local"],i=e.length,n={},s=0;s<i;s++)t=e[s],m.setAttribute("type",t),m.value="test",n[t.replace("-","")]="text"!==m.type&&"test"!==m.value;return n}(),m.style.cssText="float:left;",d.cssFloat=!!m.style.cssFloat,m=null,d.stableSort=function(){for(var t=[{index:0,field:"b"}],e=1;e<513;e++)t.push({index:e,field:"a"});return t.sort(function(t,e){return t.field>e.field?1:t.field<e.field?-1:0}),1===t[0].index}(),d.matchesSelector=l.webkitMatchesSelector||l.mozMatchesSelector||l.msMatchesSelector||l.oMatchesSelector||l.matchesSelector||l.matches||function(e){for(var i=document.querySelectorAll?(this.parentNode||document).querySelectorAll(e)||[]:t(e),n=i.length;n--;)if(i[n]==this)return!0;return!1},d.pushState=e.history&&e.history.pushState;var g=document.documentMode;d.hashChange="onhashchange"in e&&!(d.browser.msie&&(!g||g<=8)),d.customElements="registerElement"in e.document;var _=d.browser.chrome,w=d.browser.mozilla;d.msPointers=!_&&e.MSPointerEvent,d.pointers=!_&&!w&&e.PointerEvent,d.kineticScrollNeeded=f&&(d.touch||d.msPointers||d.pointers)}();var G={};function Y(e,i,n,s,a){for(var o,l=0,u=e.length;l<u;l++)(o=t(e[l])).queue(function(){var t,e,l,u;G.promise(o,(e=n,l=s,u=a,typeof(t=i)===v&&(D(e)&&(u=e,e=400,l=!1),D(l)&&(u=l,l=!1),typeof e===k&&(l=e,e=400),t={effects:t,duration:e,reverse:l,complete:u}),r({effects:{},duration:400,reverse:!1,init:h,teardown:h,hide:!1},t,{completeCallback:t.complete,complete:h})))});return e}t.extend(G,{enabled:!0,Element:function(e){this.element=t(e)},promise:function(t,e){t.is(":visible")||t.css({display:t.data("olddisplay")||"block"}).css("display"),e.hide&&t.data("olddisplay",t.css("display")).hide(),e.init&&e.init(),e.completeCallback&&e.completeCallback(t),t.dequeue()},disable:function(){this.enabled=!1,this.promise=this.promiseShim},enable:function(){this.enabled=!0,this.promise=this.animatedPromise}}),G.promiseShim=G.promise,"kendoAnimate"in t.fn||r(t.fn,{kendoStop:function(t,e){return this.stop(t,e)},kendoAnimate:function(t,e,i,n){return Y(this,t,e,i,n)},kendoAddClass:function(t,e){return s.toggleClass(this,t,e,!0)},kendoRemoveClass:function(t,e){return s.toggleClass(this,t,e,!1)},kendoToggleClass:function(t,e,i){return s.toggleClass(this,t,e,i)}});var $=/&/g,K=/</g,J=/"/g,Q=/'/g,X=/>/g,Z=function(t){return t.target};d.touch&&(Z=function(t){var e="originalEvent"in t?t.originalEvent.changedTouches:"changedTouches"in t?t.changedTouches:null;return e?document.elementFromPoint(e[0].clientX,e[0].clientY):t.target},a(["swipe","swipeLeft","swipeRight","swipeUp","swipeDown","doubleTap","tap"],function(e,i){t.fn[i]=function(t){return this.bind(i,t)}})),d.touch?d.mobileOS?(d.mousedown="touchstart",d.mouseup="touchend",d.mousemove="touchmove",d.mousecancel="touchcancel",d.click="touchend",d.resize="orientationchange"):(d.mousedown="mousedown touchstart",d.mouseup="mouseup touchend",d.mousemove="mousemove touchmove",d.mousecancel="mouseleave touchcancel",d.click="click",d.resize="resize"):d.pointers?(d.mousemove="pointermove",d.mousedown="pointerdown",d.mouseup="pointerup",d.mousecancel="pointercancel",d.click="pointerup",d.resize="orientationchange resize"):d.msPointers?(d.mousemove="MSPointerMove",d.mousedown="MSPointerDown",d.mouseup="MSPointerUp",d.mousecancel="MSPointerCancel",d.click="MSPointerUp",d.resize="orientationchange resize"):(d.mousemove="mousemove",d.mousedown="mousedown",d.mouseup="mouseup",d.mousecancel="mouseleave",d.click="click",d.resize="resize");var tt=/^([a-z]+:)?\/\//i;r(s,{widgets:[],_widgetRegisteredCallbacks:[],ui:s.ui||{},fx:s.fx||function(t){return new s.effects.Element(t)},effects:s.effects||G,mobile:s.mobile||{},data:s.data||{},dataviz:s.dataviz||{},drawing:s.drawing||{},spreadsheet:{messages:{}},keys:{INSERT:45,DELETE:46,BACKSPACE:8,TAB:9,ENTER:13,ESC:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,END:35,HOME:36,SPACEBAR:32,PAGEUP:33,PAGEDOWN:34,F2:113,F10:121,F12:123,NUMPAD_PLUS:107,NUMPAD_MINUS:109,NUMPAD_DOT:110},support:s.support||d,animate:s.animate||Y,ns:"",attr:function(t){return"data-"+s.ns+t},getShadows:function(t){var e=t.css(s.support.transitions.css+"box-shadow")||t.css("box-shadow"),i=e?e.match(m)||[0,0,0,0,0]:[0,0,0,0,0],n=u.max(+i[3],+(i[4]||0));return{left:-i[1]+n,right:+i[1]+n,bottom:+i[2]+n}},wrap:function(e,i){var n,r=d.browser,a=s._outerWidth,o=s._outerHeight;if(e.parent().hasClass("k-animation-container")){var l=e.parent(".k-animation-container"),h=l[0].style;l.is(":hidden")&&l.css({display:"",position:""}),(n=p.test(h.width)||p.test(h.height))||l.css({width:i?a(e)+1:a(e),height:o(e),boxSizing:"content-box",mozBoxSizing:"content-box",webkitBoxSizing:"content-box"})}else{var c=e[0].style.width,f=e[0].style.height,m=p.test(c),g=p.test(f);n=m||g,!m&&(!i||i&&c)&&(c=i?a(e)+1:a(e)),!g&&(!i||i&&f)&&(f=o(e)),e.wrap(t("<div/>").addClass("k-animation-container").css({width:c,height:f})),n&&e.css({width:"100%",height:"100%",boxSizing:"border-box",mozBoxSizing:"border-box",webkitBoxSizing:"border-box"})}return r.msie&&u.floor(r.version)<=7&&(e.css({zoom:1}),e.children(".k-menu").width(e.width())),e.parent()},deepExtend:N,getComputedStyles:j,webComponents:s.webComponents||[],isScrollable:function(t){if(t&&t.className&&"string"==typeof t.className&&t.className.indexOf("k-auto-scrollable")>-1)return!0;var e=j(t,["overflow"]).overflow;return"auto"==e||"scroll"==e},scrollLeft:function(e,n){var s,r=d.browser.webkit,a=d.browser.mozilla,o=e instanceof t?e[0]:e;if(e){if(s=d.isRtl(e),n===i)return s&&r?o.scrollWidth-o.clientWidth-o.scrollLeft:Math.abs(o.scrollLeft);o.scrollLeft=s&&r?o.scrollWidth-o.clientWidth-n:s&&a?-n:n}},size:function(t){var e,i=0;for(e in t)t.hasOwnProperty(e)&&"toJSON"!=e&&i++;return i},toCamelCase:q,toHyphens:function(t){return t.replace(/([a-z][A-Z])/g,function(t){return t.charAt(0)+"-"+t.charAt(1).toLowerCase()})},getOffset:s.getOffset||function(t,i,n){i||(i="offset");var s=t[i](),r={top:s.top,right:s.right,bottom:s.bottom,left:s.left};if(d.browser.msie&&(d.pointers||d.msPointers)&&!n){var a=d.isRtl(t)?1:-1;r.top-=e.pageYOffset-document.documentElement.scrollTop,r.left-=e.pageXOffset+a*document.documentElement.scrollLeft}return r},parseEffects:s.parseEffects||function(t){var e={};return a("string"==typeof t?t.split(" "):t,function(t){e[t]=this}),e},toggleClass:s.toggleClass||function(t,e,i,n){return e&&(e=e.split(" "),a(e,function(e,i){t.toggleClass(i,n)})),t},directions:s.directions||{left:{reverse:"right"},right:{reverse:"left"},down:{reverse:"up"},up:{reverse:"down"},top:{reverse:"bottom"},bottom:{reverse:"top"},in:{reverse:"out"},out:{reverse:"in"}},Observable:O,Class:I,Template:n,template:l(n.compile,n),render:l(n.render,n),stringify:l(c.stringify,c),eventTarget:Z,htmlEncode:function(t){return(""+t).replace($,"&amp;").replace(K,"&lt;").replace(X,"&gt;").replace(J,"&quot;").replace(Q,"&#39;")},isLocalUrl:function(t){return t&&!tt.test(t)},expr:function(t,e,i){return t=t||"",typeof e==v&&(i=e,e=!1),i=i||"d",t&&"["!==t.charAt(0)&&(t="."+t),t=e?(t=function(t,e){var i,n,s,r,a=e||"d",o=1;for(n=0,s=t.length;n<s;n++)""!==(r=t[n])&&(0!==(i=r.indexOf("["))&&(-1==i?r="."+r:(o++,r="."+r.substring(0,i)+" || {})"+r.substring(i))),o++,a+=r+(n<s-1?" || {})":")"));return new Array(o).join("(")+a}((t=(t=t.replace(/"([^.]*)\.([^"]*)"/g,'"$1_$DOT$_$2"')).replace(/'([^.]*)\.([^']*)'/g,"'$1_$DOT$_$2'")).split("."),i)).replace(/_\$DOT\$_/g,"."):i+t},getter:function(t,e){var i=t+e;return C[i]=C[i]||new Function("d","return "+s.expr(t,e))},setter:function(t){return T[t]=T[t]||new Function("d,value",s.expr(t)+"=value")},accessor:function(t){return{get:s.getter(t),set:s.setter(t)}},guid:function(){var t,e,i="";for(t=0;t<32;t++)e=16*u.random()|0,8!=t&&12!=t&&16!=t&&20!=t||(i+="-"),i+=(12==t?4:16==t?3&e|8:e).toString(16);return i},roleSelector:function(t){return t.replace(/(\S+)/g,"["+s.attr("role")+"=$1],").slice(0,-1)},directiveSelector:function(t){var e=t.split(" ");if(e)for(var i=0;i<e.length;i++)"view"!=e[i]&&(e[i]=e[i].replace(/(\w*)(view|bar|strip|over)$/,"$1-$2"));return e.join(" ").replace(/(\S+)/g,"kendo-mobile-$1,").slice(0,-1)},triggeredByInput:function(t){return/^(label|input|textarea|select)$/i.test(t.target.tagName)},onWidgetRegistered:function(t){for(var e=0,i=s.widgets.length;e<i;e++)t(s.widgets[e]);s._widgetRegisteredCallbacks.push(t)},logToConsole:function(t,i){var n=e.console;!s.suppressLog&&void 0!==n&&n.log&&n[i||"log"](t)}});var et=O.extend({init:function(t,e){this.element=s.jQuery(t).handler(this),this.angular("init",e),O.fn.init.call(this);var i=e?e.dataSource:null;i&&(e=r({},e,{dataSource:{}})),e=this.options=r(!0,{},this.options,e),i&&(e.dataSource=i),this.element.attr(s.attr("role"))||this.element.attr(s.attr("role"),(e.name||"").toLowerCase()),this.element.data("kendo"+e.prefix+e.name,this),this.bind(this.events,e)},events:[],options:{prefix:""},_hasBindingTarget:function(){return!!this.element[0].kendoBindingTarget},_tabindex:function(t){t=t||this.wrapper;var e=this.element,i=t.attr("tabindex")||e.attr("tabindex");e.removeAttr("tabindex"),t.attr("tabindex",isNaN(i)?0:i)},setOptions:function(e){this._setEvents(e),t.extend(this.options,e)},_setEvents:function(t){for(var e,i=0,n=this.events.length;i<n;i++)e=this.events[i],this.options[e]&&t[e]&&this.unbind(e,this.options[e]);this.bind(this.events,t)},resize:function(t){var e=this.getSize(),i=this._size;(t||(e.width>0||e.height>0)&&(!i||e.width!==i.width||e.height!==i.height))&&(this._size=e,this._resize(e,t),this.trigger("resize",e))},getSize:function(){return s.dimensions(this.element)},size:function(t){if(!t)return this.getSize();this.setSize(t)},setSize:t.noop,_resize:t.noop,destroy:function(){this.element.removeData("kendo"+this.options.prefix+this.options.name),this.element.removeData("handler"),this.unbind()},_destroy:function(){this.destroy()},angular:function(){},_muteAngularRebind:function(t){this._muteRebind=!0,t.call(this),this._muteRebind=!1}}),it=et.extend({dataItems:function(){return this.dataSource.flatView()},_angularItems:function(e){var i=this;i.angular(e,function(){return{elements:i.items(),data:t.map(i.dataItems(),function(t){return{dataItem:t}})}})}});s.dimensions=function(t,e){var i=t[0];return e&&t.css(e),{width:i.offsetWidth,height:i.offsetHeight}},s.notify=h;var nt=/template$/i,st=/^\s*(?:\{(?:.|\r\n|\n)*\}|\[(?:.|\r\n|\n)*\])\s*$/,rt=/^\{(\d+)(:[^\}]+)?\}|^\[[A-Za-z_]+\]$/,at=/([A-Z])/g;function ot(t,e){var n;return 0===e.indexOf("data")&&(e=(e=e.substring(4)).charAt(0).toLowerCase()+e.substring(1)),e=e.replace(at,"-$1"),null===(n=t.getAttribute("data-"+s.ns+e))?n=i:"null"===n?n=null:"true"===n?n=!0:"false"===n?n=!1:g.test(n)&&"mask"!=e?n=parseFloat(n):st.test(n)&&!rt.test(n)&&(n=new Function("return ("+n+")")()),n}function lt(e,n,r){var a,o,l={};for(a in n)(o=ot(e,a))!==i&&(nt.test(a)&&("string"==typeof o?t("#"+o).length?o=s.template(t("#"+o).html()):r&&(o=s.template(r[o])):o=e.getAttribute(a)),l[a]=o);return l}function ht(e,i){return t.contains(e,i)?-1:1}function ut(){var e=t(this);return t.inArray(e.attr("data-"+s.ns+"role"),["slider","rangeslider"])>-1||e.is(":visible")}s.initWidget=function(n,r,a){var o,l,h,u,c,d,p,f,m,g;if(a?a.roles&&(a=a.roles):a=s.ui.roles,d=(n=n.nodeType?n:n[0]).getAttribute("data-"+s.ns+"role")){h=(m=-1===d.indexOf("."))?a[d]:s.getter(d)(e);var _=t(n).data(),w=h?"kendo"+h.fn.options.prefix+h.fn.options.name:"";g=m?new RegExp("^kendo.*"+d+"$","i"):new RegExp("^"+w+"$","i");for(var b in _)if(b.match(g)){if(b!==w)return _[b];o=_[b]}if(h){for(f=ot(n,"dataSource"),r=t.extend({},lt(n,h.fn.options),r),f&&(r.dataSource=typeof f===v?s.getter(f)(e):f),u=0,c=h.fn.events.length;u<c;u++)(p=ot(n,l=h.fn.events[u]))!==i&&(r[l]=s.getter(p)(e));return o?t.isEmptyObject(r)||o.setOptions(r):o=new h(n,r),o}}},s.rolesFromNamespaces=function(t){var e,i,n=[];for(t[0]||(t=[s.ui,s.dataviz.ui]),e=0,i=t.length;e<i;e++)n[e]=t[e].roles;return r.apply(null,[{}].concat(n.reverse()))},s.init=function(e){var i=s.rolesFromNamespaces(S.call(arguments,1));t(e).find("[data-"+s.ns+"role]").addBack().each(function(){s.initWidget(this,{},i)})},s.destroy=function(e){t(e).find("[data-"+s.ns+"role]").addBack().each(function(){var e=t(this).data();for(var i in e)0===i.indexOf("kendo")&&typeof e[i].destroy===_&&e[i].destroy()})},s.resize=function(e,i){var n=t(e).find("[data-"+s.ns+"role]").addBack().filter(ut);if(n.length){var r=t.makeArray(n);r.sort(ht),t.each(r,function(){var e=s.widgetInstance(t(this));e&&e.resize(i)})}},s.parseOptions=lt,r(s.ui,{Widget:et,DataBoundWidget:it,roles:{},progress:function(e,i,n){var r,a,o,l,h,u=e.find(".k-loading-mask"),c=s.support,d=c.browser;h=(n=t.extend({},{width:"100%",height:"100%",top:e.scrollTop(),opacity:!1},n)).opacity?"k-loading-mask k-opaque":"k-loading-mask",i?u.length||(a=(r=c.isRtl(e))?"right":"left",l=e.scrollLeft(),o=d.webkit&&r?e[0].scrollWidth-e.width()-2*l:0,u=t(s.format("<div class='{0}'><span class='k-loading-text'>{1}</span><div class='k-loading-image'/><div class='k-loading-color'/></div>",h,s.ui.progress.messages.loading)).width(n.width).height(n.height).css("top",n.top).css(a,Math.abs(l)+o).prependTo(e)):u&&u.remove()},plugin:function(e,n,r){var a,o=e.fn.options.name;n=n||s.ui,r=r||"",n[o]=e,n.roles[o.toLowerCase()]=e,a="getKendo"+r+o;var l={name:o="kendo"+r+o,widget:e,prefix:r||""};s.widgets.push(l);for(var h=0,u=s._widgetRegisteredCallbacks.length;h<u;h++)s._widgetRegisteredCallbacks[h](l);t.fn[o]=function(n){var r,a=this;return typeof n===v?(r=S.call(arguments,1),this.each(function(){var e,l,h=t.data(this,o);if(!h)throw new Error(s.format("Cannot call method '{0}' of {1} before it is initialized",n,o));if(typeof(e=h[n])!==_)throw new Error(s.format("Cannot find method '{0}' of {1}",n,o));if((l=e.apply(h,r))!==i)return a=l,!1})):this.each(function(){return new e(this,n)}),a},t.fn[o].widget=e,t.fn[a]=function(){return this.data(o)}}}),s.ui.progress.messages={loading:"Loading..."};var ct={bind:function(){return this},nullObject:!0,options:{}},dt=et.extend({init:function(t,e){et.fn.init.call(this,t,e),this.element.autoApplyNS(),this.wrapper=this.element,this.element.addClass("km-widget")},destroy:function(){et.fn.destroy.call(this),this.element.kendoDestroy()},options:{prefix:"Mobile"},events:[],view:function(){var t=this.element.closest(s.roleSelector("view splitview modalview drawer"));return s.widgetInstance(t,s.mobile.ui)||ct},viewHasNativeScrolling:function(){var t=this.view();return t&&t.options.useNativeScrolling},container:function(){var t=this.element.closest(s.roleSelector("view layout modalview drawer splitview"));return s.widgetInstance(t.eq(0),s.mobile.ui)||ct}});function pt(e,i){var n,s=e.nodeName.toLowerCase();return(/input|select|textarea|button|object/.test(s)?!e.disabled:"a"===s?e.href||i:i)&&(n=e,t.expr.filters.visible(n)&&!t(n).parents().addBack().filter(function(){return"hidden"===t.css(this,"visibility")}).length)}r(s.mobile,{init:function(t){s.init(t,s.mobile.ui,s.ui,s.dataviz.ui)},appLevelNativeScrolling:function(){return s.mobile.application&&s.mobile.application.options&&s.mobile.application.options.useNativeScrolling},roles:{},ui:{Widget:dt,DataBoundWidget:it.extend(dt.prototype),roles:{},plugin:function(t){s.ui.plugin(t,s.mobile.ui,"Mobile")}}}),N(s.dataviz,{init:function(t){s.init(t,s.dataviz.ui)},ui:{roles:{},themes:{},views:[],plugin:function(t){s.ui.plugin(t,s.dataviz.ui)}},roles:{}}),s.touchScroller=function(e,i){return i||(i={}),i.useNative=!0,t(e).map(function(e,n){return n=t(n),!(!d.kineticScrollNeeded||!s.mobile.ui.Scroller||n.data("kendoMobileScroller"))&&(n.kendoMobileScroller(i),n.data("kendoMobileScroller"))})[0]},s.preventDefault=function(t){t.preventDefault()},s.widgetInstance=function(t,i){var n,r,a=t.data(s.ns+"role"),o=[];if(a){if("content"===a&&(a="scroller"),"editortoolbar"===a){var l=t.data("kendoEditorToolbar");if(l)return l}if(i)if(i[0])for(n=0,r=i.length;n<r;n++)o.push(i[n].roles[a]);else o.push(i.roles[a]);else o=[s.ui.roles[a],s.dataviz.ui.roles[a],s.mobile.ui.roles[a]];for(a.indexOf(".")>=0&&(o=[s.getter(a)(e)]),n=0,r=o.length;n<r;n++){var h=o[n];if(h){var u=t.data("kendo"+h.fn.options.prefix+h.fn.options.name);if(u)return u}}}},s.onResize=function(i){var n=i;return d.mobileOS.android&&(n=function(){setTimeout(i,600)}),t(e).on(d.resize,n),n},s.unbindResize=function(i){t(e).off(d.resize,i)},s.attrValue=function(t,e){return t.data(s.ns+e)},s.days={Sunday:0,Monday:1,Tuesday:2,Wednesday:3,Thursday:4,Friday:5,Saturday:6},t.extend(t.expr[":"],{kendoFocusable:function(e){var i=t.attr(e,"tabindex");return pt(e,!isNaN(i)&&i>-1)}});var ft=["mousedown","mousemove","mouseenter","mouseleave","mouseover","mouseout","mouseup","click"],mt={setupMouseMute:function(){var e=0,i=ft.length,n=document.documentElement;if(!mt.mouseTrap&&d.eventCapture){mt.mouseTrap=!0,mt.bustClick=!1,mt.captureMouse=!1;for(var s=function(e){mt.captureMouse&&("click"===e.type?mt.bustClick&&!t(e.target).is("label, input, [data-rel=external]")&&(e.preventDefault(),e.stopPropagation()):e.stopPropagation())};e<i;e++)n.addEventListener(ft[e],s,!0)}},muteMouse:function(t){mt.captureMouse=!0,t.data.bustClick&&(mt.bustClick=!0),clearTimeout(mt.mouseTrapTimeoutID)},unMuteMouse:function(){clearTimeout(mt.mouseTrapTimeoutID),mt.mouseTrapTimeoutID=setTimeout(function(){mt.captureMouse=!1,mt.bustClick=!1},400)}},gt={down:"touchstart mousedown",move:"mousemove touchmove",up:"mouseup touchend touchcancel",cancel:"mouseleave touchcancel"};d.touch&&(d.mobileOS.ios||d.mobileOS.android)?gt={down:"touchstart",move:"touchmove",up:"touchend touchcancel",cancel:"touchcancel"}:d.pointers?gt={down:"pointerdown",move:"pointermove",up:"pointerup",cancel:"pointercancel pointerleave"}:d.msPointers&&(gt={down:"MSPointerDown",move:"MSPointerMove",up:"MSPointerUp",cancel:"MSPointerCancel MSPointerLeave"}),!d.msPointers||"onmspointerenter"in e||t.each({MSPointerEnter:"MSPointerOver",MSPointerLeave:"MSPointerOut"},function(e,i){t.event.special[e]={delegateType:i,bindType:i,handle:function(e){var n,s=e.relatedTarget,r=e.handleObj;return s&&(s===this||t.contains(this,s))||(e.type=r.origType,n=r.handler.apply(this,arguments),e.type=i),n}}});var _t=function(t){return gt[t]||t},vt=/([^ ]+)/g;s.applyEventMap=function(t,e){return t=t.replace(vt,_t),e&&(t=t.replace(vt,"$1."+e)),t};var wt=t.fn.on;function bt(t,e){return new bt.fn.init(t,e)}r(!0,bt,t),bt.fn=bt.prototype=new t,bt.fn.constructor=bt,bt.fn.init=function(e,i){return i&&i instanceof t&&!(i instanceof bt)&&(i=bt(i)),t.fn.init.call(this,e,i,yt)},bt.fn.init.prototype=bt.fn;var yt=bt(document);r(bt.fn,{handler:function(t){return this.data("handler",t),this},autoApplyNS:function(t){return this.data("kendoNS",t||s.guid()),this},on:function(){var t=this.data("kendoNS");if(1===arguments.length)return wt.call(this,arguments[0]);var e=this,i=S.call(arguments);typeof i[i.length-1]===x&&i.pop();var n=i[i.length-1],r=s.applyEventMap(i[0],t);if(d.mouseAndTouchPresent&&r.search(/mouse|click/)>-1&&this[0]!==document.documentElement){mt.setupMouseMute();var a=2===i.length?null:i[1],o=r.indexOf("click")>-1&&r.indexOf("touchend")>-1;wt.call(this,{touchstart:mt.muteMouse,touchend:mt.unMuteMouse},a,{bustClick:o})}return typeof n===v&&(e=this.data("handler"),n=e[n],i[i.length-1]=function(t){n.call(e,t)}),i[0]=r,wt.apply(this,i),this},kendoDestroy:function(t){return(t=t||this.data("kendoNS"))&&this.off("."+t),this}}),s.jQuery=bt,s.eventMap=gt,s.timezone=function(){var t={Jan:0,Feb:1,Mar:2,Apr:3,May:4,Jun:5,Jul:6,Aug:7,Sep:8,Oct:9,Nov:10,Dec:11},e={Sun:0,Mon:1,Tue:2,Wed:3,Thu:4,Fri:5,Sat:6};function i(i,n){var s,r,a,o=n[3],l=n[4],h=n[5],u=n[8];return u||(n[8]=u={}),u[i]?u[i]:(isNaN(l)?0===l.indexOf("last")?(s=new Date(Date.UTC(i,t[o]+1,1,h[0]-24,h[1],h[2],0)),r=e[l.substr(4,3)],a=s.getUTCDay(),s.setUTCDate(s.getUTCDate()+r-a-(r>a?7:0))):l.indexOf(">=")>=0&&(s=new Date(Date.UTC(i,t[o],l.substr(5),h[0],h[1],h[2],0)),r=e[l.substr(0,3)],a=s.getUTCDay(),s.setUTCDate(s.getUTCDate()+r-a+(r<a?7:0))):s=new Date(Date.UTC(i,t[o],l,h[0],h[1],h[2],0)),u[i]=s)}function n(t,e,n,s){typeof t!=w&&(t=Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));var r=function(t,e,i){var n=e[i];if("string"==typeof n&&(n=e[n]),!n)throw new Error('Timezone "'+i+'" is either incorrect, or kendo.timezones.min.js is not included.');for(var s=n.length-1;s>=0;s--){var r=n[s][3];if(r&&t>r)break}var a=n[s+1];if(!a)throw new Error('Timezone "'+i+'" not found on '+t+".");return a}(t,e,s);return{zone:r,rule:function(t,e,n){if(!(e=e[n])){var s=n.split(":"),r=0;return s.length>1&&(r=60*s[0]+Number(s[1])),[-1e6,"max","-","Jan",1,[0,0,0],r,"-"]}var a=new Date(t).getUTCFullYear();(e=jQuery.grep(e,function(t){var e=t[0],i=t[1];return e<=a&&(i>=a||e==a&&"only"==i||"max"==i)})).push(t),e.sort(function(t,e){return"number"!=typeof t&&(t=Number(i(a,t))),"number"!=typeof e&&(e=Number(i(a,e))),t-e});var o=e[jQuery.inArray(t,e)-1]||e[e.length-1];return isNaN(o)?o:null}(t,n,r[1])}}return{zones:{},rules:{},offset:function(t,e){if("Etc/UTC"==e||"Etc/GMT"==e)return 0;var i=n(t,this.zones,this.rules,e),r=i.zone,a=i.rule;return s.parseFloat(a?r[0]-a[6]:r[0])},convert:function(t,e,i){var n,s=i;typeof e==v&&(e=this.offset(t,e)),typeof i==v&&(i=this.offset(t,i));var r=t.getTimezoneOffset(),a=(t=new Date(t.getTime()+6e4*(e-i))).getTimezoneOffset();return typeof s==v&&(s=this.offset(t,s)),n=a-r+(i-s),new Date(t.getTime()+6e4*n)},apply:function(t,e){return this.convert(t,t.getTimezoneOffset(),e)},remove:function(t,e){return this.convert(t,e,t.getTimezoneOffset())},abbr:function(t,e){var i=n(t,this.zones,this.rules,e),s=i.zone,r=i.rule,a=s[2];return a.indexOf("/")>=0?a.split("/")[r&&+r[6]?1:0]:a.indexOf("%s")>=0?a.replace("%s",r&&"-"!=r[7]?r[7]:""):a},toLocalDate:function(t){return this.apply(new Date(t),"Etc/UTC")}}}(),s.date=function(){var t=6e4,e=864e5;function n(t,e){return 0===e&&23===t.getHours()&&(t.setHours(t.getHours()+2),!0)}function r(t,e,i){var s=t.getHours();i=i||1,e=(e-t.getDay()+7*i)%7,t.setDate(t.getDate()+e),n(t,s)}function a(t,e,i){return r(t=new Date(t),e,i),t}function o(t){return new Date(t.getFullYear(),t.getMonth(),1)}function l(t,i){var n,s,r=new Date(t.getFullYear(),0,1,-6),o=(n=t,1!==(s=i)?c(a(n,s,-1),4):c(n,4-(n.getDay()||7))).getTime()-r.getTime(),l=Math.floor(o/e);return 1+Math.floor(l/7)}function h(t){return n(t=new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,0,0),0),t}function u(t){return f(t).getTime()-h(f(t))}function c(t,i){var s=t.getHours();return d(t=new Date(t),i*e),n(t,s),t}function d(e,i,n){var s,r=e.getTimezoneOffset();e.setTime(e.getTime()+i),n||(s=e.getTimezoneOffset()-r,e.setTime(e.getTime()+s*t))}function p(){return h(new Date)}function f(t){var e=new Date(1980,1,1,0,0,0);return t&&e.setHours(t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()),e}return{adjustDST:n,dayOfWeek:a,setDayOfWeek:r,getDate:h,isInDateRange:function(t,i,n){var s,r=i.getTime(),a=n.getTime();return r>=a&&(a+=e),(s=t.getTime())>=r&&s<=a},isInTimeRange:function(t,i,n){var s,r=u(i),a=u(n);return!t||r==a||(i>=n&&(n+=e),r>(s=u(t))&&(s+=e),a<r&&(a+=e),s>=r&&s<=a)},isToday:function(t){return h(t).getTime()==p().getTime()},nextDay:function(t){return c(t,1)},previousDay:function(t){return c(t,-1)},toUtcTime:function(t){return Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds())},MS_PER_DAY:e,MS_PER_HOUR:60*t,MS_PER_MINUTE:t,setTime:d,setHours:function(t,e){return n(t=new Date(s.date.getDate(t).getTime()+s.date.getMilliseconds(e)),e.getHours()),t},addDays:c,today:p,toInvariantTime:f,firstDayOfMonth:o,lastDayOfMonth:function(t){var e=new Date(t.getFullYear(),t.getMonth()+1,0),i=o(t),n=Math.abs(e.getTimezoneOffset()-i.getTimezoneOffset());return n&&e.setHours(i.getHours()+n/60),e},weekInYear:function(t,e){e===i&&(e=s.culture().calendar.firstDay);var n=c(t,-7),r=c(t,7),a=l(t,e);return 0===a?l(n,e)+1:53===a&&l(r,e)>1?1:a},getMilliseconds:u}}(),s.stripWhitespace=function(t){if(document.createNodeIterator)for(var e=document.createNodeIterator(t,NodeFilter.SHOW_TEXT,function(e){return e.parentNode==t?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT},!1);e.nextNode();)e.referenceNode&&!e.referenceNode.textContent.trim()&&e.referenceNode.parentNode.removeChild(e.referenceNode);else for(var i=0;i<t.childNodes.length;i++){var n=t.childNodes[i];3!=n.nodeType||/\S/.test(n.nodeValue)||(t.removeChild(n),i--),1==n.nodeType&&s.stripWhitespace(n)}};var kt=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||e.oRequestAnimationFrame||e.msRequestAnimationFrame||function(t){setTimeout(t,1e3/60)};s.animationFrame=function(t){kt.call(e,t)};var xt=[];s.queueAnimation=function(t){xt[xt.length]=t,1===xt.length&&s.runNextAnimation()},s.runNextAnimation=function(){s.animationFrame(function(){xt[0]&&(xt.shift()(),xt[0]&&s.runNextAnimation())})},s.parseQueryStringParams=function(t){for(var e={},i=(t.split("?")[1]||"").split(/&|=/),n=i.length,s=0;s<n;s+=2)""!==i[s]&&(e[decodeURIComponent(i[s])]=decodeURIComponent(i[s+1]));return e},s.elementUnderCursor=function(t){if(void 0!==t.x.client)return document.elementFromPoint(t.x.client,t.y.client)},s.wheelDeltaY=function(t){var e,n=t.originalEvent,s=n.wheelDeltaY;return n.wheelDelta?(s===i||s)&&(e=n.wheelDelta):n.detail&&n.axis===n.VERTICAL_AXIS&&(e=10*-n.detail),e},s.throttle=function(t,e){var i,n=0;if(!e||e<=0)return t;var s=function(){var s=this,r=+new Date-n,a=arguments;function o(){t.apply(s,a),n=+new Date}if(!n)return o();i&&clearTimeout(i),r>e?o():i=setTimeout(o,e-r)};return s.cancel=function(){clearTimeout(i)},s},s.caret=function(e,n,s){var r,a=n!==i;if(s===i&&(s=n),e[0]&&(e=e[0]),!a||!e.disabled){try{if(e.selectionStart!==i)if(a){e.focus();var o=d.mobileOS;o.wp||o.android?setTimeout(function(){e.setSelectionRange(n,s)},0):e.setSelectionRange(n,s)}else n=[e.selectionStart,e.selectionEnd];else if(document.selection)if(t(e).is(":visible")&&e.focus(),r=e.createTextRange(),a)r.collapse(!0),r.moveStart("character",n),r.moveEnd("character",s-n),r.select();else{var l,h,u=r.duplicate();r.moveToBookmark(document.selection.createRange().getBookmark()),u.setEndPoint("EndToStart",r),h=(l=u.text.length)+r.text.length,n=[l,h]}}catch(t){n=[]}return n}},s.compileMobileDirective=function(t,i){var n=e.angular;return t.attr("data-"+s.ns+"role",t[0].tagName.toLowerCase().replace("kendo-mobile-","").replace("-","")),n.element(t).injector().invoke(["$compile",function(e){e(t)(i),/^\$(digest|apply)$/.test(i.$$phase)||i.$digest()}]),s.widgetInstance(t,s.mobile.ui)},s.antiForgeryTokens=function(){var e={},n=t("meta[name=csrf-token],meta[name=_csrf]").attr("content"),s=t("meta[name=csrf-param],meta[name=_csrf_header]").attr("content");return t("input[name^='__RequestVerificationToken']").each(function(){e[this.name]=this.value}),s!==i&&n!==i&&(e[s]=n),e},s.cycleForm=function(t){var e=t.find("input, .k-widget").first(),i=t.find("button, .k-button").last();function n(t){var e=s.widgetInstance(t);e&&e.focus?e.focus():t.focus()}i.on("keydown",function(t){t.keyCode!=s.keys.TAB||t.shiftKey||(t.preventDefault(),n(e))}),e.on("keydown",function(t){t.keyCode==s.keys.TAB&&t.shiftKey&&(t.preventDefault(),n(i))})},s.focusElement=function(i){var n=[],r=i.parentsUntil("body").filter(function(t,e){return"visible"!==s.getComputedStyles(e,["overflow"]).overflow}).add(e);r.each(function(e,i){n[e]=t(i).scrollTop()});try{i[0].setActive()}catch(t){i[0].focus()}r.each(function(e,i){t(i).scrollTop(n[e])})},function(){function i(e,i,n,r){var a=t("<form>").attr({action:n,method:"POST",target:r}),o=s.antiForgeryTokens();o.fileName=i;var l=e.split(";base64,");o.contentType=l[0].replace("data:",""),o.base64=l[1];for(var h in o)o.hasOwnProperty(h)&&t("<input>").attr({value:o[h],name:h,type:"hidden"}).appendTo(a);a.appendTo("body").submit().remove()}var n=document.createElement("a"),r="download"in n&&!s.support.browser.edge;function a(t,e){var i=t;if("string"==typeof t){for(var n=t.split(";base64,"),s=n[0],r=atob(n[1]),a=new Uint8Array(r.length),o=0;o<r.length;o++)a[o]=r.charCodeAt(o);i=new Blob([a.buffer],{type:s})}navigator.msSaveBlob(i,e)}function o(t,i){e.Blob&&t instanceof Blob&&(t=URL.createObjectURL(t)),n.download=i,n.href=t;var s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!1,e,0,0,0,0,0,!1,!1,!1,!1,0,null),n.dispatchEvent(s),setTimeout(function(){URL.revokeObjectURL(t)})}s.saveAs=function(t){var e=i;t.forceProxy||(r?e=o:navigator.msSaveBlob&&(e=a)),e(t.dataURI,t.fileName,t.proxyURL,t.proxyTarget)}}(),s.proxyModelSetters=function(t){var e={};return Object.keys(t||{}).forEach(function(i){Object.defineProperty(e,i,{get:function(){return t[i]},set:function(e){t[i]=e,t.dirty=!0}})}),e}}(jQuery,window),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.color",["kendo.core"],function(){window.kendo=window.kendo||{};var t=kendo.Class,e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"778899",lightslategrey:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"},i=kendo.support.browser,n=function(t){var i=Object.keys(e);i.push("transparent");var s=new RegExp("^("+i.join("|")+")(\\W|$)","i");return n=function(t){return s.exec(t)},s.exec(t)},s=t.extend({init:function(){},toHSV:function(){return this},toRGB:function(){return this},toHex:function(){return this.toBytes().toHex()},toBytes:function(){return this},toCss:function(){return"#"+this.toHex()},toCssRgba:function(){var t=this.toBytes();return"rgba("+t.r+", "+t.g+", "+t.b+", "+parseFloat(Number(this.a).toFixed(3))+")"},toDisplay:function(){return i.msie&&i.version<9?this.toCss():this.toCssRgba()},equals:function(t){return t===this||null!==t&&this.toCssRgba()===c(t).toCssRgba()},diff:function(t){if(null===t)return NaN;var e=this.toBytes(),i=t.toBytes();return Math.sqrt(Math.pow(.3*(e.r-i.r),2)+Math.pow(.59*(e.g-i.g),2)+Math.pow(.11*(e.b-i.b),2))},clone:function(){var t=this.toBytes();return t===this&&(t=new a(t.r,t.g,t.b,t.a)),t}}),r=s.extend({init:function(t,e,i,n){s.fn.init.call(this),this.r=t,this.g=e,this.b=i,this.a=n},toHSV:function(){var t,e,i=this.r,n=this.g,s=this.b,r=Math.min(i,n,s),a=Math.max(i,n,s),o=a-r,h=a;return 0===o?new l(0,0,h,this.a):(0!==a?(e=o/a,t=i===a?(n-s)/o:n===a?2+(s-i)/o:4+(i-n)/o,(t*=60)<0&&(t+=360)):(e=0,t=-1),new l(t,e,h,this.a))},toHSL:function(){var t,e,i=this.r,n=this.g,s=this.b,r=Math.max(i,n,s),a=Math.min(i,n,s),o=(r+a)/2;if(r===a)t=e=0;else{var l=r-a;switch(e=o>.5?l/(2-r-a):l/(r+a),r){case i:t=(n-s)/l+(n<s?6:0);break;case n:t=(s-i)/l+2;break;case s:t=(i-n)/l+4}t*=60,e*=100,o*=100}return new h(t,e,o,this.a)},toBytes:function(){return new a(255*this.r,255*this.g,255*this.b,this.a)}}),a=r.extend({init:function(t,e,i,n){r.fn.init.call(this,Math.round(t),Math.round(e),Math.round(i),n)},toRGB:function(){return new r(this.r/255,this.g/255,this.b/255,this.a)},toHSV:function(){return this.toRGB().toHSV()},toHSL:function(){return this.toRGB().toHSL()},toHex:function(){return o(this.r,2)+o(this.g,2)+o(this.b,2)},toBytes:function(){return this}});function o(t,e,i){void 0===i&&(i="0");for(var n=t.toString(16);e>n.length;)n=i+n;return n}var l=s.extend({init:function(t,e,i,n){s.fn.init.call(this),this.h=t,this.s=e,this.v=i,this.a=n},toRGB:function(){var t,e,i,n=this.h,s=this.s,a=this.v;if(0===s)t=e=i=a;else{n/=60;var o=Math.floor(n),l=n-o,h=a*(1-s),u=a*(1-s*l),c=a*(1-s*(1-l));switch(o){case 0:t=a,e=c,i=h;break;case 1:t=u,e=a,i=h;break;case 2:t=h,e=a,i=c;break;case 3:t=h,e=u,i=a;break;case 4:t=c,e=h,i=a;break;default:t=a,e=h,i=u}}return new r(t,e,i,this.a)},toHSL:function(){return this.toRGB().toHSL()},toBytes:function(){return this.toRGB().toBytes()}}),h=s.extend({init:function(t,e,i,n){s.fn.init.call(this),this.h=t,this.s=e,this.l=i,this.a=n},toRGB:function(){var t,e,i,n=this.h,s=this.s,a=this.l;if(0===s)t=e=i=a;else{n/=360,s/=100;var o=(a/=100)<.5?a*(1+s):a+s-a*s,l=2*a-o;t=u(l,o,n+1/3),e=u(l,o,n),i=u(l,o,n-1/3)}return new r(t,e,i,this.a)},toHSV:function(){return this.toRGB().toHSV()},toBytes:function(){return this.toRGB().toBytes()}});function u(t,e,i){var n=i;return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function c(t,i){var o,l;if(null==t||"none"===t)return null;if(t instanceof s)return t;var h=t.toLowerCase();if(o=n(h))return(h="transparent"===o[1]?new r(1,1,1,0):c(e[o[1]],i)).match=[o[1]],h;if((o=/^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\b/i.exec(h))?l=new a(parseInt(o[1],16),parseInt(o[2],16),parseInt(o[3],16),1):(o=/^#?([0-9a-f])([0-9a-f])([0-9a-f])\b/i.exec(h))?l=new a(parseInt(o[1]+o[1],16),parseInt(o[2]+o[2],16),parseInt(o[3]+o[3],16),1):(o=/^rgb\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/.exec(h))?l=new a(parseInt(o[1],10),parseInt(o[2],10),parseInt(o[3],10),1):(o=/^rgba\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9.]+)\s*\)/.exec(h))?l=new a(parseInt(o[1],10),parseInt(o[2],10),parseInt(o[3],10),parseFloat(o[4])):(o=/^rgb\(\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*\)/.exec(h))?l=new r(parseFloat(o[1])/100,parseFloat(o[2])/100,parseFloat(o[3])/100,1):(o=/^rgba\(\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9.]+)\s*\)/.exec(h))&&(l=new r(parseFloat(o[1])/100,parseFloat(o[2])/100,parseFloat(o[3])/100,parseFloat(o[4]))),l)l.match=o;else if(!i)throw new Error("Cannot parse color: "+h);return l}var d=t.extend({init:function(t){if(1===arguments.length)for(var e=d.formats,i=this.resolveColor(t),n=0;n<e.length;n++){var s=e[n].re,r=e[n].process,a=s.exec(i);if(a){var o=r(a);this.r=o[0],this.g=o[1],this.b=o[2]}}else this.r=arguments[0],this.g=arguments[1],this.b=arguments[2];this.r=this.normalizeByte(this.r),this.g=this.normalizeByte(this.g),this.b=this.normalizeByte(this.b)},toHex:function(){var t=this.padDigit,e=this.r.toString(16),i=this.g.toString(16),n=this.b.toString(16);return"#"+t(e)+t(i)+t(n)},resolveColor:function(t){var e=t||"black";return"#"===e.charAt(0)&&(e=e.substr(1,6)),e=(e=e.replace(/ /g,"")).toLowerCase(),e=d.namedColors[e]||e},normalizeByte:function(t){return t<0||isNaN(t)?0:t>255?255:t},padDigit:function(t){return 1===t.length?"0"+t:t},brightness:function(t){var e=Math.round;return this.r=e(this.normalizeByte(this.r*t)),this.g=e(this.normalizeByte(this.g*t)),this.b=e(this.normalizeByte(this.b*t)),this},percBrightness:function(){return Math.sqrt(.241*this.r*this.r+.691*this.g*this.g+.068*this.b*this.b)}});d.fromBytes=function(t,e,i,n){return new a(t,e,i,null!=n?n:1)},d.fromRGB=function(t,e,i,n){return new r(t,e,i,null!=n?n:1)},d.fromHSV=function(t,e,i,n){return new l(t,e,i,null!=n?n:1)},d.fromHSL=function(t,e,i,n){return new h(t,e,i,null!=n?n:1)},d.formats=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,process:function(t){return[parseInt(t[1],10),parseInt(t[2],10),parseInt(t[3],10)]}},{re:/^(\w{2})(\w{2})(\w{2})$/,process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],d.namedColors=e,kendo.deepExtend(kendo,{parseColor:c,Color:d})}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.data",["kendo.core","kendo.data.odata","kendo.data.xml"],function(){return function(t,e){var i,n=t.extend,s=t.proxy,r=t.isPlainObject,a=t.isEmptyObject,o=t.isArray,l=t.grep,h=t.ajax,u=t.each,c=t.noop,d=window.kendo,p=d.isFunction,f=d.Observable,m=d.Class,g="string",_="change",v="requestStart",w="progress",b="requestEnd",y=["create","read","update","destroy"],k=function(t){return t},x=d.getter,C=d.stringify,T=Math,S=[].push,I=[].join,D=[].pop,F=[].splice,E=[].shift,O=[].slice,A=[].unshift,M={}.toString,H=d.support.stableSort,P=/^\/Date\((.*?)\)\/$/,z=f.extend({init:function(t,e){this.type=e||B,f.fn.init.call(this),this.length=t.length,this.wrapAll(t,this)},at:function(t){return this[t]},toJSON:function(){var t,e,i=this.length,n=new Array(i);for(t=0;t<i;t++)(e=this[t])instanceof B&&(e=e.toJSON()),n[t]=e;return n},parent:c,wrapAll:function(t,e){var i,n,s=this,r=function(){return s};for(e=e||[],i=0,n=t.length;i<n;i++)e[i]=s.wrap(t[i],r);return e},wrap:function(t,e){var i=this;return null!==t&&"[object Object]"===M.call(t)&&(t instanceof i.type||t instanceof W||(t=t instanceof B?t.toJSON():t,t=new i.type(t)),t.parent=e,t.bind(_,function(t){i.trigger(_,{field:t.field,node:t.node,index:t.index,items:t.items||[this],action:t.node?t.action||"itemloaded":"itemchange"})})),t},push:function(){var t,e=this.length,i=this.wrapAll(arguments);return t=S.apply(this,i),this.trigger(_,{action:"add",index:e,items:i}),t},slice:O,sort:[].sort,join:I,pop:function(){var t=this.length,e=D.apply(this);return t&&this.trigger(_,{action:"remove",index:t-1,items:[e]}),e},splice:function(t,e,i){var n,s,r,a=this.wrapAll(O.call(arguments,2));if((n=F.apply(this,[t,e].concat(a))).length)for(this.trigger(_,{action:"remove",index:t,items:n}),s=0,r=n.length;s<r;s++)n[s]&&n[s].children&&n[s].unbind(_);return i&&this.trigger(_,{action:"add",index:t,items:a}),n},shift:function(){var t=this.length,e=E.apply(this);return t&&this.trigger(_,{action:"remove",index:0,items:[e]}),e},unshift:function(){var t,e=this.wrapAll(arguments);return t=A.apply(this,e),this.trigger(_,{action:"add",index:0,items:e}),t},indexOf:function(t){var e,i;for(e=0,i=this.length;e<i;e++)if(this[e]===t)return e;return-1},forEach:function(t,e){for(var i=0,n=this.length,s=e||window;i<n;i++)t.call(s,this[i],i,this)},map:function(t,e){for(var i=0,n=[],s=this.length,r=e||window;i<s;i++)n[i]=t.call(r,this[i],i,this);return n},reduce:function(t){var e,i=0,n=this.length;for(2==arguments.length?e=arguments[1]:i<n&&(e=this[i++]);i<n;i++)e=t(e,this[i],i,this);return e},reduceRight:function(t){var e,i=this.length-1;for(2==arguments.length?e=arguments[1]:i>0&&(e=this[i--]);i>=0;i--)e=t(e,this[i],i,this);return e},filter:function(t,e){for(var i,n=0,s=[],r=this.length,a=e||window;n<r;n++)i=this[n],t.call(a,i,n,this)&&(s[s.length]=i);return s},find:function(t,e){for(var i,n=0,s=this.length,r=e||window;n<s;n++)if(i=this[n],t.call(r,i,n,this))return i},every:function(t,e){for(var i,n=0,s=this.length,r=e||window;n<s;n++)if(i=this[n],!t.call(r,i,n,this))return!1;return!0},some:function(t,e){for(var i,n=0,s=this.length,r=e||window;n<s;n++)if(i=this[n],t.call(r,i,n,this))return!0;return!1},remove:function(t){var e=this.indexOf(t);-1!==e&&this.splice(e,1)},empty:function(){this.splice(0,this.length)}});"undefined"!=typeof Symbol&&Symbol.iterator&&!z.prototype[Symbol.iterator]&&(z.prototype[Symbol.iterator]=[][Symbol.iterator]);var V=z.extend({init:function(t,e){f.fn.init.call(this),this.type=e||B;for(var i=0;i<t.length;i++)this[i]=t[i];this.length=i,this._parent=s(function(){return this},this)},at:function(t){var e=this[t];return e instanceof this.type?e.parent=this._parent:e=this[t]=this.wrap(e,this._parent),e}});function R(t,e,i,n){return function(s){var r,a={};for(r in s)a[r]=s[r];a.field=n?i+"."+s.field:i,e==_&&t._notifyChange&&t._notifyChange(a),t.trigger(e,a)}}var B=f.extend({init:function(t){var e,i,n=this,s=function(){return n};f.fn.init.call(this),this._handlers={};for(i in t)"object"==typeof(e=t[i])&&e&&!e.getTime&&"_"!=i.charAt(0)&&(e=n.wrap(e,i,s)),n[i]=e;n.uid=d.guid()},shouldSerialize:function(t){return this.hasOwnProperty(t)&&"_handlers"!==t&&"_events"!==t&&"function"!=typeof this[t]&&"uid"!==t},forEach:function(t){for(var e in this)this.shouldSerialize(e)&&t(this[e],e)},toJSON:function(){var t,e,i={};for(e in this)this.shouldSerialize(e)&&(((t=this[e])instanceof B||t instanceof z)&&(t=t.toJSON()),i[e]=t);return i},get:function(t){return this.trigger("get",{field:t}),"this"===t?this:d.getter(t,!0)(this)},_set:function(t,e){var i=t.indexOf(".")>=0;if(i)for(var n=t.split("."),s="";n.length>1;){s+=n.shift();var r=d.getter(s,!0)(this);if(r instanceof B)return r.set(n.join("."),e),i;s+="."}return d.setter(t)(this,e),i},set:function(t,e){var i=this,n=!1,s=t.indexOf(".")>=0,r=d.getter(t,!0)(i);return r!==e&&(r instanceof f&&this._handlers[t]&&(this._handlers[t].get&&r.unbind("get",this._handlers[t].get),r.unbind(_,this._handlers[t].change)),(n=i.trigger("set",{field:t,value:e}))||(s||(e=i.wrap(e,t,function(){return i})),(!i._set(t,e)||t.indexOf("(")>=0||t.indexOf("[")>=0)&&i.trigger(_,{field:t}))),n},parent:c,wrap:function(t,e,i){var n,s,r=M.call(t);if(null!=t&&("[object Object]"===r||"[object Array]"===r)){var a=t instanceof z,o=t instanceof kt;"[object Object]"!==r||o||a?("[object Array]"===r||a||o)&&(a||o||(t=new z(t)),s=R(this,_,e,!1),t.bind(_,s),this._handlers[e]={change:s}):(t instanceof B||(t=new B(t)),n=R(this,"get",e,!0),t.bind("get",n),s=R(this,_,e,!0),t.bind(_,s),this._handlers[e]={get:n,change:s}),t.parent=i}return t}}),L={number:function(t){return typeof t===g&&"null"===t.toLowerCase()?null:d.parseFloat(t)},date:function(t){return typeof t===g&&"null"===t.toLowerCase()?null:d.parseDate(t)},boolean:function(t){return typeof t===g?"null"===t.toLowerCase()?null:"true"===t.toLowerCase():null!=t?!!t:t},string:function(t){return typeof t===g&&"null"===t.toLowerCase()?null:null!=t?t+"":t},default:function(t){return t}},N={string:"",number:0,date:new Date,boolean:!1,default:""},W=B.extend({init:function(i){if((!i||t.isEmptyObject(i))&&(i=t.extend({},this.defaults,i),this._initializers))for(var n=0;n<this._initializers.length;n++){var s=this._initializers[n];i[s]=this.defaults[s]()}B.fn.init.call(this,i),this.dirty=!1,this.dirtyFields={},this.idField&&(this.id=this.get(this.idField),this.id===e&&(this.id=this._defaultId))},shouldSerialize:function(t){return B.fn.shouldSerialize.call(this,t)&&"uid"!==t&&!("id"!==this.idField&&"id"===t)&&"dirty"!==t&&"dirtyFields"!==t&&"_accessors"!==t},_parse:function(t,e){var i,n=t,s=this.fields||{};return(t=s[t])||(t=function(t,e){var i,n;for(n in t){if(i=t[n],r(i)&&i.field&&i.field===e)return i;if(i===e)return i}return null}(s,n)),t&&!(i=t.parse)&&t.type&&(i=L[t.type.toLowerCase()]),i?i(e):e},_notifyChange:function(t){var e=t.action;"add"!=e&&"remove"!=e||(this.dirty=!0,this.dirtyFields[t.field]=!0)},editable:function(t){return!(t=(this.fields||{})[t])||!1!==t.editable},set:function(e,i,n){var s=this.dirty;this.editable(e)&&(function e(i,n){if(i===n)return!0;var s,r=t.type(i);if(r!==t.type(n))return!1;if("date"===r)return i.getTime()===n.getTime();if("object"!==r&&"array"!==r)return!1;for(s in i)if(!e(i[s],n[s]))return!1;return!0}(i=this._parse(e,i),this.get(e))?this.trigger("equalSet",{field:e,value:i}):(this.dirty=!0,this.dirtyFields[e]=!0,B.fn.set.call(this,e,i,n)&&!s&&(this.dirty=s,this.dirty||(this.dirtyFields[e]=!1))))},accept:function(t){var e,i=this,n=function(){return i};for(e in t){var s=t[e];"_"!=e.charAt(0)&&(s=i.wrap(t[e],e,n)),i._set(e,s)}i.idField&&(i.id=i.get(i.idField)),i.dirty=!1,i.dirtyFields={}},isNew:function(){return this.id===this._defaultId}});W.define=function(t,i){i===e&&(i=t,t=W);var s,r,a,o,l,h,u,c,d=n({defaults:{}},i),p={},f=d.id,m=[];if(f&&(d.idField=f),d.id&&delete d.id,f&&(d.defaults[f]=d._defaultId=""),"[object Array]"===M.call(d.fields)){for(h=0,u=d.fields.length;h<u;h++)typeof(a=d.fields[h])===g?p[a]={}:a.field&&(p[a.field]=a);d.fields=p}for(r in d.fields)o=(a=d.fields[r]).type||"default",l=null,c=r,r=typeof a.field===g?a.field:r,a.nullable||"function"==typeof(l=d.defaults[c!==r?c:r]=a.defaultValue!==e?a.defaultValue:N[o.toLowerCase()])&&m.push(r),i.id===r&&(d._defaultId=l),d.defaults[c!==r?c:r]=l,a.parse=a.parse||L[o];return m.length>0&&(d._initializers=m),(s=t.extend(d)).define=function(t){return W.define(s,t)},d.fields&&(s.fields=d.fields,s.idField=d.idField),s};var U={selector:function(t){return p(t)?t:x(t)},compare:function(t){var e=this.selector(t);return function(t,i){return t=e(t),i=e(i),null==t&&null==i?0:null==t?-1:null==i?1:t.localeCompare?t.localeCompare(i):t>i?1:t<i?-1:0}},create:function(t){var e=t.compare||this.compare(t.field);return"desc"==t.dir?function(t,i){return e(i,t,!0)}:e},combine:function(t){return function(e,i){var n,s,r=t[0](e,i);for(n=1,s=t.length;n<s;n++)r=r||t[n](e,i);return r}}},q=n({},U,{asc:function(t){var e=this.selector(t);return function(t,i){var n=e(t),s=e(i);return n&&n.getTime&&s&&s.getTime&&(n=n.getTime(),s=s.getTime()),n===s?t.__position-i.__position:null==n?-1:null==s?1:n.localeCompare?n.localeCompare(s):n>s?1:-1}},desc:function(t){var e=this.selector(t);return function(t,i){var n=e(t),s=e(i);return n&&n.getTime&&s&&s.getTime&&(n=n.getTime(),s=s.getTime()),n===s?t.__position-i.__position:null==n?1:null==s?-1:s.localeCompare?s.localeCompare(n):n<s?1:-1}},create:function(t){return this[t.dir](t.field)}});i=function(t,e){var i,n=t.length,s=new Array(n);for(i=0;i<n;i++)s[i]=e(t[i],i,t);return s};var j=function(){function t(t){return"string"==typeof t&&(t=t.replace(/[\r\n]+/g,"")),JSON.stringify(t)}function e(e){return function(i,n,s){return n+="",s&&(i="("+i+" || '').toLowerCase()",n=n.toLowerCase()),e(i,t(n),s)}}function i(e,i,n,s){if(null!=n){if(typeof n===g){var r=P.exec(n);r?n=new Date(+r[1]):s?(n=t(n.toLowerCase()),i="(("+i+" || '')+'').toLowerCase()"):n=t(n)}n.getTime&&(i="("+i+"&&"+i+".getTime?"+i+".getTime():"+i+")",n=n.getTime())}return i+" "+e+" "+n}function n(t){for(var e="/^",i=!1,n=0;n<t.length;++n){var s=t.charAt(n);if(i)e+="\\"+s;else{if("~"==s){i=!0;continue}"*"==s?e+=".*":"?"==s?e+=".":".+^$()[]{}|\\/\n\r\u2028\u2029 ".indexOf(s)>=0?e+="\\"+s:e+=s}i=!1}return e+"$/"}return{quote:function(e){return e&&e.getTime?"new Date("+e.getTime()+")":t(e)},eq:function(t,e,n){return i("==",t,e,n)},neq:function(t,e,n){return i("!=",t,e,n)},gt:function(t,e,n){return i(">",t,e,n)},gte:function(t,e,n){return i(">=",t,e,n)},lt:function(t,e,n){return i("<",t,e,n)},lte:function(t,e,n){return i("<=",t,e,n)},startswith:e(function(t,e){return t+".lastIndexOf("+e+", 0) == 0"}),doesnotstartwith:e(function(t,e){return t+".lastIndexOf("+e+", 0) == -1"}),endswith:e(function(t,e){return t+".indexOf("+e+", "+t+".length - "+(e?e.length-2:0)+") >= 0"}),doesnotendwith:e(function(t,e){return t+".indexOf("+e+", "+t+".length - "+(e?e.length-2:0)+") < 0"}),contains:e(function(t,e){return t+".indexOf("+e+") >= 0"}),doesnotcontain:e(function(t,e){return t+".indexOf("+e+") == -1"}),matches:e(function(t,e){return n(e=e.substring(1,e.length-1))+".test("+t+")"}),doesnotmatch:e(function(t,e){return"!"+n(e=e.substring(1,e.length-1))+".test("+t+")"}),isempty:function(t){return t+" === ''"},isnotempty:function(t){return t+" !== ''"},isnull:function(t){return"("+t+" == null)"},isnotnull:function(t){return"("+t+" != null)"},isnullorempty:function(t){return"("+t+" === null) || ("+t+" === '')"},isnotnullorempty:function(t){return"("+t+" !== null) && ("+t+" !== '')"}}}();function G(t){this.data=t||[]}function Y(t,i){if(t){var n=typeof t===g?{field:t,dir:i}:t,s=o(n)?n:n!==e?[n]:[];return l(s,function(t){return!!t.dir})}}G.filterExpr=function(t){var i,n,s,r,a,o,l=[],h=[],u=[],c=t.filters;for(i=0,n=c.length;i<n;i++)a=(s=c[i]).field,o=s.operator,s.filters?(s=(r=G.filterExpr(s)).expression.replace(/__o\[(\d+)\]/g,function(t,e){return e=+e,"__o["+(u.length+e)+"]"}).replace(/__f\[(\d+)\]/g,function(t,e){return e=+e,"__f["+(h.length+e)+"]"}),u.push.apply(u,r.operators),h.push.apply(h,r.fields)):("function"==typeof a?(r="__f["+h.length+"](d)",h.push(a)):r=d.expr(a),"function"==typeof o?(s="__o["+u.length+"]("+r+", "+j.quote(s.value)+")",u.push(o)):s=j[(o||"eq").toLowerCase()](r,s.value,s.ignoreCase===e||s.ignoreCase)),l.push(s);return{expression:"("+l.join({and:" && ",or:" || "}[t.logic])+")",fields:h,operators:u}};var $={"==":"eq",equals:"eq",isequalto:"eq",equalto:"eq",equal:"eq","!=":"neq",ne:"neq",notequals:"neq",isnotequalto:"neq",notequalto:"neq",notequal:"neq","<":"lt",islessthan:"lt",lessthan:"lt",less:"lt","<=":"lte",le:"lte",islessthanorequalto:"lte",lessthanequal:"lte",">":"gt",isgreaterthan:"gt",greaterthan:"gt",greater:"gt",">=":"gte",isgreaterthanorequalto:"gte",greaterthanequal:"gte",ge:"gte",notsubstringof:"doesnotcontain",isnull:"isnull",isempty:"isempty",isnotempty:"isnotempty"};function K(t){if(t&&!a(t))return!o(t)&&t.filters||(t={logic:"and",filters:o(t)?t:[t]}),function t(e){var i,n,s,r,a=e.filters;if(a)for(i=0,n=a.length;i<n;i++)(r=(s=a[i]).operator)&&typeof r===g&&(s.operator=$[r.toLowerCase()]||r),t(s)}(t),t}function J(t){return a(t=t||{})?{logic:"and",filters:[]}:K(t)}function Q(t,e){return e.logic||t.field>e.field?1:t.field<e.field?-1:0}function X(t,n){var s=typeof t===g?{field:t,dir:n}:t,r=o(s)?s:s!==e?[s]:[];return i(r,function(t){return{field:t.field,dir:t.dir||"asc",aggregates:t.aggregates}})}function Z(t,e,i,n,s,r){var a,o,l,h=(e=e||[]).length;for(a=0;a<h;a++){l=(o=e[a]).aggregate;var u=o.field;t[u]=t[u]||{},r[u]=r[u]||{},r[u][l]=r[u][l]||{},t[u][l]=tt[l.toLowerCase()](t[u][l],i,d.accessor(u),n,s,r[u][l])}}G.normalizeFilter=K,G.compareFilters=function t(e,i){if(e=J(e),i=J(i),e.logic!==i.logic)return!1;var n,s,r,a,o=(e.filters||[]).slice(),l=(i.filters||[]).slice();if(o.length!==l.length)return!1;o=o.sort(Q),l=l.sort(Q);for(var h=0;h<o.length;h++)if(n=o[h],s=l[h],n.logic&&s.logic){if(!t(n,s))return!1}else if(r=n,a=s,r.logic||a.logic||r.field!==a.field||r.value!==a.value||r.operator!==a.operator)return!1;return!0},G.prototype={toArray:function(){return this.data},range:function(t,e){return new G(this.data.slice(t,t+e))},skip:function(t){return new G(this.data.slice(t))},take:function(t){return new G(this.data.slice(0,t))},select:function(t){return new G(i(this.data,t))},order:function(t,e,i){var n={dir:e};return t&&(t.compare?n.compare=t.compare:n.field=t),new G(i?this.data.sort(U.create(n)):this.data.slice(0).sort(U.create(n)))},orderBy:function(t,e){return this.order(t,"asc",e)},orderByDescending:function(t,e){return this.order(t,"desc",e)},sort:function(t,e,i,n){var s,r,a=Y(t,e),o=[];if(i=i||U,a.length){for(s=0,r=a.length;s<r;s++)o.push(i.create(a[s]));return this.orderBy({compare:i.combine(o)},n)}return this},filter:function(t){var e,i,n,s,r,a,o,l,h=this.data,u=[];if(!(t=K(t))||0===t.filters.length)return this;for(s=G.filterExpr(t),a=s.fields,o=s.operators,r=l=new Function("d, __f, __o","return "+s.expression),(a.length||o.length)&&(l=function(t){return r(t,a,o)}),e=0,n=h.length;e<n;e++)l(i=h[e])&&u.push(i);return new G(u)},group:function(t,e){t=X(t||[]),e=e||this.data;var i,n=new G(this.data);return t.length>0&&(i=t[0],n=n.groupBy(i).select(function(n){var s=new G(e).filter([{field:n.field,operator:"eq",value:n.value,ignoreCase:!1}]);return{field:n.field,value:n.value,items:t.length>1?new G(n.items).group(t.slice(1),s.toArray()).toArray():n.items,hasSubgroups:t.length>1,aggregates:s.aggregate(i.aggregates)}})),n},groupBy:function(t){if(a(t)||!this.data.length)return new G([]);var e,i,n,s,r,o,l=t.field,h=this._sortForGrouping(l,t.dir||"asc"),u=d.accessor(l),c=u.get(h[0],l),p={field:l,value:c,items:[]},f=[p];for(n=0,s=h.length;n<s;n++)e=h[n],r=c,o=i=u.get(e,l),(r&&r.getTime&&o&&o.getTime?r.getTime()===o.getTime():r===o)||(p={field:l,value:c=i,items:[]},f.push(p)),p.items.push(e);return new G(f)},_sortForGrouping:function(t,e){var i,n,s=this.data;if(!H){for(i=0,n=s.length;i<n;i++)s[i].__position=i;for(i=0,n=(s=new G(s).sort(t,e,q).toArray()).length;i<n;i++)delete s[i].__position;return s}return this.sort(t,e).toArray()},aggregate:function(t){var e,i,n={},s={};if(t&&t.length)for(e=0,i=this.data.length;e<i;e++)Z(n,t,this.data[e],e,i,s);return n}};var tt={sum:function(t,e,i){var n=i.get(e);return et(t)?et(n)&&(t+=n):t=n,t},count:function(t){return(t||0)+1},average:function(t,i,n,s,r,a){var o=n.get(i);return a.count===e&&(a.count=0),et(t)?et(o)&&(t+=o):t=o,et(o)&&a.count++,s==r-1&&et(t)&&(t/=a.count),t},max:function(t,e,i){var n=i.get(e);return et(t)||it(t)||(t=n),t<n&&(et(n)||it(n))&&(t=n),t},min:function(t,e,i){var n=i.get(e);return et(t)||it(t)||(t=n),t>n&&(et(n)||it(n))&&(t=n),t}};function et(t){return"number"==typeof t&&!isNaN(t)}function it(t){return t&&t.getTime}function nt(t){var e,i=t.length,n=new Array(i);for(e=0;e<i;e++)n[e]=t[e].toJSON();return n}G.process=function(t,i,n){i=i||{};var s,r=new G(t),a=i.group,o=X(a||[]).concat(Y(i.sort||[])),l=i.filterCallback,h=i.filter,u=i.skip,c=i.take;return o&&n&&(r=r.sort(o,e,e,n)),h&&(r=r.filter(h),l&&(r=l(r)),s=r.toArray().length),o&&!n&&(r=r.sort(o),a&&(t=r.toArray())),u!==e&&c!==e&&(r=r.range(u,c)),a&&(r=r.group(a,t)),{total:s,data:r.toArray()}};var st=m.extend({init:function(t){this.data=t.data},read:function(t){t.success(this.data)},update:function(t){t.success(t.data)},create:function(t){t.success(t.data)},destroy:function(t){t.success(t.data)}}),rt=m.extend({init:function(t){var e;t=this.options=n({},this.options,t),u(y,function(e,i){typeof t[i]===g&&(t[i]={url:t[i]})}),this.cache=t.cache?at.create(t.cache):{find:c,add:c},e=t.parameterMap,t.submit&&(this.submit=t.submit),p(t.push)&&(this.push=t.push),this.push||(this.push=k),this.parameterMap=p(e)?e:function(t){var i={};return u(t,function(t,n){t in e&&(t=e[t],r(t)&&(n=t.value(n),t=t.key)),i[t]=n}),i}},options:{parameterMap:k},create:function(t){return h(this.setup(t,"create"))},read:function(i){var n,s,r=this.cache;i=this.setup(i,"read"),n=i.success||c,i.error,(s=r.find(i.data))!==e?n(s):(i.success=function(t){r.add(i.data,t),n(t)},t.ajax(i))},update:function(t){return h(this.setup(t,"update"))},destroy:function(t){return h(this.setup(t,"destroy"))},setup:function(t,e){t=t||{};var i,s=this.options[e],r=p(s.data)?s.data(t.data):s.data;return t=n(!0,{},s,t),i=n(!0,{},r,t.data),t.data=this.parameterMap(i,e),p(t.url)&&(t.url=t.url(i)),t}}),at=m.extend({init:function(){this._store={}},add:function(t,i){t!==e&&(this._store[C(t)]=i)},find:function(t){return this._store[C(t)]},clear:function(){this._store={}},remove:function(t){delete this._store[C(t)]}});function ot(t,e,i,n,s){var r,a,o,l,h,u={};for(l=0,h=t.length;l<h;l++){r=t[l];for(a in e)(o=s[a])&&o!==a&&(u[o]||(u[o]=d.setter(o)),u[o](r,e[a](r)),delete r[a])}}function lt(t,e,i,n,s){var r,a,o,l,h;for(l=0,h=t.length;l<h;l++){r=t[l];for(a in e)r[a]=i._parse(a,e[a](r)),(o=s[a])&&o!==a&&delete r[o]}}function ht(t,e,i,n,s){var r,a,o,l;for(a=0,l=t.length;a<l;a++)(o=n[(r=t[a]).field])&&o!=r.field&&(r.field=o),r.value=i._parse(r.field,r.value),r.hasSubgroups?ht(r.items,e,i,n,s):lt(r.items,e,i,0,s)}function ut(t,e,i,n,s,r){return function(a){return a=t(a),ct(e,i,n,s,r)(a)}}function ct(t,e,i,n,s){return function(r){return r&&!a(i)&&("[object Array]"===M.call(r)||r instanceof z||(r=[r]),e(r,i,new t,n,s)),r||[]}}at.create=function(t){var e={inmemory:function(){return new at}};return r(t)&&p(t.find)?t:!0===t?new at:e[t]()};var dt=m.extend({init:function(t){var e,i,n,a;t=t||{};for(e in t)i=t[e],this[e]=typeof i===g?x(i):i;a=t.modelBase||W,r(this.model)&&(this.model=n=a.define(this.model));var o=s(this.data,this);if(this._dataAccessFunction=o,this.model){var l,h,c=s(this.groups,this),d=s(this.serialize,this),p={},f={},m={},_={},v=!1;(n=this.model).fields&&(u(n.fields,function(t,e){var i;l=t,r(e)&&e.field?l=e.field:typeof e===g&&(l=e),r(e)&&e.from&&(i=e.from),v=v||i&&i!==t||l!==t,h=i||l,f[t]=-1!==h.indexOf(".")?x(h,!0):x(h),m[t]=x(t),p[i||l]=t,_[t]=i||l}),!t.serialize&&v&&(this.serialize=ut(d,n,ot,m,p,_))),this._dataAccessFunction=o,this._wrapDataAccessBase=ct(n,lt,f,p,_),this.data=ut(o,n,lt,f,p,_),this.groups=ut(c,n,ht,f,p,_)}},errors:function(t){return t?t.errors:null},parse:k,data:k,total:function(t){return t.length},groups:k,aggregates:function(){return{}},serialize:function(t){return t}});function pt(t){var e,i,n,s,r=[];for(e=0,i=t.length;e<i;e++){var a=t.at(e);if(a.hasSubgroups)r=r.concat(pt(a.items));else for(n=a.items,s=0;s<n.length;s++)r.push(n.at(s))}return r}function ft(t,e,i,n){for(var s=0;s<t.length&&t[s].data!==e&&!mt(t[s].data,i,n);s++);}function mt(t,e,i){for(var n=0,s=t.length;n<s;n++){if(t[n]&&t[n].hasSubgroups)return mt(t[n].items,e,i);if(t[n]===e||t[n]===i)return t[n]=i,!0}}function gt(t,e){var i,n,s=t.length;for(n=0;n<s;n++)if((i=t[n]).uid&&i.uid==e.uid)return t.splice(n,1),i}function _t(t,e){return e?wt(t,function(t){return t.uid&&t.uid==e.uid||t[e.idField]===e.id&&e.id!==e._defaultId}):-1}function vt(t,e){return e?wt(t,function(t){return t.uid==e.uid}):-1}function wt(t,e){var i,n;for(i=0,n=t.length;i<n;i++)if(e(t[i]))return i;return-1}function bt(t,e){if(t&&!a(t)){var i,n=t[e];return i=r(n)?n.from||n.field||e:t[e]||e,p(i)?e:i}return e}function yt(t,e){var i,n,s,r,a=[];for(i=0,n=t.length;i<n;i++){s={},r=t[i];for(var l in r)s[l]=r[l];s.field=bt(e.fields,s.field),s.aggregates&&o(s.aggregates)&&(s.aggregates=yt(s.aggregates,e)),a.push(s)}return a}var kt=f.extend({init:function(t){var i,r=this;if(t&&(i=t.data),t=r.options=n({},r.options,t),r._map={},r._prefetch={},r._data=[],r._pristineData=[],r._ranges=[],r._view=[],r._pristineTotal=0,r._destroyed=[],r._pageSize=t.pageSize,r._page=t.page||(t.pageSize?1:e),r._sort=Y(t.sort),r._filter=K(t.filter),r._group=X(t.group),r._aggregate=t.aggregate,r._total=t.total,r._shouldDetachObservableParents=!0,f.fn.init.call(r),r.transport=xt.create(t,i,r),p(r.transport.push)&&r.transport.push({pushCreate:s(r._pushCreate,r),pushUpdate:s(r._pushUpdate,r),pushDestroy:s(r._pushDestroy,r)}),null!=t.offlineStorage)if("string"==typeof t.offlineStorage){var a=t.offlineStorage;r._storage={getItem:function(){return JSON.parse(localStorage.getItem(a))},setItem:function(t){localStorage.setItem(a,C(r.reader.serialize(t)))}}}else r._storage=t.offlineStorage;r.reader=new d.data.readers[t.schema.type||"json"](t.schema),r.reader.model,r._detachObservableParents(),r._data=r._observe(r._data),r._online=!0,r.bind(["push","error",_,v,"sync",b,w],t)},options:{data:null,schema:{modelBase:W},offlineStorage:null,serverSorting:!1,serverPaging:!1,serverFiltering:!1,serverGrouping:!1,serverAggregates:!1,batch:!1,inPlaceSort:!1},clone:function(){return this},online:function(i){return i!==e?this._online!=i&&(this._online=i,i)?this.sync():t.Deferred().resolve().promise():this._online},offlineData:function(t){return null==this.options.offlineStorage?null:t!==e?this._storage.setItem(t):this._storage.getItem()||[]},_isServerGrouped:function(){var t=this.group()||[];return this.options.serverGrouping&&t.length},_pushCreate:function(t){this._push(t,"pushCreate")},_pushUpdate:function(t){this._push(t,"pushUpdate")},_pushDestroy:function(t){this._push(t,"pushDestroy")},_push:function(t,e){var i=this._readData(t);i||(i=t),this[e](i)},_flatData:function(t,e){if(t){if(this._isServerGrouped())return pt(t);if(!e)for(var i=0;i<t.length;i++)t.at(i)}return t},parent:c,get:function(t){var e,i,n=this._flatData(this._data,this.options.useRanges);for(e=0,i=n.length;e<i;e++)if(n[e].id==t)return n[e]},getByUid:function(t){return this._getByUid(t,this._data)},_getByUid:function(t,e){var i,n,s=this._flatData(e,this.options.useRanges);if(s)for(i=0,n=s.length;i<n;i++)if(s[i].uid==t)return s[i]},indexOf:function(t){return vt(this._data,t)},at:function(t){return this._data.at(t)},data:function(t){if(t===e){if(this._data)for(var i=0;i<this._data.length;i++)this._data.at(i);return this._data}this._detachObservableParents(),this._data=this._observe(t),this._pristineData=t.slice(0),this._storeData(),this._ranges=[],this.trigger("reset"),this._addRange(this._data),this._total=this._data.length,this._pristineTotal=this._total,this._process(this._data)},view:function(t){if(t===e)return this._view;this._view=this._observeView(t)},_observeView:function(t){var i=this;!function t(i,n,s,r,a){for(var o=0,l=i.length;o<l;o++){var h=i[o];if(h&&!(h instanceof r))if(h.hasSubgroups===e||a){for(var u=0;u<n.length;u++)if(n[u]===h){i[o]=n.at(u),ft(s,n,h,i[o]);break}}else t(h.items,n,s,r,a)}}(t,i._data,i._ranges,i.reader.model||B,i._isServerGrouped());var n=new V(t,i.reader.model);return n.parent=function(){return i.parent()},n},flatView:function(){return(this.group()||[]).length?pt(this._view):this._view},add:function(t){return this.insert(this._data.length,t)},_createNewModel:function(t){return this.reader.model?new this.reader.model(t):t instanceof B?t:new B(t)},insert:function(t,e){return e||(e=t,t=0),e instanceof W||(e=this._createNewModel(e)),this._isServerGrouped()?this._data.splice(t,0,this._wrapInEmptyGroup(e)):this._data.splice(t,0,e),this._insertModelInRange(t,e),e},pushInsert:function(e,i){var n=this._getCurrentRangeSpan();i||(i=e,e=0),o(i)||(i=[i]);var s=[],r=this.options.autoSync;this.options.autoSync=!1;try{for(var a=0;a<i.length;a++){var l=i[a],h=this.insert(e,l);s.push(h);var u=h.toJSON();this._isServerGrouped()&&(u=this._wrapInEmptyGroup(u)),this._pristineData.push(u),n&&n.length&&t(n).last()[0].pristineData.push(u),e++}}finally{this.options.autoSync=r}s.length&&this.trigger("push",{type:"create",items:s})},pushCreate:function(t){this.pushInsert(this._data.length,t)},pushUpdate:function(t){o(t)||(t=[t]);for(var e=[],i=0;i<t.length;i++){var n=t[i],s=this._createNewModel(n),r=this.get(s.id);r?(e.push(r),r.accept(n),r.trigger(_),this._updatePristineForModel(r,n)):this.pushCreate(n)}e.length&&this.trigger("push",{type:"update",items:e})},pushDestroy:function(t){var e=this._removeItems(t);e.length&&this.trigger("push",{type:"destroy",items:e})},_removeItems:function(t){o(t)||(t=[t]);var e=[],i=this.options.autoSync;this.options.autoSync=!1;try{for(var n=0;n<t.length;n++){var s=t[n],r=this._createNewModel(s),a=!1;this._eachItem(this._data,function(t){for(var i=0;i<t.length;i++){var n=t.at(i);if(n.id===r.id){e.push(n),t.splice(i,1),a=!0;break}}}),a&&(this._removePristineForModel(r),this._destroyed.pop())}}finally{this.options.autoSync=i}return e},remove:function(t){var e,i=this,n=i._isServerGrouped();return this._eachItem(i._data,function(s){if((e=gt(s,t))&&n)return e.isNew&&e.isNew()||i._destroyed.push(e),!0}),this._removeModelFromRanges(t),t},destroyed:function(){return this._destroyed},created:function(){var t,e,i=[],n=this._flatData(this._data,this.options.useRanges);for(t=0,e=n.length;t<e;t++)n[t].isNew&&n[t].isNew()&&i.push(n[t]);return i},updated:function(){var t,e,i=[],n=this._flatData(this._data,this.options.useRanges);for(t=0,e=n.length;t<e;t++)n[t].isNew&&!n[t].isNew()&&n[t].dirty&&i.push(n[t]);return i},sync:function(){var e=this,i=[],n=[],s=e._destroyed,r=t.Deferred().resolve().promise();if(e.online()){if(!e.reader.model)return r;i=e.created(),n=e.updated();var a=[];e.options.batch&&e.transport.submit?a=e._sendSubmit(i,n,s):(a.push.apply(a,e._send("create",i)),a.push.apply(a,e._send("update",n)),a.push.apply(a,e._send("destroy",s))),r=t.when.apply(null,a).then(function(){var t,i;for(t=0,i=arguments.length;t<i;t++)arguments[t]&&e._accept(arguments[t]);e._storeData(!0),e._change({action:"sync"}),e.trigger("sync")})}else e._storeData(!0),e._change({action:"sync"});return r},cancelChanges:function(t){t instanceof d.data.Model?this._cancelModel(t):(this._destroyed=[],this._detachObservableParents(),this._data=this._observe(this._pristineData),this.options.serverPaging&&(this._total=this._pristineTotal),this._ranges=[],this._addRange(this._data,0),this._change(),this._markOfflineUpdatesAsDirty())},_markOfflineUpdatesAsDirty:function(){null!=this.options.offlineStorage&&this._eachItem(this._data,function(t){for(var e=0;e<t.length;e++){var i=t.at(e);"update"!=i.__state__&&"create"!=i.__state__||(i.dirty=!0)}})},hasChanges:function(){var t,e,i=this._flatData(this._data,this.options.useRanges);if(this._destroyed.length)return!0;for(t=0,e=i.length;t<e;t++)if(i[t].isNew&&i[t].isNew()||i[t].dirty)return!0;return!1},_accept:function(e){var i,n=e.models,s=e.response,r=0,l=this._isServerGrouped(),h=this._pristineData,u=e.type;if(this.trigger(b,{response:s,type:u}),s&&!a(s)){if(s=this.reader.parse(s),this._handleCustomErrors(s))return;s=this.reader.data(s),o(s)||(s=[s])}else s=t.map(n,function(t){return t.toJSON()});for("destroy"===u&&(this._destroyed=[]),r=0,i=n.length;r<i;r++)"destroy"!==u?(n[r].accept(s[r]),"create"===u?h.push(l?this._wrapInEmptyGroup(n[r]):s[r]):"update"===u&&this._updatePristineForModel(n[r],s[r])):this._removePristineForModel(n[r])},_updatePristineForModel:function(t,e){this._executeOnPristineForModel(t,function(t,i){d.deepExtend(i[t],e)})},_executeOnPristineForModel:function(t,e){this._eachPristineItem(function(i){var n=_t(i,t);if(n>-1)return e(n,i),!0})},_removePristineForModel:function(t){this._executeOnPristineForModel(t,function(t,e){e.splice(t,1)})},_readData:function(t){return(this._isServerGrouped()?this.reader.groups:this.reader.data).call(this.reader,t)},_eachPristineItem:function(t){var e=this,i=e.options,n=e._getCurrentRangeSpan();e._eachItem(e._pristineData,t),i.serverPaging&&i.useRanges&&u(n,function(i,n){e._eachItem(n.pristineData,t)})},_eachItem:function(t,e){t&&t.length&&(this._isServerGrouped()?function t(e,i){for(var n=0,s=e.length;n<s;n++)if(e[n].hasSubgroups){if(t(e[n].items,i))return!0}else if(i(e[n].items,e[n]))return!0}(t,e):e(t))},_pristineForModel:function(t){var e,i;return this._eachPristineItem(function(n){if((i=_t(n,t))>-1)return e=n[i],!0}),e},_cancelModel:function(t){var e=this,i=this._pristineForModel(t);this._eachItem(this._data,function(n){var s=vt(n,t);s>=0&&(!i||t.isNew()&&!i.__state__?(n.splice(s,1),e._removeModelFromRanges(t)):(n[s].accept(i),"update"==i.__state__&&(n[s].dirty=!0)))})},_submit:function(e,i){var s=this;s.trigger(v,{type:"submit"}),s.trigger(w),s.transport.submit(n({success:function(i,n){var s=t.grep(e,function(t){return t.type==n})[0];s&&s.resolve({response:i,models:s.models,type:n})},error:function(t,i,n){for(var r=0;r<e.length;r++)e[r].reject(t);s.error(t,i,n)}},i))},_sendSubmit:function(e,i,n){var s=[];return this.options.batch&&(e.length&&s.push(t.Deferred(function(t){t.type="create",t.models=e})),i.length&&s.push(t.Deferred(function(t){t.type="update",t.models=i})),n.length&&s.push(t.Deferred(function(t){t.type="destroy",t.models=n})),this._submit(s,{data:{created:this.reader.serialize(nt(e)),updated:this.reader.serialize(nt(i)),destroyed:this.reader.serialize(nt(n))}})),s},_promise:function(e,i,s){var r=this;return t.Deferred(function(t){r.trigger(v,{type:s}),r.trigger(w),r.transport[s].call(r.transport,n({success:function(e){t.resolve({response:e,models:i,type:s})},error:function(e,i,n){t.reject(e),r.error(e,i,n)}},e))}).promise()},_send:function(t,e){var i,n,s=[],r=this.reader.serialize(nt(e));if(this.options.batch)e.length&&s.push(this._promise({data:{models:r}},e,t));else for(i=0,n=e.length;i<n;i++)s.push(this._promise({data:r[i]},[e[i]],t));return s},read:function(e){var i=this,n=i._params(e),s=t.Deferred();return i._queueRequest(n,function(){var t=i.trigger(v,{type:"read"});t?(i._dequeueRequest(),s.resolve(t)):(i.trigger(w),i._ranges=[],i.trigger("reset"),i.online()?i.transport.read({data:n,success:function(t){i._ranges=[],i.success(t,n),s.resolve()},error:function(){var t=O.call(arguments);i.error.apply(i,t),s.reject.apply(s,t)}}):null!=i.options.offlineStorage&&(i.success(i.offlineData(),n),s.resolve()))}),s.promise()},_readAggregates:function(t){return this.reader.aggregates(t)},success:function(t){var e=this.options;if(this.trigger(b,{response:t,type:"read"}),this.online()){if(t=this.reader.parse(t),this._handleCustomErrors(t))return void this._dequeueRequest();this._total=this.reader.total(t),this._pageSize>this._total&&(this._pageSize=this._total,this.options.pageSize&&this.options.pageSize>this._pageSize&&(this._pageSize=this.options.pageSize)),this._aggregate&&e.serverAggregates&&(this._aggregateResult=this._readAggregates(t)),t=this._readData(t),this._destroyed=[]}else{t=this._readData(t);var i,n=[],s={},r=this.reader.model,a=r?r.idField:"id";for(i=0;i<this._destroyed.length;i++){var o=this._destroyed[i][a];s[o]=o}for(i=0;i<t.length;i++){var l=t[i];"destroy"==l.__state__?s[l[a]]||this._destroyed.push(this._createNewModel(l)):n.push(l)}t=n,this._total=t.length}if(this._pristineTotal=this._total,this._pristineData=t.slice(0),this._detachObservableParents(),this.options.endless){this._data.unbind(_,this._changeHandler),this._isServerGrouped()&&this._data[this._data.length-1].value===t[0].value&&(function t(e,i){var n,s;if(i.items&&i.items.length)for(var r=0;r<i.items.length;r++)n=e.items[r],s=i.items[r],n&&s?n.hasSubgroups?t(n,s):n.field&&n.value==s.value?n.items.push.apply(n.items,s.items):e.items.push.apply(e.items,[s]):s&&e.items.push.apply(e.items,[s])}(this._data[this._data.length-1],t[0]),t.shift()),t=this._observe(t);for(var h=0;h<t.length;h++)this._data.push(t[h]);this._data.bind(_,this._changeHandler)}else this._data=this._observe(t);this._markOfflineUpdatesAsDirty(),this._storeData(),this._addRange(this._data),this._process(this._data),this._dequeueRequest()},_detachObservableParents:function(){if(this._data&&this._shouldDetachObservableParents)for(var t=0;t<this._data.length;t++)this._data[t].parent&&(this._data[t].parent=c)},_storeData:function(t){var e=this._isServerGrouped(),i=this.reader.model;if(null!=this.options.offlineStorage){for(var n=function t(n){for(var s=[],r=0;r<n.length;r++){var a=n.at(r),o=a.toJSON();e&&a.items?o.items=t(a.items):(o.uid=a.uid,i&&(a.isNew()?o.__state__="create":a.dirty&&(o.__state__="update"))),s.push(o)}return s}(this._data),s=[],r=0;r<this._destroyed.length;r++){var a=this._destroyed[r].toJSON();a.__state__="destroy",s.push(a)}this.offlineData(n.concat(s)),t&&(this._pristineData=this.reader._wrapDataAccessBase(n))}},_addRange:function(t,e){var i=void 0!==e?e:this._skip||0,n=i+this._flatData(t,!0).length;this._ranges.push({start:i,end:n,data:t,pristineData:t.toJSON(),timestamp:this._timeStamp()}),this._sortRanges()},_sortRanges:function(){this._ranges.sort(function(t,e){return t.start-e.start})},error:function(t,e,i){this._dequeueRequest(),this.trigger(b,{}),this.trigger("error",{xhr:t,status:e,errorThrown:i})},_params:function(t){var e=n({take:this.take(),skip:this.skip(),page:this.page(),pageSize:this.pageSize(),sort:this._sort,filter:this._filter,group:this._group,aggregate:this._aggregate},t);return this.options.serverPaging||(delete e.take,delete e.skip,delete e.page,delete e.pageSize),this.options.serverGrouping?this.reader.model&&e.group&&(e.group=yt(e.group,this.reader.model)):delete e.group,this.options.serverFiltering?this.reader.model&&e.filter&&(e.filter=function t(e,i){var n,s,r={};for(var a in e)"filters"!==a&&(r[a]=e[a]);if(e.filters)for(r.filters=[],n=0,s=e.filters.length;n<s;n++)r.filters[n]=t(e.filters[n],i);else r.field=bt(i.fields,r.field);return r}(e.filter,this.reader.model)):delete e.filter,this.options.serverSorting?this.reader.model&&e.sort&&(e.sort=yt(e.sort,this.reader.model)):delete e.sort,this.options.serverAggregates?this.reader.model&&e.aggregate&&(e.aggregate=yt(e.aggregate,this.reader.model)):delete e.aggregate,e},_queueRequest:function(t,i){this._requestInProgress?this._pending={callback:s(i,this),options:t}:(this._requestInProgress=!0,this._pending=e,i())},_dequeueRequest:function(){this._requestInProgress=!1,this._pending&&this._queueRequest(this._pending.options,this._pending.callback)},_handleCustomErrors:function(t){if(this.reader.errors){var e=this.reader.errors(t);if(e)return this.trigger("error",{xhr:null,status:"customerror",errorThrown:"custom error",errors:e}),!0}return!1},_shouldWrap:function(t){var e=this.reader.model;return!(!e||!t.length||t[0]instanceof e)},_observe:function(t){var e=this,i=e.reader.model;return e._shouldDetachObservableParents=!0,t instanceof z?(e._shouldDetachObservableParents=!1,e._shouldWrap(t)&&(t.type=e.reader.model,t.wrapAll(t,t))):(t=new(e.pageSize()&&!e.options.serverPaging?V:z)(t,e.reader.model)).parent=function(){return e.parent()},e._isServerGrouped()&&function t(e,i){var n,s,r;if(i)for(n=0,s=e.length;n<s;n++)(r=e.at(n)).hasSubgroups?t(r.items,i):r.items=new V(r.items,i)}(t,i),e._changeHandler&&e._data&&e._data instanceof z?e._data.unbind(_,e._changeHandler):e._changeHandler=s(e._change,e),t.bind(_,e._changeHandler)},_updateTotalForAction:function(t,e){var i=parseInt(this._total,10);et(this._total)||(i=parseInt(this._pristineTotal,10)),"add"===t?i+=e.length:"remove"===t?i-=e.length:"itemchange"===t||"sync"===t||this.options.serverPaging?"sync"===t&&(i=this._pristineTotal=parseInt(this._total,10)):i=this._pristineTotal,this._total=i},_change:function(t){var e,i,n=this,s=t?t.action:"";if("remove"===s)for(e=0,i=t.items.length;e<i;e++)t.items[e].isNew&&t.items[e].isNew()||n._destroyed.push(t.items[e]);if(!n.options.autoSync||"add"!==s&&"remove"!==s&&"itemchange"!==s)n._updateTotalForAction(s,t?t.items:[]),n._process(n._data,t);else{var r=function(e){"sync"===e.action&&(n.unbind("change",r),n._updateTotalForAction(s,t.items))};n.first("change",r),n.sync()}},_calculateAggregates:function(t,e){e=e||{};var i=new G(t),n=e.aggregate,s=e.filter;return s&&(i=i.filter(s)),i.aggregate(n)},_process:function(t,i){var n,s={};!0!==this.options.serverPaging&&(s.skip=this._skip,s.take=this._take||this._pageSize,s.skip===e&&this._page!==e&&this._pageSize!==e&&(s.skip=(this._page-1)*this._pageSize),this.options.useRanges&&(s.skip=this.currentRangeStart())),!0!==this.options.serverSorting&&(s.sort=this._sort),!0!==this.options.serverFiltering&&(s.filter=this._filter),!0!==this.options.serverGrouping&&(s.group=this._group),!0!==this.options.serverAggregates&&(s.aggregate=this._aggregate,this._aggregateResult=this._calculateAggregates(t,s)),n=this._queryProcess(t,s),this.view(n.data),n.total===e||this.options.serverFiltering||(this._total=n.total),(i=i||{}).items=i.items||this._view,this.trigger(_,i)},_queryProcess:function(t,e){return this.options.inPlaceSort?G.process(t,e,this.options.inPlaceSort):G.process(t,e)},_mergeState:function(t){var i;return t!==e&&(this._pageSize=t.pageSize,this._page=t.page,this._sort=t.sort,this._filter=t.filter,this._group=t.group,this._aggregate=t.aggregate,this._skip=this._currentRangeStart=t.skip,this._take=t.take,this._skip===e&&(this._skip=this._currentRangeStart=this.skip(),t.skip=this.skip()),this._take===e&&this._pageSize!==e&&(this._take=this._pageSize,t.take=this._take),t.sort&&(this._sort=t.sort=Y(t.sort)),t.filter&&(this._filter=t.filter=K(t.filter)),t.group&&(this._group=t.group=X(t.group)),t.aggregate&&(this._aggregate=t.aggregate=(i=t.aggregate,o(i)?i:[i]))),t},query:function(i){var n;if(this.options.serverSorting||this.options.serverPaging||this.options.serverFiltering||this.options.serverGrouping||this.options.serverAggregates||(this._data===e||0===this._data.length)&&!this._destroyed.length){if(this.options.endless){var s=i.pageSize-this.pageSize();s>0?(s=this.pageSize(),i.page=i.pageSize/s,i.pageSize=s):(i.page=1,this.options.endless=!1)}return this.read(this._mergeState(i))}var r=this.trigger(v,{type:"read"});return r||(this.trigger(w),n=this._queryProcess(this._data,this._mergeState(i)),this.options.serverFiltering||(n.total!==e?this._total=n.total:this._total=this._data.length),this._aggregateResult=this._calculateAggregates(this._data,i),this.view(n.data),this.trigger(b,{type:"read"}),this.trigger(_,{items:n.data})),t.Deferred().resolve(r).promise()},fetch:function(t){var e=this;return this._query().then(function(i){!0!==i&&p(t)&&t.call(e)})},_query:function(t){return this.query(n({},{page:this.page(),pageSize:this.pageSize(),sort:this.sort(),filter:this.filter(),group:this.group(),aggregate:this.aggregate()},t))},next:function(t){var e=this.page(),i=this.total();if(t=t||{},e&&!(i&&e+1>this.totalPages()))return this._skip=this._currentRangeStart=e*this.take(),e+=1,t.page=e,this._query(t),e},prev:function(t){var e=this.page();if(t=t||{},e&&1!==e)return this._skip=this._currentRangeStart=this._skip-this.take(),e-=1,t.page=e,this._query(t),e},page:function(t){var i;return t!==e?(t=T.max(T.min(T.max(t,1),this.totalPages()),1),void this._query({page:t})):(i=this.skip())!==e?T.round((i||0)/(this.take()||1))+1:e},pageSize:function(t){if(t===e)return this.take();this._query({pageSize:t,page:1})},sort:function(t){if(t===e)return this._sort;this._query({sort:t})},filter:function(t){if(t===e)return this._filter;this.trigger("reset"),this._query({filter:t,page:1})},group:function(t){if(t===e)return this._group;this._query({group:t})},total:function(){return parseInt(this._total||0,10)},aggregate:function(t){if(t===e)return this._aggregate;this._query({aggregate:t})},aggregates:function(){var t=this._aggregateResult;return a(t)&&(t=this._emptyAggregates(this.aggregate())),t},_emptyAggregates:function(t){var e={};if(!a(t)){var i={};o(t)||(t=[t]);for(var n=0;n<t.length;n++)i[t[n].aggregate]=0,e[t[n].field]=i}return e},_wrapInEmptyGroup:function(t){var e,i,n,s=this.group();for(n=s.length-1;n>=0;n--)i=s[n],e={value:t.get(i.field),field:i.field,items:e?[e]:[t],hasSubgroups:!!e,aggregates:this._emptyAggregates(i.aggregates)};return e},totalPages:function(){var t=this.pageSize()||this.total();return T.ceil((this.total()||0)/t)},inRange:function(t,e){var i=T.min(t+e,this.total());return!this.options.serverPaging&&this._data.length>0||this._findRange(t,i).length>0},lastRange:function(){var t=this._ranges;return t[t.length-1]||{start:0,end:0,data:[]}},firstItemUid:function(){var t=this._ranges;return t.length&&t[0].data.length&&t[0].data[0].uid},enableRequestsInProgress:function(){this._skipRequestsInProgress=!1},_timeStamp:function(){return(new Date).getTime()},range:function(t,i,n){this._currentRequestTimeStamp=this._timeStamp(),this._skipRequestsInProgress=!0,t=T.min(t||0,this.total()),n=p(n)?n:c;var s,r=this,a=T.max(T.floor(t/i),0)*i,o=T.min(a+i,r.total());if((s=r._findRange(t,T.min(t+i,r.total()))).length||0===r.total())return r._processRangeData(s,t,i,a,o),void n();i!==e&&(r._rangeExists(a,o)?a<t&&r.prefetch(o,i,function(){r.range(t,i,n)}):r.prefetch(a,i,function(){t>a&&o<r.total()&&!r._rangeExists(o,T.min(o+i,r.total()))?r.prefetch(o,i,function(){r.range(t,i,n)}):r.range(t,i,n)}))},_findRange:function(t,i){var n,s,r,a,o,l,h,u,c,d,p,f=this._ranges,m=[],g=this.options,_=g.serverSorting||g.serverPaging||g.serverFiltering||g.serverGrouping||g.serverAggregates;for(s=0,p=f.length;s<p;s++)if(t>=(n=f[s]).start&&t<=n.end){for(d=0,r=s;r<p;r++)if(n=f[r],(c=this._flatData(n.data,!0)).length&&t+d>=n.start){if(l=n.data,h=n.end,!_){if(g.inPlaceSort)u=this._queryProcess(n.data,{filter:this.filter()});else{var v=X(this.group()||[]).concat(Y(this.sort()||[]));u=this._queryProcess(n.data,{sort:v,filter:this.filter()})}c=l=u.data,u.total!==e&&(h=u.total)}if(a=0,t+d>n.start&&(a=t+d-n.start),o=c.length,h>i&&(o-=h-i),d+=o-a,m=this._mergeGroups(m,l,a,o),i<=n.end&&d==i-t)return m}break}return[]},_mergeGroups:function(t,e,i,n){if(this._isServerGrouped()){var s,r=e.toJSON();return t.length&&(s=t[t.length-1]),function t(e,i,n,s){for(var r,a,o=0;i.length&&s;){var l=(a=(r=i[o]).items).length;if(e&&e.field===r.field&&e.value===r.value?(e.hasSubgroups&&e.items.length?t(e.items[e.items.length-1],r.items,n,s):(a=a.slice(n,n+s),e.items=e.items.concat(a)),i.splice(o--,1)):r.hasSubgroups&&a.length?(t(r,a,n,s),r.items.length||i.splice(o--,1)):(a=a.slice(n,n+s),r.items=a,r.items.length||i.splice(o--,1)),0===a.length?n-=l:(n=0,s-=a.length),++o>=i.length)break}o<i.length&&i.splice(o,i.length-o)}(s,r,i,n),t.concat(r)}return t.concat(e.slice(i,n))},_processRangeData:function(t,i,n,s,r){this._pending=e,this._skip=i>this.skip()?T.min(r,(this.totalPages()-1)*this.take()):s,this._currentRangeStart=i,this._take=n;var a=this.options.serverPaging,o=this.options.serverSorting,l=this.options.serverFiltering,h=this.options.serverAggregates;try{this.options.serverPaging=!0,this._isServerGrouped()||this.group()&&this.group().length||(this.options.serverSorting=!0),this.options.serverFiltering=!0,this.options.serverPaging=!0,this.options.serverAggregates=!0,a&&(this._detachObservableParents(),this._data=t=this._observe(t)),this._process(t)}finally{this.options.serverPaging=a,this.options.serverSorting=o,this.options.serverFiltering=l,this.options.serverAggregates=h}},skip:function(){return this._skip===e?this._page!==e?(this._page-1)*(this.take()||1):e:this._skip},currentRangeStart:function(){return this._currentRangeStart||0},take:function(){return this._take||this._pageSize},_prefetchSuccessHandler:function(t,e,i,n){var s=this,r=s._timeStamp();return function(a){var o,l,h,u=!1,c={start:t,end:e,data:[],timestamp:s._timeStamp()};if(s._dequeueRequest(),s.trigger(b,{response:a,type:"read"}),a=s.reader.parse(a),(h=s._readData(a)).length){for(o=0,l=s._ranges.length;o<l;o++)if(s._ranges[o].start===t){u=!0,(c=s._ranges[o]).pristineData=h,c.data=s._observe(h),c.end=c.start+s._flatData(c.data,!0).length,s._sortRanges();break}u||s._addRange(s._observe(h),t)}s._total=s.reader.total(a),(n||r>=s._currentRequestTimeStamp||!s._skipRequestsInProgress)&&(i&&h.length?i():s.trigger(_,{}))}},prefetch:function(t,e,i){var n=this,s=T.min(t+e,n.total()),r={take:e,skip:t,page:t/e+1,pageSize:e,sort:n._sort,filter:n._filter,group:n._group,aggregate:n._aggregate};n._rangeExists(t,s)?i&&i():(clearTimeout(n._timeout),n._timeout=setTimeout(function(){n._queueRequest(r,function(){n.trigger(v,{type:"read"})?n._dequeueRequest():n.transport.read({data:n._params(r),success:n._prefetchSuccessHandler(t,s,i),error:function(){var t=O.call(arguments);n.error.apply(n,t)}})})},100))},_multiplePrefetch:function(t,e,i){var n=T.min(t+e,this.total()),s={take:e,skip:t,page:t/e+1,pageSize:e,sort:this._sort,filter:this._filter,group:this._group,aggregate:this._aggregate};this._rangeExists(t,n)?i&&i():this.trigger(v,{type:"read"})||this.transport.read({data:this._params(s),success:this._prefetchSuccessHandler(t,n,i,!0)})},_rangeExists:function(t,e){var i,n,s=this._ranges;for(i=0,n=s.length;i<n;i++)if(s[i].start<=t&&s[i].end>=e)return!0;return!1},_getCurrentRangeSpan:function(){var t,e,i=this._ranges,n=this.currentRangeStart(),s=n+(this.take()||0),r=[],a=i.length;for(e=0;e<a;e++)((t=i[e]).start<=n&&t.end>=n||t.start>=n&&t.start<=s)&&r.push(t);return r},_removeModelFromRanges:function(t){for(var e,i,n=0,s=this._ranges.length;n<s&&(i=this._ranges[n],this._eachItem(i.data,function(i){e=gt(i,t)}),!e);n++);this._updateRangesLength()},_insertModelInRange:function(t,e){var i,n,s=this._ranges||[],r=s.length;for(n=0;n<r;n++)if((i=s[n]).start<=t&&i.end>=t){this._getByUid(e.uid,i.data)||(this._isServerGrouped()?i.data.splice(t,0,this._wrapInEmptyGroup(e)):i.data.splice(t,0,e));break}this._updateRangesLength()},_updateRangesLength:function(){var t,e,i=this._ranges||[],n=i.length,s=!1,r=0,a=0;for(e=0;e<n;e++)t=i[e],a=this._flatData(t.data,!0).length-T.abs(t.end-t.start),s||0===a?s&&(t.start+=r,t.end+=r):(s=!0,r=a,t.end+=r)}}),xt={};xt.create=function(e,i,s){var a,o=e.transport?t.extend({},e.transport):null;return o?(o.read=typeof o.read===g?{url:o.read}:o.read,"jsdo"===e.type&&(o.dataSource=s),e.type&&(d.data.transports=d.data.transports||{},d.data.schemas=d.data.schemas||{},d.data.transports[e.type]?r(d.data.transports[e.type])?o=n(!0,{},d.data.transports[e.type],o):a=new d.data.transports[e.type](n(o,{data:i})):d.logToConsole("Unknown DataSource transport type '"+e.type+"'.\nVerify that registration scripts for this type are included after Kendo UI on the page.","warn"),e.schema=n(!0,{},d.data.schemas[e.type],e.schema)),a||(a=p(o.read)?o:new rt(o))):a=new st({data:e.data||[]}),a},kt.create=function(i){(o(i)||i instanceof z)&&(i={data:i});var s,r,l,h=i||{},u=h.data,c=h.fields,p=h.table,f=h.select,m={};if(u||!c||h.transport||(p?u=function(e,i){var n,s,r,a,o,l,h,u=t(e)[0].tBodies[0],c=u?u.rows:[],d=i.length,p=[];for(n=0,s=c.length;n<s;n++){for(o={},h=!0,a=c[n].cells,r=0;r<d;r++)"th"!==(l=a[r]).nodeName.toLowerCase()&&(h=!1,o[i[r].field]=l.innerHTML);h||p.push(o)}return p}(p,c):f&&(u=function(e,i){var n,s,r,a,o,l,h=(e=t(e)[0]).options,u=i[0],c=i[1],d=[];for(n=0,s=h.length;n<s;n++)o={},a=h[n],(r=a.parentNode)===e&&(r=null),a.disabled||r&&r.disabled||(r&&(o.optgroup=r.label),o[u.field]=a.text,l=(l=a.attributes.value)&&l.specified?a.value:a.text,o[c.field]=l,d.push(o));return d}(f,c),h.group===e&&u[0]&&u[0].optgroup!==e&&(h.group="optgroup"))),d.data.Model&&c&&(!h.schema||!h.schema.model)){for(s=0,r=c.length;s<r;s++)(l=c[s]).type&&(m[l.field]=l);a(m)||(h.schema=n(!0,h.schema,{model:{fields:m}}))}return h.data=u,f=null,h.select=null,p=null,h.table=null,h instanceof kt?h:new kt(h)};var Ct=W.define({idField:"id",init:function(t){var e=this.hasChildren||t&&t.hasChildren,i="items",s={};if(d.data.Model.fn.init.call(this,t),typeof this.children===g&&(i=this.children),s={schema:{data:i,model:{hasChildren:e,id:this.idField,fields:this.fields}}},typeof this.children!==g&&n(s,this.children),s.data=t,e||(e=s.schema.data),typeof e===g&&(e=d.getter(e)),p(e)){var r=e.call(this,this);r&&0===r.length?this.hasChildren=!1:this.hasChildren=!!r}this._childrenOptions=s,this.hasChildren&&this._initChildren(),this._loaded=!(!t||!t._loaded)},_initChildren:function(){var t,e,i,n=this;n.children instanceof St||(e=(t=n.children=new St(n._childrenOptions)).transport,i=e.parameterMap,e.parameterMap=function(t,e){return t[n.idField||"id"]=n.id,i&&(t=i(t,e)),t},t.parent=function(){return n},t.bind(_,function(t){t.node=t.node||n,n.trigger(_,t)}),t.bind("error",function(t){var e=n.parent();e&&(t.node=t.node||n,e.trigger("error",t))}),n._updateChildrenField())},append:function(t){this._initChildren(),this.loaded(!0),this.children.add(t)},hasChildren:!1,level:function(){for(var t=this.parentNode(),e=0;t&&t.parentNode;)e++,t=t.parentNode?t.parentNode():null;return e},_updateChildrenField:function(){this[this._childrenOptions.schema.data||"items"]=this.children.data()},_childrenLoaded:function(){this._loaded=!0,this._updateChildrenField()},load:function(){var i,n,r={},a="_query";return this.hasChildren?(this._initChildren(),i=this.children,r[this.idField||"id"]=this.id,this._loaded||(i._data=e,a="read"),i.one(_,s(this._childrenLoaded,this)),this._matchFilter&&(r.filter={field:"_matchFilter",operator:"eq",value:!0}),n=i[a](r)):this.loaded(!0),n||t.Deferred().resolve().promise()},parentNode:function(){return this.parent().parent()},loaded:function(t){if(t===e)return this._loaded;this._loaded=t},shouldSerialize:function(t){return W.fn.shouldSerialize.call(this,t)&&"children"!==t&&"_loaded"!==t&&"hasChildren"!==t&&"_childrenOptions"!==t}});function Tt(t){return function(){var e=this._data,i=kt.fn[t].apply(this,O.call(arguments));return this._data!=e&&this._attachBubbleHandlers(),i}}var St=kt.extend({init:function(t){var e=Ct.define({children:t});t.filter&&!t.serverFiltering&&(this._hierarchicalFilter=t.filter,t.filter=null),kt.fn.init.call(this,n(!0,{},{schema:{modelBase:e,model:e}},t)),this._attachBubbleHandlers()},_attachBubbleHandlers:function(){var t=this;t._data.bind("error",function(e){t.trigger("error",e)})},read:function(t){var e=kt.fn.read.call(this,t);return this._hierarchicalFilter&&(this._data&&this._data.length>0?this.filter(this._hierarchicalFilter):(this.options.filter=this._hierarchicalFilter,this._filter=K(this.options.filter),this._hierarchicalFilter=null)),e},remove:function(t){var e,i=t.parentNode(),n=this;return i&&i._initChildren&&(n=i.children),e=kt.fn.remove.call(n,t),i&&!n.data().length&&(i.hasChildren=!1),e},success:Tt("success"),data:Tt("data"),insert:function(t,e){var i=this.parent();return i&&i._initChildren&&(i.hasChildren=!0,i._initChildren()),kt.fn.insert.call(this,t,e)},filter:function(t){if(t===e)return this._filter;!this.options.serverFiltering&&this._markHierarchicalQuery(t)&&(t={logic:"or",filters:[t,{field:"_matchFilter",operator:"equals",value:!0}]}),this.trigger("reset"),this._query({filter:t,page:1})},_markHierarchicalQuery:function(t){var e,i,n,s,r;return(t=K(t))&&0!==t.filters.length?(e=G.filterExpr(t),n=e.fields,s=e.operators,i=r=new Function("d, __f, __o","return "+e.expression),(n.length||s.length)&&(r=function(t){return i(t,n,s)}),this._updateHierarchicalFilter(r),!0):(this._updateHierarchicalFilter(function(){return!0}),!1)},_updateHierarchicalFilter:function(t){for(var e,i=this._data,n=!1,s=0;s<i.length;s++)(e=i[s]).hasChildren?(e._matchFilter=e.children._updateHierarchicalFilter(t),e._matchFilter||(e._matchFilter=t(e))):e._matchFilter=t(e),e._matchFilter&&(n=!0);return n},_find:function(t,e){var i,n,s,r,a=this._data;if(a){if(s=kt.fn[t].call(this,e))return s;for(i=0,n=(a=this._flatData(this._data)).length;i<n;i++)if((r=a[i].children)instanceof St&&(s=r[t](e)))return s}},get:function(t){return this._find("get",t)},getByUid:function(t){return this._find("getByUid",t)}});St.create=function(e){var i=(e=e&&e.push?{data:e}:e)||{},n=i.data,s=i.fields,r=i.list;return n&&n._dataSource?n._dataSource:(n||!s||i.transport||r&&(n=function e(i,n){var s,r,a,o,l,h,u,c,d=t(i).children(),p=[],f=n[0].field,m=n[1]&&n[1].field,g=n[2]&&n[2].field,_=n[3]&&n[3].field;function v(t,e){return t.filter(e).add(t.find(e))}for(s=0,r=d.length;s<r;s++)a={_loaded:!0},h=(o=d.eq(s))[0].firstChild,i=(c=o.children()).filter("ul"),c=c.filter(":not(ul)"),(l=o.attr("data-id"))&&(a.id=l),h&&(a[f]=3==h.nodeType?h.nodeValue:c.text()),m&&(a[m]=v(c,"a").attr("href")),_&&(a[_]=v(c,"img").attr("src")),g&&(u=v(c,".k-sprite").prop("className"),a[g]=u&&t.trim(u.replace("k-sprite",""))),i.length&&(a.items=e(i.eq(0),n)),"true"==o.attr("data-hasChildren")&&(a.hasChildren=!0),p.push(a);return p}(r,s)),i.data=n,i instanceof St?i:new St(i))};var It=d.Observable.extend({init:function(t,e,i){d.Observable.fn.init.call(this),this._prefetching=!1,this.dataSource=t,this.prefetch=!i;var n=this;t.bind("change",function(){n._change()}),t.bind("reset",function(){n._reset()}),this._syncWithDataSource(),this.setViewSize(e)},setViewSize:function(t){this.viewSize=t,this._recalculate()},at:function(t){var e=this.pageSize,i=!0;return t>=this.total()?(this.trigger("endreached",{index:t}),null):this.useRanges?this.useRanges?((t<this.dataOffset||t>=this.skip+e)&&(i=this.range(Math.floor(t/e)*e)),t===this.prefetchThreshold&&this._prefetch(),t===this.midPageThreshold?this.range(this.nextMidRange,!0):t===this.nextPageThreshold?this.range(this.nextFullRange):t===this.pullBackThreshold&&(this.offset===this.skip?this.range(this.previousMidRange):this.range(this.previousFullRange)),i?this.dataSource.at(t-this.dataOffset):(this.trigger("endreached",{index:t}),null)):void 0:this.dataSource.view()[t]},indexOf:function(t){return this.dataSource.data().indexOf(t)+this.dataOffset},total:function(){return parseInt(this.dataSource.total(),10)},next:function(){var t=this,e=t.pageSize,i=t.skip-t.viewSize+e,n=T.max(T.floor(i/e),0)*e;this.offset=i,this.dataSource.prefetch(n,e,function(){t._goToRange(i,!0)})},range:function(t,e){if(this.offset===t)return!0;var i=this,n=this.pageSize,s=T.max(T.floor(t/n),0)*n,r=this.dataSource;return e&&(s+=n),r.inRange(t,n)?(this.offset=t,this._recalculate(),this._goToRange(t),!0):!this.prefetch||(r.prefetch(s,n,function(){i.offset=t,i._recalculate(),i._goToRange(t,!0)}),!1)},syncDataSource:function(){var t=this.offset;this.offset=null,this.range(t)},destroy:function(){this.unbind()},_prefetch:function(){var t=this,e=this.pageSize,i=this.skip+e,n=this.dataSource;n.inRange(i,e)||this._prefetching||!this.prefetch||(this._prefetching=!0,this.trigger("prefetching",{skip:i,take:e}),n.prefetch(i,e,function(){t._prefetching=!1,t.trigger("prefetched",{skip:i,take:e})}))},_goToRange:function(t,e){this.offset===t&&(this.dataOffset=t,this._expanding=e,this.dataSource.range(t,this.pageSize),this.dataSource.enableRequestsInProgress())},_reset:function(){this._syncPending=!0},_change:function(){var t=this.dataSource;this.length=this.useRanges?t.lastRange().end:t.view().length,this._syncPending&&(this._syncWithDataSource(),this._recalculate(),this._syncPending=!1,this.trigger("reset",{offset:this.offset})),this.trigger("resize"),this._expanding&&this.trigger("expand"),delete this._expanding},_syncWithDataSource:function(){var t=this.dataSource;this._firstItemUid=t.firstItemUid(),this.dataOffset=this.offset=t.skip()||0,this.pageSize=t.pageSize(),this.useRanges=t.options.serverPaging},_recalculate:function(){var t=this.pageSize,e=this.offset,i=this.viewSize,n=Math.ceil(e/t)*t;this.skip=n,this.midPageThreshold=n+t-1,this.nextPageThreshold=n+i-1,this.prefetchThreshold=n+Math.floor(t/3*2),this.pullBackThreshold=this.offset-1,this.nextMidRange=n+t-i,this.nextFullRange=n,this.previousMidRange=e-i,this.previousFullRange=n-t}}),Dt=d.Observable.extend({init:function(t,e){var i=this;d.Observable.fn.init.call(i),this.dataSource=t,this.batchSize=e,this._total=0,this.buffer=new It(t,3*e),this.buffer.bind({endreached:function(t){i.trigger("endreached",{index:t.index})},prefetching:function(t){i.trigger("prefetching",{skip:t.skip,take:t.take})},prefetched:function(t){i.trigger("prefetched",{skip:t.skip,take:t.take})},reset:function(){i._total=0,i.trigger("reset")},resize:function(){i._total=Math.ceil(this.length/i.batchSize),i.trigger("resize",{total:i.total(),offset:this.offset})}})},syncDataSource:function(){this.buffer.syncDataSource()},at:function(t){var e,i=this.buffer,n=t*this.batchSize,s=this.batchSize,r=[];i.offset>n&&i.at(i.offset-1);for(var a=0;a<s&&null!==(e=i.at(n+a));a++)r.push(e);return r},total:function(){return this._total},destroy:function(){this.buffer.destroy(),this.unbind()}});n(!0,d.data,{readers:{json:dt},Query:G,DataSource:kt,HierarchicalDataSource:St,Node:Ct,ObservableObject:B,ObservableArray:z,LazyObservableArray:V,LocalTransport:st,RemoteTransport:rt,Cache:at,DataReader:dt,Model:W,Buffer:It,BatchBuffer:Dt})}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.calendar",["kendo.core","kendo.selectable"],function(){return function(t,e){var i=window.kendo,n=i.support,s=i.ui,r=s.Widget,a=i.keys,o=i.parseDate,l=i.date.adjustDST,h=i.date.weekInYear,u=i.ui.Selectable,c=i._extractFormat,d=i.template,p=i.getCulture,f=i.support.transitions,m=f?f.css+"transform-origin":"",g=d('<td#=data.cssClass# role="gridcell"><a tabindex="-1" class="k-link" href="\\#" data-#=data.ns#value="#=data.dateString#">#=data.value#</a></td>',{useWithBlock:!1}),_=d('<td role="gridcell">&nbsp;</td>',{useWithBlock:!1}),v=d('<td class="k-alt">#= data.weekNumber #</td>',{useWithBlock:!1}),w=i.support.browser,b=w.msie&&w.version<9,y=i._outerHeight,k=i._outerWidth,x=".kendoCalendar",C="click"+x,T="month",S="k-state-hover",I="k-state-disabled",D="k-state-focused",F="k-other-month",E=' class="'+F+'"',O="td:has(.k-link)",A="td:has(.k-link):not(."+I+")",M="focus",H=M+x,P=n.touch?"touchstart":"mouseenter",z=n.touch?"touchstart"+x:"mouseenter"+x,V=n.touch?"touchend"+x+" touchmove"+x:"mouseleave"+x,R="_prevArrow",B="_nextArrow",L="aria-disabled",N=t.proxy,W=t.extend,U=Date,q={month:0,year:1,decade:2,century:3},j=r.extend({init:function(e,n){var s,a,l=this;r.fn.init.call(l,e,n),e=l.wrapper=l.element,(n=l.options).url=window.unescape(n.url),l.options.disableDates=ut(l.options.disableDates),l._templates(),l._selectable(),l._header(),l._footer(l.footer),(a=e.addClass("k-widget k-calendar "+(n.weekNumber?" k-week-number":"")).on(z+" "+V,O,nt).on("keydown.kendoCalendar","table.k-content",N(l._move,l)).on(C,O,function(e){var i=e.currentTarget.firstChild,s=l._toDateObject(i);-1!=i.href.indexOf("#")&&e.preventDefault(),"month"==l._view.name&&l.options.disableDates(s)||"month"==l._view.name&&"single"!=n.selectable||l._click(t(i))}).on("mouseup"+x,"table.k-content, .k-footer",function(){l._focusView(!1!==l.options.focusOnNav)}).attr("id"))&&(l._cellID=a+"_cell_selected"),l._isMultipleSelection()&&l.options.weekNumber&&e.on(C,"td:not(:has(.k-link))",function(e){var i=t(e.currentTarget).closest("tr").find(A).first(),n=l.selectable._lastActive=t(e.currentTarget).closest("tr").find(A).last();l.selectable.selectRange(i,n,{event:e}),l._current=l._value=l._toDateObject(n.find("a")),l._class(D,l._current)}),ot(n),s=o(n.value,n.format,n.culture),l._selectDates=[],l._index=q[n.start],l._current=new U(+Q(s,n.min,n.max)),l._addClassProxy=function(){if(l._active=!0,l._cell.hasClass(I)){var t=l._view.toDateString(J());l._cell=l._cellByDate(t)}l._cell.addClass(D)},l._removeClassProxy=function(){l._active=!1,l._cell.removeClass(D)},l.value(s),l._isMultipleSelection()&&n.selectDates.length>0&&l.selectDates(n.selectDates),i.notify(l)},options:{name:"Calendar",value:null,min:new U(1900,0,1),max:new U(2099,11,31),dates:[],disableDates:null,url:"",culture:"",footer:"",format:"",month:{},weekNumber:!1,selectable:"single",selectDates:[],start:T,depth:T,animation:{horizontal:{effects:"slideIn",reverse:!0,duration:500,divisor:2},vertical:{effects:"zoomIn",duration:400}},messages:{weekColumnHeader:""}},events:["change","navigate"],setOptions:function(t){ot(t),t.disableDates=ut(t.disableDates),r.fn.setOptions.call(this,t),this._templates(),this._selectable(),this._footer(this.footer),this._index=q[this.options.start],this.navigate()},destroy:function(){var t=this._today;this.element.off(x),this._title.off(x),this[R].off(x),this[B].off(x),this._destroySelectable(),i.destroy(this._table),t&&i.destroy(t.off(x)),r.fn.destroy.call(this)},current:function(){return this._current},view:function(){return this._view},focus:function(t){t=t||this._table,this._bindTable(t),t.focus()},min:function(t){return this._option("min",t)},max:function(t){return this._option("max",t)},navigateToPast:function(){this._navigate(R,-1)},navigateToFuture:function(){this._navigate(B,1)},navigateUp:function(){var t=this._index;this._title.hasClass(I)||this.navigate(this._current,++t)},navigateDown:function(t){var e=this._index,i=this.options.depth;t&&(e!==q[i]?this.navigate(t,--e):ct(this._value,this._current)&&ct(this._value,t)||(this.value(t),this.trigger("change")))},navigate:function(i,n){n=isNaN(n)?q[n]:n;var s,r,a,o,l=this.options,h=l.culture,u=l.min,c=l.max,d=this._title,p=this._table,f=this._oldTable,m=this._current,g=i&&+i>+m,_=n!==e&&n!==this._index;if(i||(i=m),this._current=i=new U(+Q(i,u,c)),n===e?n=this._index:this._index=n,this._view=r=G.views[n],a=r.compare,o=n===q.century,d.toggleClass(I,o).attr(L,o),o=a(i,u)<1,this[R].toggleClass(I,o).attr(L,o),this[R].hasClass(I)&&this[R].removeClass(S),o=a(i,c)>-1,this[B].toggleClass(I,o).attr(L,o),this[B].hasClass(I)&&this[B].removeClass(S),p&&f&&f.data("animating")&&(f.kendoStop(!0,!0),p.kendoStop(!0,!0)),this._oldTable=p,!p||this._changeView){d.html(r.title(i,u,c,h)),this._table=s=t(r.content(W({min:u,max:c,date:i,url:l.url,dates:l.dates,format:l.format,culture:h,disableDates:l.disableDates,isWeekColumnVisible:l.weekNumber,messages:l.messages},this[r.name]))),ht(s,r.name),lt(s);var v=p&&p.data("start")===s.data("start");this._animate({from:p,to:s,vertical:_,future:g,replace:v}),this.trigger("navigate"),this._focus(i)}n===q[l.depth]&&this._selectDates.length>0&&this._visualizeSelectedDatesInView(),"single"===this.options.selectable&&n===q[l.depth]&&this._value&&!this.options.disableDates(this._value)&&this._class("k-state-selected",this._value),this._class(D,i),!p&&this._cell&&this._cell.removeClass(D),this._changeView=!0},selectDates:function(i){var n,s,r=this;if(i===e)return r._selectDates;s=i.map(function(t){return t.getTime()}).filter(function(t,e,i){return i.indexOf(t)===e}).map(function(t){return new Date(t)}),n=t.grep(s,function(t){if(t)return+r._validateValue(new Date(t.setHours(0,0,0,0)))==+t}),r._selectDates=n.length>0?n:0===s.length?s:r._selectDates,r._visualizeSelectedDatesInView()},value:function(t){var i=this._view,n=this._view;if(t===e)return this._value;if((t=this._validateValue(t))&&this._isMultipleSelection()){var s=new Date(+t);s.setHours(0,0,0,0),this._selectDates=[s],this.selectable._lastActive=null}i&&null===t&&this._cell?this._cell.removeClass("k-state-selected"):(this._changeView=!t||n&&0!==n.compare(t,this._current),this.navigate(t))},_validateValue:function(t){var i=this.options,n=i.min,s=i.max;return null===t&&(this._current=rt(this._current.getFullYear(),this._current.getMonth(),this._current.getDate())),null!==(t=o(t,i.format,i.culture))&&(X(t=new U(+t),n,s)||(t=null)),null!==t&&this.options.disableDates(new Date(+t))?this._value===e&&(this._value=null):this._value=t,this._value},_visualizeSelectedDatesInView:function(){var e={};t.each(this._selectDates,function(t,n){e[i.calendar.views[0].toDateString(n)]=n}),this.selectable.clear();var n=this._table.find(O).filter(function(n,s){return e[t(s.firstChild).attr(i.attr("value"))]});n.length>0&&this.selectable._selectElement(n,!0)},_isMultipleSelection:function(){return"multiple"===this.options.selectable},_selectable:function(){if(this._isMultipleSelection()){var t=this.options.selectable,e=u.parseOptions(t);e.multiple&&this.element.attr("aria-multiselectable","true"),this.selectable=new u(this.wrapper,{aria:!0,inputSelectors:"input,textarea,.k-multiselect-wrap,select,button,.k-button>span,.k-button>img,span.k-icon.k-i-arrow-60-down,span.k-icon.k-i-arrow-60-up",multiple:e.multiple,filter:"table.k-month:eq(0) "+A,change:N(this._onSelect,this),relatedTarget:N(this._onRelatedTarget,this)})}},_onRelatedTarget:function(t){this.selectable.options.multiple&&t.is(A)&&(this._current=this._toDateObject(t.find("a")),this._class(D,this._toDateObject(t.find("a"))))},_onSelect:function(e){var i=this,n=e;u.parseOptions(i.options.selectable).multiple?(n.event.ctrlKey||n.event.metaKey?t(n.event.currentTarget).is(A)?i._toggleSelection(t(n.event.currentTarget)):(i._cellsBySelector(A).each(function(e,n){var s=i._toDateObject(t(n).find("a"));i._deselect(s)}),i._addSelectedCellsToArray()):n.event.shiftKey?i._rangeSelection(i._cell):t(n.event.currentTarget).is(O)?i.value(i._toDateObject(t(n.event.currentTarget).find("a"))):(i._selectDates=[],i._addSelectedCellsToArray()),i.trigger("change")):t(n.event.currentTarget).is("td")&&!t(n.event.currentTarget).hasClass("k-state-selected")?t(n.event.currentTarget).addClass("k-state-selected"):i._click(t(n.event.currentTarget).find("a"))},_destroySelectable:function(){this.selectable&&(this.selectable.destroy(),this.selectable=null)},_toggleSelection:function(t){var e=this._toDateObject(t.find("a"));t.hasClass("k-state-selected")?this._selectDates.push(e):this._deselect(e)},_rangeSelection:function(t,e){var i,n=e||this._toDateObject(this.selectable.value().first().find("a")),s=this._toDateObject(t.find("a"));this.selectable._lastActive||this._value?n=this.selectable._lastActive?this._toDateObject(this.selectable._lastActive.find("a")):new Date(+this._value):this.selectable._lastActive=e?this._cellByDate(this._view.toDateString(e),A):this.selectable.value().first(),this._selectDates=[],i=et(n,s),it(this._selectDates,i,n,this.options.disableDates),this._visualizeSelectedDatesInView()},_cellsBySelector:function(t){return this._table.find(t)},_addSelectedCellsToArray:function(){var e=this;e.selectable.value().each(function(i,n){var s=e._toDateObject(t(n.firstChild));e.options.disableDates(s)||e._selectDates.push(s)})},_deselect:function(t){var e=this._selectDates.map(Number).indexOf(+t);-1!=e&&this._selectDates.splice(e,1)},_dateInView:function(t){var e=this._toDateObject(this._cellsBySelector(O+":first").find("a"));return+t<=+this._toDateObject(this._cellsBySelector(O+":last").find("a"))&&+t>=+e},_move:function(n){var s,r,o,l,h=this.options,u=n.keyCode,c=this._view,d=this._index,p=this.options.min,f=this.options.max,m=new U(+this._current),g=i.support.isRtl(this.wrapper),_=this.options.disableDates;if(n.target===this._table[0]&&(this._active=!0),u==a.RIGHT&&!g||u==a.LEFT&&g?(s=1,r=!0):u==a.LEFT&&!g||u==a.RIGHT&&g?(s=-1,r=!0):u==a.UP?(s=0===d?-7:-4,r=!0):u==a.DOWN?(s=0===d?7:4,r=!0):u==a.SPACEBAR?(s=0,r=!0):u!=a.HOME&&u!=a.END||(l=c[o=u==a.HOME?"first":"last"](m),(m=new U(l.getFullYear(),l.getMonth(),l.getDate(),m.getHours(),m.getMinutes(),m.getSeconds(),m.getMilliseconds())).setFullYear(l.getFullYear()),r=!0),n.ctrlKey||n.metaKey){if(u==a.RIGHT&&!g||u==a.LEFT&&g)this.navigateToFuture(),r=!0;else if(u==a.LEFT&&!g||u==a.RIGHT&&g)this.navigateToPast(),r=!0;else if(u==a.UP)this.navigateUp(),r=!0;else if(u==a.DOWN)this._click(t(this._cell[0].firstChild)),r=!0;else if((u==a.ENTER||u==a.SPACEBAR)&&this._isMultipleSelection()){this._keyboardToggleSelection(n);var v=this._toDateObject(t(this._cell[0]).find("a"));this._class(D,v)}}else n.shiftKey?(s!==e||o)&&(o||c.setDate(m,s),_(m)&&(m=this._nextNavigatable(m,s)),X(m,p=rt(p.getFullYear(),p.getMonth(),p.getDate()),f)&&(this._isMultipleSelection()?this._keyboardRangeSelection(n,m):this._focus(Q(m,h.min,h.max)))):(u==a.ENTER||u==a.SPACEBAR?("month"==c.name&&this._isMultipleSelection()?(this.value(this._toDateObject(t(this._cell.find("a")))),this.selectable._lastActive=t(this._cell[0]),this.trigger("change")):this._click(t(this._cell[0].firstChild)),r=!0):u==a.PAGEUP?(r=!0,this.navigateToPast()):u==a.PAGEDOWN&&(r=!0,this.navigateToFuture()),(s||o)&&(o||c.setDate(m,s),_(m)&&(m=this._nextNavigatable(m,s)),X(m,p=rt(p.getFullYear(),p.getMonth(),p.getDate()),f)&&(this._isMultipleSelection()?this._dateInView(m)?(this._current=m,this._class(D,m)):this.navigate(m):this._focus(Q(m,h.min,h.max)))));return r&&n.preventDefault(),this._current},_keyboardRangeSelection:function(t,e){var i,n;if(!this._dateInView(e))return this._selectDates=[],n=et(i=this.selectable._lastActive?this._toDateObject(this.selectable._lastActive.find("a")):e,new Date(+e)),it(this._selectDates,n,i,this.options.disableDates),this.navigate(e),this._current=e,this.selectable._lastActive=this.selectable._lastActive||this._cellByDate(this._view.toDateString(e),A),void this.trigger("change");this.selectable.options.filter=this.wrapper.find("table").length>1&&+e>+this._current?"table.k-month:eq(1) "+A:"table.k-month:eq(0) "+A,this._class(D,e),this._current=e,this._rangeSelection(this._cellByDate(this._view.toDateString(e),A),e),this.trigger("change"),this.selectable.options.filter="table.k-month:eq(0) "+A},_keyboardToggleSelection:function(e){e.currentTarget=this._cell[0],this.selectable._lastActive=t(this._cell[0]),t(this._cell[0]).hasClass("k-state-selected")?(this.selectable._unselect(t(this._cell[0])),this.selectable.trigger("change",{event:e})):this.selectable.value(t(this._cell[0]),{event:e})},_nextNavigatable:function(t,e){var i=!0,n=this._view,s=this.options.min,r=this.options.max,a=this.options.disableDates,o=new Date(t.getTime());for(n.setDate(o,-e);i;){if(n.setDate(t,e),!X(t,s,r)){t=o;break}i=a(t)}return t},_animate:function(t){var e=t.from,i=t.to,n=this._active;e?e.parent().data("animating")?(e.off(x),e.parent().kendoStop(!0,!0).remove(),e.remove(),i.insertAfter(this.element[0].firstChild),this._focusView(n)):!e.is(":visible")||!1===this.options.animation||t.replace?(i.insertAfter(e),e.off(x).remove(),this._focusView(n)):this[t.vertical?"_vertical":"_horizontal"](e,i,t.future):(i.insertAfter(this.element[0].firstChild),this._bindTable(i))},_horizontal:function(t,e,i){var n=this,s=n._active,r=n.options.animation.horizontal,a=r.effects,o=k(t);a&&-1!=a.indexOf("slideIn")&&(t.add(e).css({width:o}),t.wrap("<div/>"),n._focusView(s,t),t.parent().css({position:"relative",width:2*o,float:"left","margin-left":i?0:-o}),e[i?"insertAfter":"insertBefore"](t),W(r,{effects:"slideIn:"+(i?"right":"left"),complete:function(){t.off(x).remove(),n._oldTable=null,e.unwrap(),n._focusView(s)}}),t.parent().kendoStop(!0,!0).kendoAnimate(r))},_vertical:function(t,e){var i,n,s=this,r=s.options.animation.vertical,a=r.effects,o=s._active;a&&-1!=a.indexOf("zoom")&&(e.css({position:"absolute",top:y(t.prev()),left:0}).insertBefore(t),m&&(n=(n=(i=s._cellByDate(s._view.toDateString(s._current))).position()).left+parseInt(i.width()/2,10)+"px "+(n.top+parseInt(i.height()/2,10))+"px",e.css(m,n)),t.kendoStop(!0,!0).kendoAnimate({effects:"fadeOut",duration:600,complete:function(){t.off(x).remove(),s._oldTable=null,e.css({position:"static",top:0,left:0}),s._focusView(o)}}),e.kendoStop(!0,!0).kendoAnimate(r))},_cellByDate:function(e,n){return this._table.find(n||"td:not(."+F+")").filter(function(){return t(this.firstChild).attr(i.attr("value"))===e})},_class:function(t,e){var i,n=this._cellID,s=this._cell,r=this._view.toDateString(e);s&&s.removeAttr("aria-selected").removeAttr("aria-label").removeAttr("id"),e&&"month"==this._view.name&&(i=this.options.disableDates(e)),this._cellsBySelector(this._isMultipleSelection()?O:"td:not(."+F+")").removeClass(t),s=this._cellByDate(r,"multiple"==this.options.selectable?O:"td:not(."+F+")").attr("aria-selected",!0),(t===D&&!this._active&&!1!==this.options.focusOnNav||i)&&(t=""),s.addClass(t),s[0]&&(this._cell=s),n&&(s.attr("id",n),this._table.removeAttr("aria-activedescendant").attr("aria-activedescendant",n))},_bindTable:function(t){t.on(H,this._addClassProxy).on("blur.kendoCalendar",this._removeClassProxy)},_click:function(t){var e=this.options,i=new Date(+this._current),n=this._toDateObject(t);l(n,0),"month"==this._view.name&&this.options.disableDates(n)&&(n=this._value),this._view.setDate(i,n),this.navigateDown(Q(i,e.min,e.max))},_focus:function(t){0!==this._view.compare(t,this._current)?this.navigate(t):(this._current=t,this._class(D,t))},_focusView:function(t,e){t&&this.focus(e)},_footer:function(e){var n=J(),s=this.element,r=s.find(".k-footer");if(!e)return this._toggle(!1),void r.hide();r[0]||(r=t('<div class="k-footer"><a href="#" class="k-link k-nav-today"></a></div>').appendTo(s)),this._today=r.show().find(".k-link").html(e(n)).attr("title",i.toString(n,"D",this.options.culture)),this._toggle()},_header:function(){var t,e=this,i=e.element;i.find(".k-header")[0]||i.html('<div class="k-header"><a href="#" role="button" class="k-link k-nav-prev" aria-label="Previous"><span class="k-icon k-i-arrow-60-left"></span></a><a href="#" role="button" aria-live="assertive" aria-atomic="true" class="k-link k-nav-fast"></a><a href="#" role="button" class="k-link k-nav-next" aria-label="Next"><span class="k-icon k-i-arrow-60-right"></span></a></div>'),t=i.find(".k-link").on(z+" "+V+" "+H+" blur.kendoCalendar",nt).click(!1),e._title=t.eq(1).on(C,function(){e._active=!1!==e.options.focusOnNav,e.navigateUp()}),e[R]=t.eq(0).on(C,function(){e._active=!1!==e.options.focusOnNav,e.navigateToPast()}),e[B]=t.eq(2).on(C,function(){e._active=!1!==e.options.focusOnNav,e.navigateToFuture()})},_navigate:function(t,e){var i=this._index+1,n=new U(+this._current);if(this._isMultipleSelection()){var s=this._table.find("td:not(.k-other-month)").has(".k-link").first();n=this._toDateObject(s.find("a")),this._current=new Date(+n)}(t=this[t]).hasClass(I)||(i>3?n.setFullYear(n.getFullYear()+100*e):G.views[i].setDate(n,e),this.navigate(n))},_option:function(t,i){var n,s,r,a=this.options,l=this._value||this._current;if(i===e)return a[t];(i=o(i,a.format,a.culture))&&(a[t]=new U(+i),((n="min"===t?i>l:l>i)||(r=i,!!(s=l)&&s.getFullYear()===r.getFullYear()&&s.getMonth()===r.getMonth()))&&(n&&(this._value=null),this._changeView=!0),this._changeView||(this._changeView=!(!a.month.content&&!a.month.empty)),this.navigate(this._value),this._toggle())},_toggle:function(t){var i=this.options,n=this.options.disableDates(J()),s=this._today;t===e&&(t=X(J(),i.min,i.max)),s&&(s.off(C),t&&!n?s.addClass("k-nav-today").removeClass(I).on(C,N(this._todayClick,this)):s.removeClass("k-nav-today").addClass(I).on(C,st))},_todayClick:function(t){var e=q[this.options.depth],i=this.options.disableDates,n=J();t.preventDefault(),i(n)||(0===this._view.compare(this._current,n)&&this._index==e&&(this._changeView=!1),this._isMultipleSelection()&&(this._selectDates=[n],this.selectable._lastActive=null),this._value=n,this.navigate(n,e),this.trigger("change"))},_toDateObject:function(e){var n=t(e).attr(i.attr("value")).split("/");return n=rt(n[0],n[1],n[2])},_templates:function(){var t=this.options,e=t.footer,n=t.month,s=n.content,r=n.weekNumber,a=n.empty;this.month={content:d('<td#=data.cssClass# role="gridcell"><a tabindex="-1" class="k-link#=data.linkClass#" href="#=data.url#" '+i.attr("value")+'="#=data.dateString#" title="#=data.title#">'+(s||"#=data.value#")+"</a></td>",{useWithBlock:!!s}),empty:d('<td role="gridcell">'+(a||"&nbsp;")+"</td>",{useWithBlock:!!a}),weekNumber:d('<td class="k-alt">'+(r||"#= data.weekNumber #")+"</td>",{useWithBlock:!!r})},this.footer=!1!==e?d(e||'#= kendo.toString(data,"D","'+t.culture+'") #',{useWithBlock:!1}):null}});s.plugin(j);var G={firstDayOfMonth:function(t){return rt(t.getFullYear(),t.getMonth(),1)},firstVisibleDay:function(t,e){var n=(e=e||i.culture().calendar).firstDay,s=new U(t.getFullYear(),t.getMonth(),1,t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds());for(s.setFullYear(t.getFullYear());s.getDay()!=n;)G.setTime(s,-864e5);return s},setTime:function(t,e){var i=t.getTimezoneOffset(),n=new U(t.getTime()+e),s=n.getTimezoneOffset()-i;t.setTime(n.getTime()+6e4*s)},views:[{name:T,title:function(t,e,i,n){return at(n).months.names[t.getMonth()]+" "+t.getFullYear()},content:function(t){var e=0,n=t.min,s=t.max,r=t.date,a=t.dates,o=t.format,u=t.culture,c=t.url,d=t.isWeekColumnVisible,p=c&&a[0],f=at(u),m=f.firstDay,g=f.days,_=Z(g.names,m),v=Z(g.namesShort,m),w=G.firstVisibleDay(r,f),b=this.first(r),y=this.last(r),k=this.toDateString,x=J(),C='<table tabindex="0" role="grid" class="k-content" cellspacing="0" data-start="'+k(w)+'"><thead><tr role="row">';for(d&&(C+='<th scope="col" class="k-alt">'+t.messages.weekColumnHeader+"</th>");e<7;e++)C+='<th scope="col" title="'+_[e]+'">'+v[e]+"</th>";return l(x,0),x=+x,$({cells:42,perRow:7,html:C+='</tr></thead><tbody><tr role="row">',start:w,isWeekColumnVisible:d,weekNumber:t.weekNumber,min:rt(n.getFullYear(),n.getMonth(),n.getDate()),max:rt(s.getFullYear(),s.getMonth(),s.getDate()),content:t.content,empty:t.empty,setter:this.setDate,disableDates:t.disableDates,build:function(t,e,n){var s=[],r=t.getDay(),l="",h="#";return(t<b||t>y)&&s.push(F),n(t)&&s.push(I),+t===x&&s.push("k-today"),0!==r&&6!==r||s.push("k-weekend"),p&&function(t,e){for(var i=0,n=e.length;i<n;i++)if(t===+e[i])return!0;return!1}(+t,a)&&(h=c.replace("{0}",i.toString(t,o,u)),l=" k-action-link"),{date:t,dates:a,ns:i.ns,title:i.toString(t,"D",u),value:t.getDate(),dateString:k(t),cssClass:s[0]?' class="'+s.join(" ")+'"':"",linkClass:l,url:h}},weekNumberBuild:function(t){return{weekNumber:h(t,i.culture().calendar.firstDay),currentDate:t}}})},first:function(t){return G.firstDayOfMonth(t)},last:function(t){var e=rt(t.getFullYear(),t.getMonth()+1,0),i=G.firstDayOfMonth(t),n=Math.abs(e.getTimezoneOffset()-i.getTimezoneOffset());return n&&e.setHours(i.getHours()+n/60),e},compare:function(t,e){var i=t.getMonth(),n=t.getFullYear(),s=e.getMonth(),r=e.getFullYear();return n>r?1:n<r?-1:i==s?0:i>s?1:-1},setDate:function(t,e){var i=t.getHours();e instanceof U?t.setFullYear(e.getFullYear(),e.getMonth(),e.getDate()):G.setTime(t,864e5*e),l(t,i)},toDateString:function(t){return t.getFullYear()+"/"+t.getMonth()+"/"+t.getDate()}},{name:"year",title:function(t){return t.getFullYear()},content:function(t){var e=at(t.culture).months.namesAbbr,n=this.toDateString,s=t.min,r=t.max;return $({min:rt(s.getFullYear(),s.getMonth(),1),max:rt(r.getFullYear(),r.getMonth(),1),start:rt(t.date.getFullYear(),0,1),setter:this.setDate,build:function(t){return{value:e[t.getMonth()],ns:i.ns,dateString:n(t),cssClass:""}}})},first:function(t){return rt(t.getFullYear(),0,t.getDate())},last:function(t){return rt(t.getFullYear(),11,t.getDate())},compare:function(t,e){return K(t,e)},setDate:function(t,e){var i,n=t.getHours();e instanceof U?(i=e.getMonth(),t.setFullYear(e.getFullYear(),i,t.getDate()),i!==t.getMonth()&&t.setDate(0)):(i=t.getMonth()+e,t.setMonth(i),i>11&&(i-=12),i>0&&t.getMonth()!=i&&t.setDate(0)),l(t,n)},toDateString:function(t){return t.getFullYear()+"/"+t.getMonth()+"/1"}},{name:"decade",title:function(t,e,i){return Y(t,e,i,10)},content:function(t){var e=t.date.getFullYear(),n=this.toDateString;return $({start:rt(e-e%10-1,0,1),min:rt(t.min.getFullYear(),0,1),max:rt(t.max.getFullYear(),0,1),setter:this.setDate,build:function(t,e){return{value:t.getFullYear(),ns:i.ns,dateString:n(t),cssClass:0===e||11==e?E:""}}})},first:function(t){var e=t.getFullYear();return rt(e-e%10,t.getMonth(),t.getDate())},last:function(t){var e=t.getFullYear();return rt(e-e%10+9,t.getMonth(),t.getDate())},compare:function(t,e){return K(t,e,10)},setDate:function(t,e){tt(t,e,1)},toDateString:function(t){return t.getFullYear()+"/0/1"}},{name:"century",title:function(t,e,i){return Y(t,e,i,100)},content:function(t){var e=t.date.getFullYear(),n=t.min.getFullYear(),s=t.max.getFullYear(),r=this.toDateString,a=n,o=s;return(o-=o%10)-(a-=a%10)<10&&(o=a+9),$({start:rt(e-e%100-10,0,1),min:rt(a,0,1),max:rt(o,0,1),setter:this.setDate,build:function(t,e){var a=t.getFullYear(),o=a+9;return a<n&&(a=n),o>s&&(o=s),{ns:i.ns,value:a+" - "+o,dateString:r(t),cssClass:0===e||11==e?E:""}}})},first:function(t){var e=t.getFullYear();return rt(e-e%100,t.getMonth(),t.getDate())},last:function(t){var e=t.getFullYear();return rt(e-e%100+99,t.getMonth(),t.getDate())},compare:function(t,e){return K(t,e,100)},setDate:function(t,e){tt(t,e,10)},toDateString:function(t){var e=t.getFullYear();return e-e%10+"/0/1"}}]};function Y(t,e,i,n){var s,r=t.getFullYear(),a=e.getFullYear(),o=i.getFullYear();return s=(r-=r%n)+(n-1),r<a&&(r=a),s>o&&(s=o),r+"-"+s}function $(t){var e,i=0,n=t.min,s=t.max,r=t.start,a=t.setter,o=t.build,h=t.weekNumberBuild,u=t.cells||12,c=t.isWeekColumnVisible,d=t.perRow||4,p=t.weekNumber||v,f=t.content||g,m=t.empty||_,w=t.html||'<table tabindex="0" role="grid" class="k-content k-meta-view" cellspacing="0"><tbody><tr role="row">';for(c&&(w+=p(h(r)));i<u;i++)i>0&&i%d==0&&(w+='</tr><tr role="row">',c&&(w+=p(h(r)))),r=rt(r.getFullYear(),r.getMonth(),r.getDate()),l(r,0),e=o(r,i,t.disableDates),w+=X(r,n,s)?f(e):m(e),a(r,1);return w+"</tr></tbody></table>"}function K(t,e,i){var n=t.getFullYear(),s=e.getFullYear(),r=s,a=0;return i&&(r=(s-=s%i)-s%i+i-1),n>r?a=1:n<s&&(a=-1),a}function J(){var t=new U;return new U(t.getFullYear(),t.getMonth(),t.getDate())}function Q(t,e,i){var n=J();return t&&(n=new U(+t)),e>n?n=new U(+e):i<n&&(n=new U(+i)),n}function X(t,e,i){return+t>=+e&&+t<=+i}function Z(t,e){return t.slice(e).concat(t.slice(0,e))}function tt(t,e,i){e=e instanceof U?e.getFullYear():t.getFullYear()+i*e,t.setFullYear(e)}function et(t,e){if(+e<+t){var n=+t;G.views[0].setDate(t,e),G.views[0].setDate(e,new Date(n))}var s=Date.UTC(t.getFullYear(),t.getMonth(),t.getDate()),r=Date.UTC(e.getFullYear(),e.getMonth(),e.getDate());return Math.ceil((+r-+s)/i.date.MS_PER_DAY)}function it(t,e,i,n){for(var s=0;s<=e;s++){var r=new Date(i.getTime());n(r=new Date(r.setDate(r.getDate()+s)))||t.push(r)}}function nt(e){t(this).hasClass("k-state-disabled")||t(this).toggleClass(S,P.indexOf(e.type)>-1||e.type==M)}function st(t){t.preventDefault()}function rt(t,e,i){var n=new U(t,e,i);return n.setFullYear(t,e,i),n}function at(t){return p(t).calendars.standard}function ot(t){var i=q[t.start],n=q[t.depth],s=p(t.culture);t.format=c(t.format||s.calendars.standard.patterns.d),isNaN(i)&&(i=0,t.start=T),(n===e||n>i)&&(t.depth=T),null===t.dates&&(t.dates=[])}function lt(t){b&&t.find("*").attr("unselectable","on")}function ht(t,e){t.addClass("k-"+e)}function ut(e){return i.isFunction(e)?e:t.isArray(e)?function(e){var i,n=[],s=["su","mo","tu","we","th","fr","sa"],r="if (found) { return true } else {return false}";if(e[0]instanceof U)n=function(t){for(var e=[],i=0;i<t.length;i++)e.push(t[i].setHours(0,0,0,0));return e}(e),i="var found = date && window.kendo.jQuery.inArray(date.setHours(0, 0, 0, 0),["+n+"]) > -1;"+r;else{for(var a=0;a<e.length;a++){var o=e[a].slice(0,2).toLowerCase(),l=t.inArray(o,s);l>-1&&n.push(l)}i="var found = date && window.kendo.jQuery.inArray(date.getDay(),["+n+"]) > -1;"+r}return new Function("date",i)}(e):t.noop}function ct(t,e){return t instanceof Date&&e instanceof Date&&(t=t.getTime(),e=e.getTime()),t===e}G.isEqualDatePart=function(t,e){return!!t&&t.getFullYear()===e.getFullYear()&&t.getMonth()===e.getMonth()&&t.getDate()===e.getDate()},G.isEqualDate=ct,G.makeUnselectable=lt,G.restrictValue=Q,G.isInRange=X,G.addClassToViewContainer=ht,G.normalize=ot,G.viewsEnum=q,G.disabled=ut,i.calendar=G}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.popup",["kendo.core"],function(){return function(t,e){var i=window.kendo,n=i.ui,s=n.Widget,r=i.Class,a=i.support,o=i.getOffset,l=i._outerWidth,h=i._outerHeight,u="position",c="k-state-border",d=/k-state-border-(\w+)/,p=".k-picker-wrap, .k-dropdown-wrap, .k-link",f=t(document.documentElement),m=t.proxy,g=t(window),_=a.transitions.css+"transform",v=t.extend,w=".kendoPopup",b=["font-size","font-family","font-stretch","font-style","font-weight","line-height"];function y(e,i){return!(!e||!i)&&(e===i||t.contains(e,i))}var k=s.extend({init:function(e,n){var r,o=this;(n=n||{}).isRtl&&(n.origin=n.origin||"bottom right",n.position=n.position||"top right"),s.fn.init.call(o,e,n),e=o.element,n=o.options,o.collisions=n.collision?n.collision.split(" "):[],o.downEvent=i.applyEventMap("down",i.guid()),1===o.collisions.length&&o.collisions.push(o.collisions[0]),r=t(o.options.anchor).closest(".k-popup,.k-group").filter(":not([class^=km-])"),n.appendTo=t(t(n.appendTo)[0]||r[0]||document.body),o.element.hide().addClass("k-popup k-group k-reset").toggleClass("k-rtl",!!n.isRtl).css({position:"absolute"}).appendTo(n.appendTo).attr("aria-hidden",!0).on("mouseenter"+w,function(){o._hovered=!0}).on("wheel"+w,function(e){var i=t(e.target).find(".k-list"),n=i.parent();i.length&&i.is(":visible")&&(0===n.scrollTop()&&e.originalEvent.deltaY<0||n.scrollTop()===n.prop("scrollHeight")-n.prop("offsetHeight")&&e.originalEvent.deltaY>0)&&e.preventDefault()}).on("mouseleave"+w,function(){o._hovered=!1}),o.wrapper=t(),!1===n.animation&&(n.animation={open:{effects:{}},close:{hide:!0,effects:{}}}),v(n.animation.open,{complete:function(){o.wrapper.css({overflow:"visible"}),o._activated=!0,o._trigger("activate")}}),v(n.animation.close,{complete:function(){o._animationClose()}}),o._mousedownProxy=function(t){o._mousedown(t)},a.mobileOS.android?o._resizeProxy=function(t){setTimeout(function(){o._resize(t)},600)}:o._resizeProxy=function(t){o._resize(t)},n.toggleTarget&&t(n.toggleTarget).on(n.toggleEvent+w,t.proxy(o.toggle,o))},events:["open","activate","close","deactivate"],options:{name:"Popup",toggleEvent:"click",origin:"bottom left",position:"top left",anchor:"body",appendTo:null,collision:"flip fit",viewport:window,copyAnchorStyles:!0,autosize:!1,modal:!1,adjustSize:{width:0,height:0},animation:{open:{effects:"slideIn:down",transition:!0,duration:200},close:{duration:100,hide:!0}}},_animationClose:function(){var t=this.wrapper.data("location");this.wrapper.hide(),t&&this.wrapper.css(t),"body"!=this.options.anchor&&this._hideDirClass(),this._closing=!1,this._trigger("deactivate")},destroy:function(){var e,n=this.options,r=this.element.off(w);s.fn.destroy.call(this),n.toggleTarget&&t(n.toggleTarget).off(w),n.modal||(f.unbind(this.downEvent,this._mousedownProxy),this._toggleResize(!1)),i.destroy(this.element.children()),r.removeData(),n.appendTo[0]===document.body&&((e=r.parent(".k-animation-container"))[0]?e.remove():r.remove())},open:function(e,n){var s,r,o={isFixed:!isNaN(parseInt(n,10)),x:e,y:n},l=this.element,h=this.options,c=t(h.anchor),d=l[0]&&l.hasClass("km-widget");if(!this.visible()){if(h.copyAnchorStyles&&(d&&"font-size"==b[0]&&b.shift(),l.css(i.getComputedStyles(c[0],b))),l.data("animating")||this._trigger("open"))return;this._activated=!1,h.modal||(f.unbind(this.downEvent,this._mousedownProxy).bind(this.downEvent,this._mousedownProxy),this._toggleResize(!1),this._toggleResize(!0)),this.wrapper=r=i.wrap(l,h.autosize).css({overflow:"hidden",display:"block",position:"absolute"}).attr("aria-hidden",!1),a.mobileOS.android&&r.css(_,"translatez(0)"),r.css(u),t(h.appendTo)[0]==document.body&&r.css("top","-10000px"),this.flipped=this._position(o),s=this._openAnimation(),"body"!=h.anchor&&this._showDirClass(s),l.data("effects",s.effects).kendoStop(!0).kendoAnimate(s).attr("aria-hidden",!1)}},_location:function(e){var n,s=this.element,r=this.options,o=t(r.anchor),l=s[0]&&s.hasClass("km-widget");r.copyAnchorStyles&&(l&&"font-size"==b[0]&&b.shift(),s.css(i.getComputedStyles(o[0],b))),this.wrapper=n=i.wrap(s,r.autosize).css({overflow:"hidden",display:"block",position:"absolute"}),a.mobileOS.android&&n.css(_,"translatez(0)"),n.css(u),t(r.appendTo)[0]==document.body&&n.css("top","-10000px"),this._position(e||{});var h=n.offset();return{width:i._outerWidth(n),height:i._outerHeight(n),left:h.left,top:h.top}},_openAnimation:function(){var t=v(!0,{},this.options.animation.open);return t.effects=i.parseEffects(t.effects,this.flipped),t},_hideDirClass:function(){var e=t(this.options.anchor),n=((e.attr("class")||"").match(d)||["","down"])[1],s=c+"-"+n;e.removeClass(s).children(p).removeClass("k-state-active").removeClass(s),this.element.removeClass(c+"-"+i.directions[n].reverse)},_showDirClass:function(e){var n=e.effects.slideIn?e.effects.slideIn.direction:"down",s=c+"-"+n;t(this.options.anchor).addClass(s).children(p).addClass("k-state-active").addClass(s),this.element.addClass(c+"-"+i.directions[n].reverse)},position:function(){this.visible()&&(this.flipped=this._position())},toggle:function(){this[this.visible()?"close":"open"]()},visible:function(){return this.element.is(":visible")},close:function(e){var n,s,r,a,o=this.options;if(this.visible()){if(n=this.wrapper[0]?this.wrapper:i.wrap(this.element).hide(),this._toggleResize(!1),this._closing||this._trigger("close"))return void this._toggleResize(!0);this.element.find(".k-popup").each(function(){var i=t(this).data("kendoPopup");i&&i.close(e)}),f.unbind(this.downEvent,this._mousedownProxy),e?s={hide:!0,effects:{}}:(s=v(!0,{},o.animation.close),r=this.element.data("effects"),!(a=s.effects)&&!i.size(a)&&r&&i.size(r)&&(s.effects=r,s.reverse=!0),this._closing=!0),this.element.kendoStop(!0).attr("aria-hidden",!0),n.css({overflow:"hidden"}).attr("aria-hidden",!0),this.element.kendoAnimate(s),e&&this._animationClose()}},_trigger:function(t){return this.trigger(t,{type:t})},_resize:function(t){var e=this;-1!==a.resize.indexOf(t.type)?(clearTimeout(e._resizeTimeout),e._resizeTimeout=setTimeout(function(){e._position(),e._resizeTimeout=null},50)):(!e._hovered||e._activated&&e.element.hasClass("k-list-container"))&&e.close()},_toggleResize:function(t){var e=t?"on":"off",i=a.resize;a.mobileOS.ios||a.mobileOS.android||(i+=" scroll"),this._scrollableParents()[e]("scroll",this._resizeProxy),g[e](i,this._resizeProxy)},_mousedown:function(e){var n=this.element[0],s=this.options,r=t(s.anchor)[0],a=s.toggleTarget,o=i.eventTarget(e),l=t(o).closest(".k-popup"),h=l.parent().parent(".km-shim").length;l=l[0],!h&&l&&l!==this.element[0]||"popover"!==t(e.target).closest("a").data("rel")&&(y(n,o)||y(r,o)||a&&y(t(a)[0],o)||this.close())},_fit:function(t,e,i){var n=0;return t+e>i&&(n=i-(t+e)),t<0&&(n=-t),n},_flip:function(t,e,i,n,s,r,a){var o=0;return a=a||e,r!==s&&"center"!==r&&"center"!==s&&(t+a>n&&(o+=-(i+e)),t+o<0&&(o+=i+e)),o},_scrollableParents:function(){return t(this.options.anchor).parentsUntil("body").filter(function(t,e){return i.isScrollable(e)})},_position:function(e){var n,s,r,c,d,p,f,m=this.element,g=this.wrapper,_=this.options,w=t(_.viewport),b=a.zoomLevel(),y=!!(w[0]==window&&window.innerWidth&&b<=1.02),k=t(_.anchor),x=_.origin.toLowerCase().split(" "),C=_.position.toLowerCase().split(" "),T=this.collisions,S=10002,I=0,D=document.documentElement;if(d=_.viewport===window?{top:window.pageYOffset||document.documentElement.scrollTop||0,left:window.pageXOffset||document.documentElement.scrollLeft||0}:w.offset(),y?(p=window.innerWidth,f=window.innerHeight):(p=w.width(),f=w.height()),y&&D.scrollHeight-D.clientHeight>0&&(p-=(_.isRtl?-1:1)*i.support.scrollbar()),(n=k.parents().filter(g.siblings()))[0])if(r=Math.max(Number(n.css("zIndex")),0))S=r+10;else for(c=(s=k.parentsUntil(n)).length;I<c;I++)(r=Number(t(s[I]).css("zIndex")))&&S<r&&(S=r+10);g.css("zIndex",S),e&&e.isFixed?g.css({left:e.x,top:e.y}):g.css(this._align(x,C));var F=o(g,u,k[0]===g.offsetParent()[0]),E=o(g);k.offsetParent().parent(".k-animation-container,.k-popup,.k-group").length&&(F=o(g,u,!0),E=o(g)),E.top-=d.top,E.left-=d.left,this.wrapper.data("location")||g.data("location",v({},F));var O=v({},E),A=v({},F),M=_.adjustSize;"fit"===T[0]&&(A.top+=this._fit(O.top,h(g)+M.height,f/b)),"fit"===T[1]&&(A.left+=this._fit(O.left,l(g)+M.width,p/b));var H=v({},A),P=h(m),z=h(g);return!g.height()&&P&&(z+=P),"flip"===T[0]&&(A.top+=this._flip(O.top,P,h(k),f/b,x[0],C[0],z)),"flip"===T[1]&&(A.left+=this._flip(O.left,l(m),l(k),p/b,x[1],C[1],l(g))),m.css(u,"absolute"),g.css(A),A.left!=H.left||A.top!=H.top},_align:function(e,i){var n,s=this.wrapper,r=t(this.options.anchor),a=e[0],u=e[1],c=i[0],d=i[1],p=o(r),f=t(this.options.appendTo),m=l(s),g=h(s)||h(s.children().first()),_=l(r),v=h(r),w=p.top,b=p.left,y=Math.round;return f[0]!=document.body&&(w-=(n=o(f)).top,b-=n.left),"bottom"===a&&(w+=v),"center"===a&&(w+=y(v/2)),"bottom"===c&&(w-=g),"center"===c&&(w-=y(g/2)),"right"===u&&(b+=_),"center"===u&&(b+=y(_/2)),"right"===d&&(b-=m),"center"===d&&(b-=y(m/2)),{top:w,left:b}}});n.plugin(k);var x=i.support.stableSort,C="kendoTabKeyTrap",T=r.extend({init:function(e){this.element=t(e),this.element.autoApplyNS(C)},trap:function(){this.element.on("keydown",m(this._keepInTrap,this))},removeTrap:function(){this.element.kendoDestroy(C)},destroy:function(){this.element.kendoDestroy(C),this.element=void 0},shouldTrap:function(){return!0},_keepInTrap:function(t){if(9===t.which&&this.shouldTrap()&&!t.isDefaultPrevented()){var e=this._focusableElements(),i=this._sortFocusableElements(e),n=this._nextFocusable(t,i);this._focus(n),t.preventDefault()}},_focusableElements:function(){var e=this.element.find("a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex], *[contenteditable]").filter(function(e,i){return i.tabIndex>=0&&t(i).is(":visible")&&!t(i).is("[disabled]")});return this.element.is("[tabindex]")&&e.push(this.element[0]),e},_sortFocusableElements:function(t){var e;if(x)e=t.sort(function(t,e){return t.tabIndex-e.tabIndex});else{var i="__k_index";t.each(function(t,e){e.setAttribute(i,t)}),e=t.sort(function(t,e){return t.tabIndex===e.tabIndex?parseInt(t.getAttribute(i),10)-parseInt(e.getAttribute(i),10):t.tabIndex-e.tabIndex}),t.removeAttr(i)}return e},_nextFocusable:function(t,e){var i=e.length,n=e.index(t.target);return e.get((n+(t.shiftKey?-1:1))%i)},_focus:function(t){"IFRAME"!=t.nodeName?(t.focus(),"INPUT"==t.nodeName&&t.setSelectionRange&&this._haveSelectionRange(t)&&t.setSelectionRange(0,t.value.length)):t.contentWindow.document.body.focus()},_haveSelectionRange:function(t){var e=t.type.toLowerCase();return"text"===e||"search"===e||"url"===e||"tel"===e||"password"===e}});n.Popup.TabKeyTrap=T}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.datepicker",["kendo.calendar","kendo.popup","kendo.dateinput"],function(){return function(t,e){var i=window.kendo,n=i.ui,s=n.Widget,r=i.parseDate,a=i.keys,o=i.support,l=i.template,h=i._activeElement,u=".kendoDatePicker",c="click"+u,d=o.mouseAndTouchPresent?i.applyEventMap("up",u.slice(1)):c,p="k-state-default",f="k-state-disabled",m="mousedown"+u,g=i.calendar,_=g.isInRange,v=g.restrictValue,w=g.isEqualDatePart,b=t.extend,y=t.proxy,k=Date;function x(e){var i=e.parseFormats,n=e.format;g.normalize(e),(i=t.isArray(i)?i:[i]).length||i.push("yyyy-MM-dd"),-1===t.inArray(n,i)&&i.splice(0,0,e.format),e.parseFormats=i}function C(t){t.preventDefault()}var T=function(e){var s,r=document.body,a=t("<div />").attr("aria-hidden","true").addClass("k-calendar-container").appendTo(r);this.options=e=e||{},(s=e.id)&&(s+="_dateview",a.attr("id",s),this._dateViewID=s),this.popup=new n.Popup(a,b(e.popup,e,{name:"Popup",isRtl:i.support.isRtl(e.anchor)})),this.div=a,this.value(e.value)};T.prototype={_calendar:function(){var e,s=this.calendar,r=this.options;s||(e=t("<div />").attr("id",i.guid()).appendTo(this.popup.element).on(m,C).on(c,"td:has(.k-link)",y(this._click,this)),this.calendar=s=new n.Calendar(e),this._setOptions(r),i.calendar.makeUnselectable(s.element),s.navigate(this._value||this._current,r.start),this.value(this._value))},_setOptions:function(t){this.calendar.setOptions({focusOnNav:!1,change:t.change,culture:t.culture,dates:t.dates,depth:t.depth,footer:t.footer,format:t.format,max:t.max,min:t.min,month:t.month,weekNumber:t.weekNumber,start:t.start,disableDates:t.disableDates})},setOptions:function(t){var e=this.options,i=t.disableDates;i&&(t.disableDates=g.disabled(i)),this.options=b(e,t,{change:e.change,close:e.close,open:e.open}),this.calendar&&this._setOptions(this.options)},destroy:function(){this.popup.destroy()},open:function(){var t,e=this;e._calendar(),t=e.popup._hovered,e.popup._hovered=!0,e.popup.open(),setTimeout(function(){e.popup._hovered=t},1)},close:function(){this.popup.close()},min:function(t){this._option("min",t)},max:function(t){this._option("max",t)},toggle:function(){this[this.popup.visible()?"close":"open"]()},move:function(t){var e=t.keyCode,i=this.calendar,n=t.ctrlKey&&e==a.DOWN||e==a.ENTER,s=!1;if(t.altKey)e==a.DOWN?(this.open(),t.preventDefault(),s=!0):e==a.UP&&(this.close(),t.preventDefault(),s=!0);else if(this.popup.visible()){if(e==a.ESC||n&&i._cell.hasClass("k-state-selected"))return this.close(),t.preventDefault(),!0;e!=a.SPACEBAR&&(this._current=i._move(t)),s=!0}return s},current:function(t){this._current=t,this.calendar._focus(t)},value:function(t){var e=this.calendar,i=this.options,n=i.disableDates;n&&n(t)&&(t=null),this._value=t,this._current=new k(+v(t,i.min,i.max)),e&&e.value(t)},_click:function(t){-1!==t.currentTarget.className.indexOf("k-state-selected")&&this.close()},_option:function(t,e){var i=this.calendar;this.options[t]=e,i&&i[t](e)}},T.normalize=x,i.DateView=T;var S=s.extend({init:function(e,n){var a,o=this;s.fn.init.call(o,e,n),e=o.element,(n=o.options).disableDates=i.calendar.disabled(n.disableDates),n.min=r(e.attr("min"))||r(n.min),n.max=r(e.attr("max"))||r(n.max),x(n),o._initialOptions=b({},n),o._wrapper(),o.dateView=new T(b({},n,{id:e.attr("id"),anchor:o.wrapper,change:function(){o._change(this.value()),o.close()},close:function(t){o.trigger("close")?t.preventDefault():(e.attr("aria-expanded",!1),a.attr("aria-hidden",!0))},open:function(t){var i,n=o.options;o.trigger("open")?t.preventDefault():(o.element.val()!==o._oldText&&(i=r(e.val(),n.parseFormats,n.culture),o.dateView[i?"current":"value"](i)),e.attr("aria-expanded",!0),a.attr("aria-hidden",!1),o._updateARIA(i))}})),a=o.dateView.div,o._icon();try{e[0].setAttribute("type","text")}catch(t){e[0].type="text"}e.addClass("k-input").attr({role:"combobox","aria-expanded":!1,"aria-owns":o.dateView._dateViewID}),o._reset(),o._template(),e.is("[disabled]")||t(o.element).parents("fieldset").is(":disabled")?o.enable(!1):o.readonly(e.is("[readonly]")),o._createDateInput(n),o._old=o._update(n.value||o.element.val()),o._oldText=e.val(),i.notify(o)},events:["open","close","change"],options:{name:"DatePicker",value:null,footer:"",format:"",culture:"",parseFormats:[],min:new Date(1900,0,1),max:new Date(2099,11,31),start:"month",depth:"month",animation:{},month:{},dates:[],disableDates:null,ARIATemplate:'Current focused date is #=kendo.toString(data.current, "D")#',dateInput:!1,weekNumber:!1},setOptions:function(t){var e=this._value;s.fn.setOptions.call(this,t),(t=this.options).min=r(t.min),t.max=r(t.max),x(t),this.dateView.setOptions(t),this._createDateInput(t),this._dateInput||this.element.val(i.toString(e,t.format,t.culture)),e&&this._updateARIA(e)},_editable:function(t){var e=this,i=e._dateIcon.off(u),n=e.element.off(u),s=e._inputWrapper.off(u),r=t.readonly,a=t.disable;r||a?(s.addClass(a?f:p).removeClass(a?p:f),n.attr("disabled",a).attr("readonly",r).attr("aria-disabled",a)):(s.addClass(p).removeClass(f).on("mouseenter.kendoDatePicker mouseleave.kendoDatePicker",e._toggleHover),n.removeAttr("disabled").removeAttr("readonly").attr("aria-disabled",!1).on("keydown"+u,y(e._keydown,e)).on("focusout"+u,y(e._blur,e)).on("focus"+u,function(){e._inputWrapper.addClass("k-state-focused")}),i.on(d,y(e._click,e)).on(m,C))},readonly:function(t){this._editable({readonly:void 0===t||t,disable:!1})},enable:function(t){this._editable({readonly:!1,disable:!(t=void 0===t||t)})},destroy:function(){s.fn.destroy.call(this),this.dateView.destroy(),this.element.off(u),this._dateIcon.off(u),this._inputWrapper.off(u),this._form&&this._form.off("reset",this._resetHandler)},open:function(){this.dateView.open()},close:function(){this.dateView.close()},min:function(t){return this._option("min",t)},max:function(t){return this._option("max",t)},value:function(t){if(void 0===t)return this._value;this._old=this._update(t),null===this._old&&this.element.val(""),this._oldText=this.element.val()},_toggleHover:function(e){t(e.currentTarget).toggleClass("k-state-hover","mouseenter"===e.type)},_blur:function(){var t=this.element.val();this.close(),t!==this._oldText&&this._change(t),this._inputWrapper.removeClass("k-state-focused")},_click:function(t){this.dateView.toggle(),this._focusElement(t.type)},_focusElement:function(t){var e=this.element;o.touch&&(!o.mouseAndTouchPresent||(t||"").match(/touch/i))||e[0]===h()||e.focus()},_change:function(t){var e,n=this.element.val();t=this._update(t);var s=(e=!i.calendar.isEqualDate(this._old,t))&&!this._typing,r=n!==this.element.val();(s||r)&&this.element.trigger("change"),e&&(this._old=t,this._oldText=this.element.val(),this.trigger("change")),this._typing=!1},_keydown:function(t){var e=this.dateView,i=this.element.val(),n=!1;e.popup.visible()||t.keyCode!=a.ENTER||i===this._oldText?(n=e.move(t),this._updateARIA(e._current),n?this._dateInput&&t.stopImmediatePropagation&&t.stopImmediatePropagation():this._typing=!0):this._change(i)},_icon:function(){var e,i=this.element;(e=i.next("span.k-select"))[0]||(e=t('<span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-calendar"></span></span>').insertAfter(i)),this._dateIcon=e.attr({role:"button","aria-controls":this.dateView._dateViewID})},_option:function(t,e){var i=this.options;if(void 0===e)return i[t];(e=r(e,i.parseFormats,i.culture))&&(i[t]=new k(+e),this.dateView[t](e))},_update:function(t){var e,n=this.options,s=n.min,a=n.max,o=this._value,l=r(t,n.parseFormats,n.culture),h=null===l&&null===o||l instanceof Date&&o instanceof Date;return n.disableDates(l)&&(l=null,this._old||this.element.val()||(t=null)),+l==+o&&h?((e=i.toString(l,n.format,n.culture))!==t&&this.element.val(null===l?t:e),l):(null!==l&&w(l,s)?l=v(l,s,a):_(l,s,a)||(l=null),this._value=l,this.dateView.value(l),this._dateInput&&l?this._dateInput.value(l||t):this.element.val(i.toString(l||t,n.format,n.culture)),this._updateARIA(l),l)},_wrapper:function(){var e,i=this.element;(e=i.parents(".k-datepicker"))[0]||(e=(e=i.wrap("<span />").parent().addClass("k-picker-wrap k-state-default")).wrap("<span />").parent()),e[0].style.cssText=i[0].style.cssText,i.css({width:"100%",height:i[0].style.height}),this.wrapper=e.addClass("k-widget k-datepicker k-header").addClass(i[0].className),this._inputWrapper=t(e[0].firstChild)},_reset:function(){var e=this,i=e.element,n=i.attr("form"),s=n?t("#"+n):i.closest("form");s[0]&&(e._resetHandler=function(){e.value(i[0].defaultValue),e.max(e._initialOptions.max),e.min(e._initialOptions.min)},e._form=s.on("reset",e._resetHandler))},_template:function(){this._ariaTemplate=l(this.options.ARIATemplate)},_createDateInput:function(t){this._dateInput&&(this._dateInput.destroy(),this._dateInput=null),t.dateInput&&(this._dateInput=new n.DateInput(this.element,{culture:t.culture,format:t.format,min:t.min,max:t.max}))},_updateARIA:function(t){var e,i=this.dateView.calendar;this.element.removeAttr("aria-activedescendant"),i&&((e=i._cell).attr("aria-label",this._ariaTemplate({current:t||i.current()})),this.element.attr("aria-activedescendant",e.attr("id")))}});n.plugin(S)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.timepicker",["kendo.popup","kendo.dateinput"],function(){return function(t,e){var i=window.kendo,n=i.keys,s=i.parseDate,r=i._activeElement,a=i._extractFormat,o=i.support,l=o.browser,h=i.ui,u=h.Widget,c=".kendoTimePicker",d="click"+c,p="k-state-default",f="k-state-hover",m="mousedown"+c,g=6e4,_=864e5,v="k-state-disabled",w="aria-activedescendant",b=t.isArray,y=t.extend,k=t.proxy,x=Date,C=new x;C=new x(C.getFullYear(),C.getMonth(),C.getDate(),0,0,0);var T=function(e){var n=e.id;this.options=e,this._dates=[],this.ul=t('<ul tabindex="-1" role="listbox" aria-hidden="true" unselectable="on" class="k-list k-reset"/>').css({overflow:o.kineticScrollNeeded?"":"auto"}).on(d,"li",k(this._click,this)).on("mouseenter"+c,"li",function(){t(this).addClass(f)}).on("mouseleave"+c,"li",function(){t(this).removeClass(f)}),this.list=t("<div class='k-list-container k-list-scroller' unselectable='on'/>").append(this.ul).on(m,E),n&&(this._timeViewID=n+"_timeview",this._optionID=n+"_option_selected",this.ul.attr("id",this._timeViewID)),this._popup(),this._heightHandler=k(this._height,this),this.template=i.template('<li tabindex="-1" role="option" class="k-item" unselectable="on">#=data#</li>',{useWithBlock:!1})};function S(t){return 60*t.getHours()*g+t.getMinutes()*g+1e3*t.getSeconds()+t.getMilliseconds()}function I(t,e,i){var n,s=S(e),r=S(i);return!t||s==r||(s>(n=S(t))&&(n+=_),r<s&&(r+=_),n>=s&&n<=r)}T.prototype={current:function(e){var i=this.options.active;if(void 0===e)return this._current;this._current&&this._current.removeClass("k-state-selected").removeAttr("aria-selected").removeAttr("id"),e&&(e=t(e).addClass("k-state-selected").attr("id",this._optionID).attr("aria-selected",!0),this.scroll(e[0])),this._current=e,i&&i(e)},close:function(){this.popup.close()},destroy:function(){this.ul.off(c),this.list.off(c),this.popup.destroy()},open:function(){var t,e=this;e.ul[0].firstChild||e.bind(),t=e.popup._hovered,e.popup._hovered=!0,e.popup.open(),setTimeout(function(){e.popup._hovered=t},1),e._current&&e.scroll(e._current[0])},dataBind:function(t){for(var e,n=this.options,s=n.format,r=i.toString,a=this.template,o=t.length,l=0,h="";l<o;l++)I(e=t[l],n.min,n.max)&&(h+=a(r(e,s,n.culture)));this._html(h)},refresh:function(){var t,e,n,s,r,a,o,l,h,u,c,d,p,f=this.options,m=f.format,v=(a=new x,o=new x(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0),l=new x(a.getFullYear(),a.getMonth(),a.getDate(),12,0,0),-1*(o.getTimezoneOffset()-l.getTimezoneOffset())),w=v<0,b=f.min,y=f.max,k=S(b),C=S(y),T=S((s=f.interval,(r=new Date(2100,0,1)).setMinutes(-s),r)),I=f.interval*g,D=i.toString,F=this.template,E=new x(+b),O=new x(E),A=0,M="";for(n=w?(_+v*g)/I:_/I,k==C&&T!==C||(k>C&&(C+=_),n=(C-k)/I+1),e=parseInt(n,10);A<n;A++)A&&(h=E,u=I,c=w,d=void 0,p=h.getTimezoneOffset(),h.setTime(h.getTime()+u),c||(d=h.getTimezoneOffset()-p,h.setTime(h.getTime()+d*g))),C&&e==A&&(t=S(E),O<E&&(t+=_),t>C&&(E=new x(+y))),this._dates.push(S(E)),M+=F(D(E,m,f.culture));this._html(M)},bind:function(){var t=this.options.dates;t&&t[0]?this.dataBind(t):this.refresh()},_html:function(t){this.ul[0].innerHTML=t,this.popup.unbind("open",this._heightHandler),this.popup.one("open",this._heightHandler),this.current(null),this.select(this._value)},scroll:function(t){if(t){var e=this.list[0],i=t.offsetTop,n=t.offsetHeight,s=e.scrollTop,r=e.clientHeight,a=i+n;s>i?s=i:a>s+r&&(s=a-r),e.scrollTop=s}},select:function(e){var n,s=this.options,r=this._current;e instanceof Date&&(e=i.toString(e,s.format,s.culture)),"string"==typeof e&&(e=r&&r.text()===e?r:(e=t.grep(this.ul[0].childNodes,function(t){return(t.textContent||t.innerText)==e}))[0]?e:null),n=this._distinctSelection(e),this.current(n)},_distinctSelection:function(e){var i,n;return e&&e.length>1&&(i=S(this._value),n=t.inArray(i,this._dates),e=this.ul.children()[n]),e},setOptions:function(t){var e=this.options;t.min=s(t.min),t.max=s(t.max),this.options=y(e,t,{active:e.active,change:e.change,close:e.close,open:e.open}),this.bind()},toggle:function(){this.popup.visible()?this.close():this.open()},value:function(t){this._value=t,this.ul[0].firstChild&&this.select(t)},_click:function(e){var i=t(e.currentTarget),n=i.text(),s=this.options.dates;s&&s.length>0&&(n=s[i.index()]),e.isDefaultPrevented()||(this.select(i),this.options.change(n,!0),this.close())},_height:function(){var t=this.list,e=t.parent(".k-animation-container"),i=this.options.height;this.ul[0].children.length&&t.add(e).show().height(this.ul[0].scrollHeight>i?i:"auto").hide()},_parse:function(t){var e=this.options,i=S(e.min)!=S(C)?e.min:null,n=S(e.max)!=S(C)?e.max:null,r=this._value||i||n||C;return t instanceof x?t:((t=s(t,e.parseFormats,e.culture))&&(t=new x(r.getFullYear(),r.getMonth(),r.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds())),t)},_adjustListWidth:function(){var t,e,n=this.list,s=n[0].style.width,r=this.options.anchor,a=i._outerWidth;!n.data("width")&&s||(e=(t=window.getComputedStyle?window.getComputedStyle(r[0],null):0)?parseFloat(t.width):a(r),t&&(l.mozilla||l.msie)&&(e+=parseFloat(t.paddingLeft)+parseFloat(t.paddingRight)+parseFloat(t.borderLeftWidth)+parseFloat(t.borderRightWidth)),s=e-(a(n)-n.width()),n.css({fontFamily:r.css("font-family"),width:s}).data("width",s))},_popup:function(){var t=this.list,e=this.options,i=e.anchor;this.popup=new h.Popup(t,y(e.popup,{anchor:i,open:e.open,close:e.close,animation:e.animation,isRtl:o.isRtl(e.anchor)}))},move:function(t){var e=t.keyCode,i=this.ul[0],s=this._current,r=e===n.DOWN;if(e===n.UP||r){if(t.altKey)return void this.toggle(r);(s=r?s?s[0].nextSibling:i.firstChild:s?s[0].previousSibling:i.lastChild)&&this.select(s),this.options.change(this._current.text()),t.preventDefault()}else e!==n.ENTER&&e!==n.TAB&&e!==n.ESC||(t.preventDefault(),s&&this.options.change(s.text(),!0),this.close())}},T.getMilliseconds=S,i.TimeView=T;var D=u.extend({init:function(e,n){var r,a,o=this;u.fn.init.call(o,e,n),e=o.element,(n=o.options).min=s(e.attr("min"))||s(n.min),n.max=s(e.attr("max"))||s(n.max),F(n),o._initialOptions=y({},n),o._wrapper(),o.timeView=a=new T(y({},n,{id:e.attr("id"),anchor:o.wrapper,format:n.format,change:function(t,i){i?o._change(t):e.val(t)},open:function(t){o.timeView._adjustListWidth(),o.trigger("open")?t.preventDefault():(e.attr("aria-expanded",!0),r.attr("aria-hidden",!1))},close:function(t){o.trigger("close")?t.preventDefault():(e.attr("aria-expanded",!1),r.attr("aria-hidden",!0))},active:function(t){e.removeAttr(w),t&&e.attr(w,a._optionID)}})),r=a.ul,o._icon(),o._reset();try{e[0].setAttribute("type","text")}catch(t){e[0].type="text"}if(e.addClass("k-input").attr({role:"combobox","aria-expanded":!1,"aria-owns":a._timeViewID}),e.is("[disabled]")||t(o.element).parents("fieldset").is(":disabled")?o.enable(!1):o.readonly(e.is("[readonly]")),n.dateInput){var l=n.min,c=n.max,d=new x;S(l)==S(c)&&(l=new x(d.getFullYear(),d.getMonth(),d.getDate(),0,0,0),c=new x(d.getFullYear(),d.getMonth(),d.getDate(),24,0,0)),o._dateInput=new h.DateInput(e,{culture:n.culture,format:n.format,min:l,max:c,value:n.value})}o._old=o._update(n.value||o.element.val()),o._oldText=e.val(),i.notify(o)},options:{name:"TimePicker",min:C,max:C,format:"",dates:[],parseFormats:[],value:null,interval:30,height:200,animation:{},dateInput:!1},events:["open","close","change"],setOptions:function(t){var e=this._value;u.fn.setOptions.call(this,t),F(t=this.options),this.timeView.setOptions(t),e&&this.element.val(i.toString(e,t.format,t.culture))},dataBind:function(t){b(t)&&this.timeView.dataBind(t)},_editable:function(t){var e=this,i=t.disable,n=t.readonly,s=e._arrow.off(c),r=e.element.off(c),a=e._inputWrapper.off(c);n||i?(a.addClass(i?v:p).removeClass(i?p:v),r.attr("disabled",i).attr("readonly",n).attr("aria-disabled",i)):(a.addClass(p).removeClass(v).on("mouseenter.kendoTimePicker mouseleave.kendoTimePicker",e._toggleHover),r.removeAttr("disabled").removeAttr("readonly").attr("aria-disabled",!1).on("keydown"+c,k(e._keydown,e)).on("focusout"+c,k(e._blur,e)).on("focus"+c,function(){e._inputWrapper.addClass("k-state-focused")}),s.on(d,k(e._click,e)).on(m,E))},readonly:function(t){this._editable({readonly:void 0===t||t,disable:!1})},enable:function(t){this._editable({readonly:!1,disable:!(t=void 0===t||t)})},destroy:function(){u.fn.destroy.call(this),this.timeView.destroy(),this.element.off(c),this._arrow.off(c),this._inputWrapper.off(c),this._form&&this._form.off("reset",this._resetHandler)},close:function(){this.timeView.close()},open:function(){this.timeView.open()},min:function(t){return this._option("min",t)},max:function(t){return this._option("max",t)},value:function(t){if(void 0===t)return this._value;this._old=this._update(t),null===this._old&&this.element.val(""),this._oldText=this.element.val()},_blur:function(){var t=this.element.val();this.close(),t!==this._oldText&&this._change(t),this._inputWrapper.removeClass("k-state-focused")},_click:function(){var t=this.element;this.timeView.toggle(),o.touch||t[0]===r()||t.focus()},_change:function(t){t=this._update(t),+this._old!=+t&&(this._old=t,this._oldText=this.element.val(),this._typing||this.element.trigger("change"),this.trigger("change")),this._typing=!1},_icon:function(){var e,i=this.element;(e=i.next("span.k-select"))[0]||(e=t('<span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-clock"></span></span>').insertAfter(i)),this._arrow=e.attr({role:"button","aria-controls":this.timeView._timeViewID})},_keydown:function(t){var e=t.keyCode,i=this.timeView,s=this.element.val();i.popup.visible()||t.altKey?(i.move(t),this._dateInput&&t.stopImmediatePropagation&&t.stopImmediatePropagation()):e===n.ENTER&&s!==this._oldText?this._change(s):this._typing=!0},_option:function(t,e){var i=this.options;if(void 0===e)return i[t];(e=this.timeView._parse(e))&&(e=new x(+e),i[t]=e,this.timeView.options[t]=e,this.timeView.bind())},_toggleHover:function(e){t(e.currentTarget).toggleClass(f,"mouseenter"===e.type)},_update:function(t){var e=this.options,n=this.timeView,s=n._parse(t);return I(s,e.min,e.max)||(s=null),this._value=s,this._dateInput&&s?this._dateInput.value(s||t):this.element.val(i.toString(s||t,e.format,e.culture)),n.value(s),s},_wrapper:function(){var e,i=this.element;(e=i.parents(".k-timepicker"))[0]||(e=(e=i.wrap("<span/>").parent().addClass("k-picker-wrap k-state-default")).wrap("<span/>").parent()),e[0].style.cssText=i[0].style.cssText,this.wrapper=e.addClass("k-widget k-timepicker k-header").addClass(i[0].className),i.css({width:"100%",height:i[0].style.height}),this._inputWrapper=t(e[0].firstChild)},_reset:function(){var e=this,i=e.element,n=i.attr("form"),s=n?t("#"+n):i.closest("form");s[0]&&(e._resetHandler=function(){e.value(i[0].defaultValue),e.max(e._initialOptions.max),e.min(e._initialOptions.min)},e._form=s.on("reset",e._resetHandler))}});function F(t){var e=t.parseFormats;t.format=a(t.format||i.getCulture(t.culture).calendars.standard.patterns.t),(e=b(e)?e:[e]).splice(0,0,t.format),t.parseFormats=e}function E(t){t.preventDefault()}h.plugin(D)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.datetimepicker",["kendo.datepicker","kendo.timepicker"],function(){return function(t,e){var i=window.kendo,n=i.TimeView,s=i.parseDate,r=i.support,a=i._activeElement,o=i._extractFormat,l=i.calendar,h=l.isInRange,u=l.restrictValue,c=l.isEqualDatePart,d=n.getMilliseconds,p=i.ui,f=p.Widget,m=".kendoDateTimePicker",g=r.mouseAndTouchPresent?i.applyEventMap("up",m.slice(1)):"click.kendoDateTimePicker",_="k-state-default",v="k-state-disabled",w="mousedown"+m,b="aria-activedescendant",y="aria-expanded",k="aria-hidden",x="aria-owns",C=Date,T=new C(1800,0,1),S=new C(2099,11,31),I={view:"date"},D={view:"time"},F=t.extend,E=f.extend({init:function(e,n){f.fn.init.call(this,e,n),e=this.element,(n=this.options).disableDates=i.calendar.disabled(n.disableDates),n.min=s(e.attr("min"))||s(n.min),n.max=s(e.attr("max"))||s(n.max),M(n),this._initialOptions=F({},n),this._wrapper(),this._views(),this._icons(),this._reset(),this._template();try{e[0].setAttribute("type","text")}catch(t){e[0].type="text"}e.addClass("k-input").attr({role:"combobox","aria-expanded":!1}),this._midnight=this._calculateMidnight(n.min,n.max),e.is("[disabled]")||t(this.element).parents("fieldset").is(":disabled")?this.enable(!1):this.readonly(e.is("[readonly]")),this._createDateInput(n),this._old=this._update(n.value||this.element.val()),this._oldText=e.val(),i.notify(this)},options:{name:"DateTimePicker",value:null,format:"",timeFormat:"",culture:"",parseFormats:[],dates:[],disableDates:null,min:new C(T),max:new C(S),interval:30,height:200,footer:"",start:"month",depth:"month",animation:{},month:{},ARIATemplate:'Current focused date is #=kendo.toString(data.current, "d")#',dateButtonText:"Open the date view",timeButtonText:"Open the time view",dateInput:!1,weekNumber:!1},events:["open","close","change"],setOptions:function(t){var e,n,r,a=this._value;f.fn.setOptions.call(this,t),(t=this.options).min=e=s(t.min),t.max=n=s(t.max),M(t),this._midnight=this._calculateMidnight(t.min,t.max),r=t.value||this._value||this.dateView._current,e&&!c(e,r)&&(e=new C(T)),n&&!c(n,r)&&(n=new C(S)),this.dateView.setOptions(t),this.timeView.setOptions(F({},t,{format:t.timeFormat,min:e,max:n})),this._createDateInput(t),this._dateInput||this.element.val(i.toString(a,t.format,t.culture)),a&&this._updateARIA(a)},_editable:function(e){var i=this,n=i.element.off(m),s=i._dateIcon.off(m),r=i._timeIcon.off(m),a=i._inputWrapper.off(m),o=e.readonly,l=e.disable;o||l?(a.addClass(l?v:_).removeClass(l?_:v),n.attr("disabled",l).attr("readonly",o).attr("aria-disabled",l)):(a.addClass(_).removeClass(v).on("mouseenter.kendoDateTimePicker mouseleave.kendoDateTimePicker",i._toggleHover),n.removeAttr("disabled").removeAttr("readonly").attr("aria-disabled",!1).on("keydown"+m,t.proxy(i._keydown,i)).on("focus"+m,function(){i._inputWrapper.addClass("k-state-focused")}).on("focusout"+m,function(){i._inputWrapper.removeClass("k-state-focused"),n.val()!==i._oldText&&i._change(n.val()),i.close("date"),i.close("time")}),s.on(w,A).on(g,function(t){i.toggle("date"),i._focusElement(t.type)}),r.on(w,A).on(g,function(t){i.toggle("time"),i._focusElement(t.type)}))},_focusElement:function(t){var e=this.element;r.touch&&(!r.mouseAndTouchPresent||(t||"").match(/touch/i))||e[0]===a()||e.focus()},readonly:function(t){this._editable({readonly:void 0===t||t,disable:!1})},enable:function(t){this._editable({readonly:!1,disable:!(t=void 0===t||t)})},destroy:function(){f.fn.destroy.call(this),this.dateView.destroy(),this.timeView.destroy(),this.element.off(m),this._dateIcon.off(m),this._timeIcon.off(m),this._inputWrapper.off(m),this._form&&this._form.off("reset",this._resetHandler)},close:function(t){"time"!==t&&(t="date"),this[t+"View"].close()},open:function(t){"time"!==t&&(t="date"),this[t+"View"].open()},min:function(t){return this._option("min",t)},max:function(t){return this._option("max",t)},toggle:function(t){var e="timeView";"time"!==t?t="date":e="dateView",this[t+"View"].toggle(),this[e].close()},value:function(t){if(void 0===t)return this._value;this._old=this._update(t),null===this._old&&this.element.val(""),this._oldText=this.element.val()},_change:function(t){var e,i=this.element.val();t=this._update(t);var n=(e=+this._old!=+t)&&!this._typing,s=i!==this.element.val();(n||s)&&this.element.trigger("change"),e&&(this._old=t,this._oldText=this.element.val(),this.trigger("change")),this._typing=!1},_option:function(t,e){var i,n,r=this.options,a=this.timeView,o=a.options,l=this._value||this._old;if(void 0===e)return r[t];if(e=s(e,r.parseFormats,r.culture)){if(r.min.getTime()===r.max.getTime()&&(o.dates=[]),r[t]=new C(e.getTime()),this.dateView[t](e),this._midnight=this._calculateMidnight(r.min,r.max),l&&(i=c(r.min,l),n=c(r.max,l)),i||n){if(o[t]=e,i&&!n&&(o.max=O(r.interval)),n){if(this._midnight)return void a.dataBind([S]);i||(o.min=T)}}else o.max=S,o.min=T;a.bind()}},_toggleHover:function(e){t(e.currentTarget).toggleClass("k-state-hover","mouseenter"===e.type)},_update:function(e){var n,r,a,o,l,d=this.options,p=d.min,f=d.max,m=d.dates,g=this.timeView,_=this._value,v=s(e,d.parseFormats,d.culture),w=null===v&&null===_||v instanceof Date&&_ instanceof Date;return d.disableDates&&d.disableDates(v)&&(v=null,this._old||this.element.val()||(e=null)),+v==+_&&w?((l=i.toString(v,d.format,d.culture))!==e&&(this.element.val(null===v?e:l),e instanceof String&&this.element.trigger("change")),v):(null!==v&&c(v,p)?v=u(v,p,f):h(v,p,f)||(v=null),this._value=v,g.value(v),this.dateView.value(v),v&&(a=this._old,r=g.options,m[0]&&(m=t.grep(m,function(t){return c(v,t)}))[0]&&(g.dataBind(m),o=!0),o||(c(v,p)&&(r.min=p,r.max=O(d.interval),n=!0),c(v,f)&&(this._midnight?(g.dataBind([S]),o=!0):(r.max=f,n||(r.min=T),n=!0))),!o&&(!a&&n||a&&!c(a,v))&&(n||(r.max=S,r.min=T),g.bind())),this._dateInput&&v?this._dateInput.value(v||e):this.element.val(i.toString(v||e,d.format,d.culture)),this._updateARIA(v),v)},_keydown:function(t){var e=this.dateView,n=this.timeView,s=this.element.val(),r=e.popup.visible(),a=this._dateInput&&t.stopImmediatePropagation;t.altKey&&t.keyCode===i.keys.DOWN?this.toggle(r?"time":"date"):r?(e.move(t),this._updateARIA(e._current)):n.popup.visible()?n.move(t):t.keyCode===i.keys.ENTER&&s!==this._oldText?this._change(s):(this._typing=!0,a=!1),a&&t.stopImmediatePropagation()},_views:function(){var t,e,r,a,o,l,u=this,c=u.element,d=u.options,p=c.attr("id");u.dateView=t=new i.DateView(F({},d,{id:p,anchor:u.wrapper,change:function(){var e,n,s=t.calendar.value(),r=+s,a=+d.min,o=+d.max;r!==a&&r!==o||(e=r===a?a:o,(e=new C(u._value||e)).setFullYear(s.getFullYear(),s.getMonth(),s.getDate()),h(e,a,o)&&(s=e)),u._value&&(n=i.date.setHours(new Date(s),u._value),h(n,a,o)&&(s=n)),u._change(s),u.close("date")},close:function(t){u.trigger("close",I)?t.preventDefault():(c.attr(y,!1),r.attr(k,!0),e.popup.visible()||c.removeAttr(x))},open:function(e){u.trigger("open",I)?e.preventDefault():(c.val()!==u._oldText&&(l=s(c.val(),d.parseFormats,d.culture),u.dateView[l?"current":"value"](l)),r.attr(k,!1),c.attr(y,!0).attr(x,t._dateViewID),u._updateARIA(l))}})),r=t.div,o=d.min.getTime(),u.timeView=e=new n({id:p,value:d.value,anchor:u.wrapper,animation:d.animation,format:d.timeFormat,culture:d.culture,height:d.height,interval:d.interval,min:new C(T),max:new C(S),dates:o===d.max.getTime()?[new Date(o)]:[],parseFormats:d.parseFormats,change:function(n,s){(n=e._parse(n))<d.min?(n=new C(+d.min),e.options.min=n):n>d.max&&(n=new C(+d.max),e.options.max=n),s?(u._timeSelected=!0,u._change(n)):(c.val(i.toString(n,d.format,d.culture)),t.value(n),u._updateARIA(n))},close:function(e){u.trigger("close",D)?e.preventDefault():(a.attr(k,!0),c.attr(y,!1),t.popup.visible()||c.removeAttr(x))},open:function(t){e._adjustListWidth(),u.trigger("open",D)?t.preventDefault():(c.val()!==u._oldText&&(l=s(c.val(),d.parseFormats,d.culture),u.timeView.value(l)),a.attr(k,!1),c.attr(y,!0).attr(x,e._timeViewID),e.options.active(e.current()))},active:function(t){c.removeAttr(b),t&&c.attr(b,e._optionID)}}),a=e.ul},_icons:function(){var e,i=this.element,n=this.options;(e=i.next("span.k-select"))[0]||(e=t('<span unselectable="on" class="k-select"><span class="k-link k-link-date" aria-label="'+n.dateButtonText+'"><span unselectable="on" class="k-icon k-i-calendar"></span></span><span class="k-link k-link-time" aria-label="'+n.timeButtonText+'"><span unselectable="on" class="k-icon k-i-clock"></span></span></span>').insertAfter(i)),e=(e=e.children()).children(),this._dateIcon=e.eq(0).attr("aria-controls",this.dateView._dateViewID),this._timeIcon=e.eq(1).attr("aria-controls",this.timeView._timeViewID)},_wrapper:function(){var e,i=this.element;(e=i.parents(".k-datetimepicker"))[0]||(e=(e=i.wrap("<span/>").parent().addClass("k-picker-wrap k-state-default")).wrap("<span/>").parent()),e[0].style.cssText=i[0].style.cssText,i.css({width:"100%",height:i[0].style.height}),this.wrapper=e.addClass("k-widget k-datetimepicker k-header").addClass(i[0].className),this._inputWrapper=t(e[0].firstChild)},_reset:function(){var e=this,i=e.element,n=i.attr("form"),s=n?t("#"+n):i.closest("form");s[0]&&(e._resetHandler=function(){e.value(i[0].defaultValue),e.max(e._initialOptions.max),e.min(e._initialOptions.min)},e._form=s.on("reset",e._resetHandler))},_template:function(){this._ariaTemplate=i.template(this.options.ARIATemplate)},_createDateInput:function(t){this._dateInput&&(this._dateInput.destroy(),this._dateInput=null),t.dateInput&&(this._dateInput=new p.DateInput(this.element,{culture:t.culture,format:t.format,min:t.min,max:t.max}))},_calculateMidnight:function(t,e){return d(t)+d(e)===0},_updateARIA:function(t){var e,i=this.dateView.calendar;this.element.removeAttr(b),i&&((e=i._cell).attr("aria-label",this._ariaTemplate({current:t||i.current()})),this.element.attr(b,e.attr("id")))}});function O(t){var e=new Date(2100,0,1);return e.setMinutes(-t),e}function A(t){t.preventDefault()}function M(e){var n,s=i.getCulture(e.culture).calendars.standard.patterns,r=!e.parseFormats.length;e.format=o(e.format||s.g),e.timeFormat=n=o(e.timeFormat||s.t),i.DateView.normalize(e),r&&e.parseFormats.unshift("yyyy-MM-ddTHH:mm:ss"),-1===t.inArray(n,e.parseFormats)&&e.parseFormats.push(n)}p.plugin(E)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.list",["kendo.data","kendo.popup"],function(){return function(t,e){var i=window.kendo,n=i.ui,s=i._outerHeight,r=n.Widget,a=i.keys,o=i.support,l=i.htmlEncode,h=i._activeElement,u=i._outerWidth,c=i.data.ObservableArray,d="change",p="k-state-hover",f=t.extend,m=t.proxy,g=t.isArray,_=o.browser,v=_.msie,w=v&&_.version<9,b=/"/g,y={ComboBox:"DropDownList",DropDownList:"ComboBox"},k=i.ui.DataBoundWidget.extend({init:function(e,i){var n,s=this.ns;r.fn.init.call(this,e,i),e=this.element,i=this.options,this._isSelect=e.is("select"),this._isSelect&&this.element[0].length&&(i.dataSource||(i.dataTextField=i.dataTextField||"text",i.dataValueField=i.dataValueField||"value")),this.ul=t('<ul unselectable="on" class="k-list k-reset"/>').attr({tabIndex:-1,"aria-hidden":!0}),this.list=t("<div class='k-list-container'/>").append(this.ul).on("mousedown"+s,m(this._listMousedown,this)),(n=e.attr("id"))&&(this.list.attr("id",n+"-list"),this.ul.attr("id",n+"_listbox")),this._header(),this._noData(),this._footer(),this._accessors(),this._initValue()},options:{valuePrimitive:!1,footerTemplate:"",headerTemplate:"",noDataTemplate:"No data found."},setOptions:function(t){r.fn.setOptions.call(this,t),t&&t.enable!==e&&(t.enabled=t.enable),this._header(),this._noData(),this._footer(),this._renderFooter(),this._renderNoData()},focus:function(){this._focused.focus()},readonly:function(t){this._editable({readonly:t===e||t,disable:!1})},enable:function(t){this._editable({readonly:!1,disable:!(t=t===e||t)})},_header:function(){var e=t(this.header),n=this.options.headerTemplate;if(this._angularElement(e,"cleanup"),i.destroy(e),e.remove(),n){var s="function"!=typeof n?i.template(n):n;e=t(s({})),this.header=e[0]?e:null,this.list.prepend(e),this._angularElement(this.header,"compile")}else this.header=null},_noData:function(){var e=t(this.noData),n=this.options.noDataTemplate;this.angular("cleanup",function(){return{elements:e}}),i.destroy(e),e.remove(),n?(this.noData=t('<div class="k-nodata" style="display:none"><div></div></div>').appendTo(this.list),this.noDataTemplate="function"!=typeof n?i.template(n):n):this.noData=null},_footer:function(){var e=t(this.footer),n=this.options.footerTemplate;this._angularElement(e,"cleanup"),i.destroy(e),e.remove(),n?(this.footer=t('<div class="k-footer"></div>').appendTo(this.list),this.footerTemplate="function"!=typeof n?i.template(n):n):this.footer=null},_listOptions:function(e){var n=this,s=n.options,r=s.virtual,a={change:m(n._listChange,n)},o=m(n._listBound,n);return r="object"==typeof r?r:{},(e=t.extend({autoBind:!1,selectable:!0,dataSource:n.dataSource,click:m(n._click,n),activate:m(n._activateItem,n),deactivate:m(n._deactivateItem,n),dataBinding:function(){n.trigger("dataBinding")},dataBound:o,height:s.height,dataValueField:s.dataValueField,dataTextField:s.dataTextField,groupTemplate:s.groupTemplate,fixedGroupTemplate:s.fixedGroupTemplate,template:s.template},e,r,a)).template||(e.template="#:"+i.expr(e.dataTextField,"data")+"#"),s.$angular&&(e.$angular=s.$angular),e},_initList:function(){var t=this._listOptions({selectedItemChange:m(this._listChange,this)});this.options.virtual?this.listView=new i.ui.VirtualList(this.ul,t):this.listView=new i.ui.StaticList(this.ul,t),this.listView.bind("listBound",m(this._listBound,this)),this._setListValue()},_setListValue:function(t){(t=t||this.options.value)!==e&&this.listView.value(t).done(m(this._updateSelectionState,this))},_updateSelectionState:t.noop,_listMousedown:function(t){this.filterInput&&this.filterInput[0]===t.target||t.preventDefault()},_isFilterEnabled:function(){var t=this.options.filter;return t&&"none"!==t},_hideClear:function(){this._clear&&this._clear.addClass("k-hidden")},_showClear:function(){this._clear&&this._clear.removeClass("k-hidden")},_clearValue:function(){this._clearText(),this._accessor(""),this.listView.value([]),this._isFilterEnabled()&&!this.options.enforceMinLength&&(this._isSelect&&(this._customOption=e),this._filter({word:"",open:!1})),this._change()},_clearText:function(){this.text("")},_clearFilter:function(){this.options.virtual||this.listView.bound(!1),this._filterSource()},_filterSource:function(e,i){var n=this.options,s=this.dataSource,r=f({},s.filter()||{}),a=e||r.filters&&r.filters.length&&!e,o=F(r,n.dataTextField);if(!e&&!o||!this.trigger("filtering",{filter:e})){var l={filters:[],logic:"and"};D(e)&&t.trim(e.value).length&&l.filters.push(e),D(r)&&(l.logic===r.logic?l.filters=l.filters.concat(r.filters):l.filters.push(r)),this._cascading&&this.listView.setDSFilter(l);var h=f({},{page:a?1:s.page(),pageSize:a?s.options.pageSize:s.pageSize(),sort:s.sort(),filter:s.filter(),group:s.group(),aggregate:s.aggregate()},{filter:l});return s[i?"read":"query"](s._mergeState(h))}},_angularElement:function(t,e){t&&this.angular(e,function(){return{elements:t}})},_renderNoData:function(){var t=this.noData;t&&(this._angularElement(t,"cleanup"),t.children(":first").html(this.noDataTemplate({instance:this})),this._angularElement(t,"compile"))},_toggleNoData:function(e){t(this.noData).toggle(e)},_toggleHeader:function(t){this.listView.content.prev(".k-group-header").toggle(t)},_renderFooter:function(){var t=this.footer;t&&(this._angularElement(t,"cleanup"),t.html(this.footerTemplate({instance:this})),this._angularElement(t,"compile"))},_allowOpening:function(){return this.options.noDataTemplate||this.dataSource.flatView().length},_initValue:function(){var t=this.options.value;null!==t?this.element.val(t):(t=this._accessor(),this.options.value=t),this._old=t},_ignoreCase:function(){var t,e=this.dataSource.reader.model;e&&e.fields&&(t=e.fields[this.options.dataTextField])&&t.type&&"string"!==t.type&&(this.options.ignoreCase=!1)},_focus:function(t){return this.listView.focus(t)},_filter:function(t){var e=this.options,i=e.ignoreCase,n=e.dataTextField,s={value:i?t.word.toLowerCase():t.word,field:n,operator:e.filter,ignoreCase:i};this._open=t.open,this._filterSource(s)},_clearButton:function(){var e=this.options.messages&&this.options.messages.clear?this.options.messages.clear:"clear";this._clear||(this._clear=t('<span unselectable="on" class="k-icon k-clear-value k-i-close" title="'+e+'"></span>').attr({role:"button",tabIndex:-1})),this.options.clearButton||this._clear.remove(),this._hideClear()},search:function(e){var i=this.options;e="string"==typeof e?e:this._inputValue(),clearTimeout(this._typingTimeout),(!i.enforceMinLength&&!e.length||e.length>=i.minLength)&&(this._state="filter",this._isFilterEnabled()?(t.trim(e).length&&this.listView?this.listView._emptySearch=!1:this.listView._emptySearch=!0,this._filter({word:e,open:!0})):this._searchByWord(e))},current:function(t){return this._focus(t)},items:function(){return this.ul[0].children},destroy:function(){var t=this.ns;r.fn.destroy.call(this),this._unbindDataSource(),this.listView.destroy(),this.list.off(t),this.popup.destroy(),this._form&&this._form.off("reset",this._resetHandler)},dataItem:function(i){if(i===e)return this.listView.selectedDataItems()[0];if("number"!=typeof i){if(this.options.virtual)return this.dataSource.getByUid(t(i).data("uid"));i=t(this.items()).index(i)}return this.dataSource.flatView()[i]},_activateItem:function(){var t=this.listView.focus();t&&this._focused.add(this.filterInput).attr("aria-activedescendant",t.attr("id"))},_deactivateItem:function(){this._focused.add(this.filterInput).removeAttr("aria-activedescendant")},_accessors:function(){var t=this.element,e=this.options,n=i.getter,s=t.attr(i.attr("text-field")),r=t.attr(i.attr("value-field"));!e.dataTextField&&s&&(e.dataTextField=s),!e.dataValueField&&r&&(e.dataValueField=r),this._text=n(e.dataTextField),this._value=n(e.dataValueField)},_aria:function(t){var i=this.options,n=this._focused.add(this.filterInput);i.suggest!==e&&n.attr("aria-autocomplete",i.suggest?"both":"list"),t=t?t+" "+this.ul[0].id:this.ul[0].id,n.attr("aria-owns",t),this.ul.attr("aria-live",this._isFilterEnabled()?"polite":"off"),this._ariaLabel()},_ariaLabel:function(){var e=this._focused,i=this.element,n=i.attr("id"),s=t('label[for="'+n+'"]'),r=i.attr("aria-label"),a=i.attr("aria-labelledby");if(e!==i)if(r)e.attr("aria-label",r);else if(a)e.attr("aria-labelledby",a);else if(s.length){var o=s.attr("id")||this._generateLabelId(s,n);e.attr("aria-labelledby",o)}},_generateLabelId:function(t,e){var i=e+"_label";return t.attr("id",i),i},_blur:function(){this._change(),this.close()},_change:function(){var t,i=this.selectedIndex,n=this.options.value,s=this.value();this._isSelect&&!this.listView.bound()&&n&&(s=n),s!==x(this._old,typeof s)?t=!0:this._valueBeforeCascade!==e&&this._valueBeforeCascade!==x(this._old,typeof this._valueBeforeCascade)&&this._userTriggered?t=!0:i===e||i===this._oldIndex||this.listView.isFiltered()||(t=!0),t&&(null===this._old||""===s?this._valueBeforeCascade=this._old=s:this._valueBeforeCascade=this._old=this.dataItem()?this.dataItem()[this.options.dataValueField]||this.dataItem():null,this._oldIndex=i,this._typing||this.element.trigger(d),this.trigger(d)),this.typing=!1},_data:function(){return this.dataSource.view()},_enable:function(){var t=this.options,i=this.element.is("[disabled]");t.enable!==e&&(t.enabled=t.enable),!t.enabled||i?this.enable(!1):this.readonly(this.element.is("[readonly]"))},_dataValue:function(t){var i=this._value(t);return i===e&&(i=this._text(t)),i},_offsetHeight:function(){var e=0;return this.listView.content.prevAll(":visible").each(function(){var i=t(this);e+=s(i,!0)}),e},_height:function(e){var i,n=this.list,r=this.options.height,a=this.popup.visible();if(e||this.options.noDataTemplate){if(i=n.add(n.parent(".k-animation-container")).show(),!n.is(":visible"))return void i.hide();r=this.listView.content[0].scrollHeight>r?r:"auto",i.height(r),"auto"!==r&&(r=r-this._offsetHeight()-(s(t(this.footer))||0)),this.listView.content.height(r),a||i.hide()}return r},_openHandler:function(t){this._adjustListWidth(),this.trigger("open")?t.preventDefault():(this._focused.attr("aria-expanded",!0),this.ul.attr("aria-hidden",!1))},_adjustListWidth:function(){var t,e,i=this.list,n=i[0].style.width,s=this.wrapper;if(i.data("width")||!n)return t=window.getComputedStyle?window.getComputedStyle(s[0],null):0,e=parseFloat(t&&t.width)||u(s),t&&_.msie&&(e+=parseFloat(t.paddingLeft)+parseFloat(t.paddingRight)+parseFloat(t.borderLeftWidth)+parseFloat(t.borderRightWidth)),n="border-box"!==i.css("box-sizing")?e-(u(i)-i.width()):e,i.css({fontFamily:s.css("font-family"),width:this.options.autoWidth?"auto":n,minWidth:n,whiteSpace:this.options.autoWidth?"nowrap":"normal"}).data("width",n),!0},_closeHandler:function(t){this.trigger("close")?t.preventDefault():(this._focused.attr("aria-expanded",!1),this.ul.attr("aria-hidden",!0))},_focusItem:function(){var t=this.listView,i=!t.focus(),n=S(t.select());n===e&&this.options.highlightFirst&&i&&(n=0),n!==e?t.focus(n):i&&t.scrollToIndex(0)},_calculateGroupPadding:function(t){var e=this.ul.children(".k-first:first"),n=this.listView.content.prev(".k-group-header"),s=0;n[0]&&"none"!==n[0].style.display&&("auto"!==t&&(s=i.support.scrollbar()),s+=parseFloat(e.css("border-right-width"),10)+parseFloat(e.children(".k-group").css("padding-right"),10),n.css("padding-right",s))},_calculatePopupHeight:function(t){var e=this._height(this.dataSource.flatView().length||t);this._calculateGroupPadding(e)},_resizePopup:function(t){this.options.virtual||(this.popup.element.is(":visible")?this._calculatePopupHeight(t):this.popup.one("open",function(t){return m(function(){this._calculatePopupHeight(t)},this)}.call(this,t)))},_popup:function(){this.popup=new n.Popup(this.list,f({},this.options.popup,{anchor:this.wrapper,open:m(this._openHandler,this),close:m(this._closeHandler,this),animation:this.options.animation,isRtl:o.isRtl(this.wrapper),autosize:this.options.autoWidth}))},_makeUnselectable:function(){w&&this.list.find("*").not(".k-textbox").attr("unselectable","on")},_toggleHover:function(e){t(e.currentTarget).toggleClass(p,"mouseenter"===e.type)},_toggle:function(t,i){var n=o.mobileOS&&(o.touch||o.MSPointers||o.pointers);t=t!==e?t:!this.popup.visible(),i||n||this._focused[0]===h()||(this._prevent=!0,this._focused.focus(),this._prevent=!1),this[t?"open":"close"]()},_triggerCascade:function(){this._cascadeTriggered&&this.value()===x(this._cascadedValue,typeof this.value())||(this._cascadedValue=this.value(),this._cascadeTriggered=!0,this.trigger("cascade",{userTriggered:this._userTriggered}))},_triggerChange:function(){this._valueBeforeCascade!==this.value()&&this.trigger(d)},_unbindDataSource:function(){this.dataSource.unbind("requestStart",this._requestStartHandler).unbind("requestEnd",this._requestEndHandler).unbind("error",this._errorHandler)},requireValueMapper:function(t,e){if(((t.value instanceof Array?t.value.length:t.value)||(e instanceof Array?e.length:e))&&t.virtual&&"function"!=typeof t.virtual.valueMapper)throw new Error("ValueMapper is not provided while the value is being set. See http://docs.telerik.com/kendo-ui/controls/editors/combobox/virtualization#the-valuemapper-function")}});function x(t,i){return t!==e&&""!==t&&null!==t&&("boolean"===i?t=Boolean(t):"number"===i?t=Number(t):"string"===i&&(t=t.toString())),t}f(k,{inArray:function(t,e){var i,n,s=e.children;if(!t||t.parentNode!==e)return-1;for(i=0,n=s.length;i<n;i++)if(t===s[i])return i;return-1},unifyType:x}),i.ui.List=k,n.Select=k.extend({init:function(t,e){k.fn.init.call(this,t,e),this._initial=this.element.val()},setDataSource:function(t){var e;this.options.dataSource=t,this._dataSource(),this.listView.bound()&&(this._initialIndex=null,this.listView._current=null),this.listView.setDataSource(this.dataSource),this.options.autoBind&&this.dataSource.fetch(),(e=this._parentWidget())&&this._cascadeSelect(e)},close:function(){this.popup.close()},select:function(t){var i=this;return t===e?i.selectedIndex:i._select(t).done(function(){i._cascadeValue=i._old=i._accessor(),i._oldIndex=i.selectedIndex})},_accessor:function(t,e){return this[this._isSelect?"_accessorSelect":"_accessorInput"](t,e)},_accessorInput:function(t){var i=this.element[0];if(t===e)return i.value;null===t&&(t=""),i.value=t},_accessorSelect:function(t,i){var n=this.element[0];if(t===e)return I(n).value||"";I(n).selected=!1,i===e&&(i=-1),null!==t&&""!==t&&-1==i?this._custom(t):t?n.value=t:n.selectedIndex=i},_syncValueAndText:function(){return!0},_custom:function(e){var i=this.element,n=this._customOption;n||(n=t("<option/>"),this._customOption=n,i.append(n)),n.text(e),n[0].selected=!0},_hideBusy:function(){clearTimeout(this._busy),this._arrowIcon.removeClass("k-i-loading"),this._focused.attr("aria-busy",!1),this._busy=null,this._showClear()},_showBusy:function(t){var e=this;t.isDefaultPrevented()||(e._request=!0,e._busy||(e._busy=setTimeout(function(){e._arrowIcon&&(e._focused.attr("aria-busy",!0),e._arrowIcon.addClass("k-i-loading"),e._hideClear())},100)))},_requestEnd:function(){this._request=!1,this._hideBusy()},_dataSource:function(){var e,n=this.element,s=this.options,r=s.dataSource||{};r=t.isArray(r)?{data:r}:r,this._isSelect&&((e=n[0].selectedIndex)>-1&&(s.index=e),r.select=n,r.fields=[{field:s.dataTextField},{field:s.dataValueField}]),this.dataSource?this._unbindDataSource():(this._requestStartHandler=m(this._showBusy,this),this._requestEndHandler=m(this._requestEnd,this),this._errorHandler=m(this._hideBusy,this)),this.dataSource=i.data.DataSource.create(r).bind("requestStart",this._requestStartHandler).bind("requestEnd",this._requestEndHandler).bind("error",this._errorHandler)},_firstItem:function(){this.listView.focusFirst()},_lastItem:function(){this.listView.focusLast()},_nextItem:function(){this.listView.focusNext()},_prevItem:function(){this.listView.focusPrev()},_move:function(t){var e,i,n,s=this,r=s.listView,o=t.keyCode,l=o===a.DOWN;if(o===a.UP||l){if(t.altKey)s.toggle(l);else{if(!r.bound()&&!s.ul[0].firstChild)return s._fetch||(s.dataSource.one(d,function(){s._fetch=!1,s._move(t)}),s._fetch=!0,s._filterSource()),t.preventDefault(),!0;if(n=s._focus(),s._fetch||n&&!n.hasClass("k-state-selected")||(l?(s._nextItem(),s._focus()||s._lastItem()):(s._prevItem(),s._focus()||s._firstItem())),e=r.dataItemByIndex(r.getElementIndex(s._focus())),s.trigger("select",{dataItem:e,item:s._focus()}))return void s._focus(n);s._select(s._focus(),!0).done(function(){s.popup.visible()||s._blur(),null===s._cascadedValue?s._cascadedValue=s.value():s._cascadedValue=s.dataItem()?s.dataItem()[s.options.dataValueField]||s.dataItem():null})}t.preventDefault(),i=!0}else if(o===a.ENTER||o===a.TAB){s.popup.visible()&&t.preventDefault(),n=s._focus(),e=s.dataItem(),s.popup.visible()||e&&s.text()===s._text(e)||(n=null);var u=s.filterInput&&s.filterInput[0]===h();if(n){var c=!0;if((e=r.dataItemByIndex(r.getElementIndex(n)))&&(c=s._value(e)!==k.unifyType(s.value(),typeof s._value(e))),c&&s.trigger("select",{dataItem:e,item:n}))return;s._select(n)}else s.input&&((s._syncValueAndText()||s._isSelect)&&s._accessor(s.input.val()),s.listView.value(s.input.val()));s._focusElement&&s._focusElement(s.wrapper),u&&o===a.TAB?s.wrapper.focusout():s._blur(),s.close(),i=!0}else if(o===a.ESC)s.popup.visible()&&t.preventDefault(),s.close(),i=!0;else if(s.popup.visible()&&(o===a.PAGEDOWN||o===a.PAGEUP)){t.preventDefault();var p=o===a.PAGEDOWN?1:-1;r.scrollWith(p*r.screenHeight()),i=!0}return i},_fetchData:function(){var t=this,e=!!t.dataSource.view().length;t._request||t.options.cascadeFrom||t.listView.bound()||t._fetch||e||(t._fetch=!0,t.dataSource.fetch().done(function(){t._fetch=!1}))},_options:function(t,i,n){var s,r,a,o,h=this.element,u=h[0],c=t.length,d="",p=0;for(i&&(d=i);p<c;p++)s="<option",r=t[p],a=this._text(r),(o=this._value(r))!==e&&(-1!==(o+="").indexOf('"')&&(o=o.replace(b,"&quot;")),s+=' value="'+o+'"'),s+=">",a!==e&&(s+=l(a)),d+=s+="</option>";h.html(d),n!==e&&(u.value=n,u.value&&!n&&(u.selectedIndex=-1)),-1!==u.selectedIndex&&(s=I(u))&&s.setAttribute("selected","selected")},_reset:function(){var e=this,i=e.element,n=i.attr("form"),s=n?t("#"+n):i.closest("form");s[0]&&(e._resetHandler=function(){setTimeout(function(){e.value(e._initial)})},e._form=s.on("reset",e._resetHandler))},_parentWidget:function(){var e=this.options.name;if(this.options.cascadeFrom){var i=t("#"+this.options.cascadeFrom),n=i.data("kendo"+e);return n||(n=i.data("kendo"+y[e])),n}},_cascade:function(){var t,e=this,i=e.options;if(i.cascadeFrom){if(!(t=e._parentWidget()))return;e._cascadeHandlerProxy=m(e._cascadeHandler,e),e._cascadeFilterRequests=[],i.autoBind=!1,t.bind("set",function(){e.one("set",function(t){e._selectedValue=t.value||e._accessor()})}),t.first("cascade",e._cascadeHandlerProxy),t.listView.bound()?(e._toggleCascadeOnFocus(),e._cascadeSelect(t)):(t.one("dataBound",function(){e._toggleCascadeOnFocus(),t.popup.visible()&&t._focused.focus()}),t.value()||e.enable(!1))}},_toggleCascadeOnFocus:function(){var t=this,e=t._parentWidget(),i=v?"blur":"focusout";e._focused.add(e.filterInput).bind("focus",function(){e.unbind("cascade",t._cascadeHandlerProxy),e.first(d,t._cascadeHandlerProxy)}),e._focused.add(e.filterInput).bind(i,function(){e.unbind(d,t._cascadeHandlerProxy),e.first("cascade",t._cascadeHandlerProxy)})},_cascadeHandler:function(t){var e=this._parentWidget(),i=this.value();this._userTriggered=t.userTriggered,this.listView.bound()&&this._clearSelection(e,!0),this._cascadeSelect(e,i)},_cascadeChange:function(t){var e=this._accessor()||this._selectedValue;this._cascadeFilterRequests.length||(this._selectedValue=null),this._userTriggered?this._clearSelection(t,!0):e?(e!==this.listView.value()[0]&&this.value(e),this.dataSource.view()[0]&&-1!==this.selectedIndex||this._clearSelection(t,!0)):this.dataSource.flatView().length&&this.select(this.options.index),this.enable(),this._triggerCascade(),this._triggerChange(),this._userTriggered=!1},_cascadeSelect:function(t,i){var n=this,s=t.dataItem(),r=s?t._value(s):null,a=n.options.cascadeFromField||t.options.dataValueField;if(n._valueBeforeCascade=i!==e?i:n.value(),r||0===r){F(n.dataSource.filter()||{},a);var o=function(){var e=n._cascadeFilterRequests.shift();e&&n.unbind("dataBound",e),(e=n._cascadeFilterRequests[0])&&n.first("dataBound",e),n._cascadeChange(t)};n._cascadeFilterRequests.push(o),1===n._cascadeFilterRequests.length&&n.first("dataBound",o),n._cascading=!0,n._filterSource({field:a,operator:"eq",value:r}),n._cascading=!1}else n.enable(!1),n._clearSelection(t),n._triggerCascade(),n._triggerChange(),n._userTriggered=!1}});var C=".StaticList",T=i.ui.DataBoundWidget.extend({init:function(e,n){r.fn.init.call(this,e,n),this.element.attr("role","listbox").on("click"+C,"li",m(this._click,this)).on("mouseenter"+C,"li",function(){t(this).addClass(p)}).on("mouseleave"+C,"li",function(){t(this).removeClass(p)}),"multiple"===this.options.selectable&&this.element.attr("aria-multiselectable",!0),this.content=this.element.wrap("<div class='k-list-scroller' unselectable='on'></div>").parent(),this.header=this.content.before('<div class="k-group-header" style="display:none"></div>').prev(),this.bound(!1),this._optionID=i.guid(),this._selectedIndices=[],this._view=[],this._dataItems=[],this._values=[];var s=this.options.value;s&&(this._values=t.isArray(s)?s.slice(0):[s]),this._getter(),this._templates(),this.setDataSource(this.options.dataSource),this._onScroll=m(function(){var t=this;clearTimeout(t._scrollId),t._scrollId=setTimeout(function(){t._renderHeader()},50)},this)},options:{name:"StaticList",dataValueField:null,valuePrimitive:!1,selectable:!0,template:null,groupTemplate:null,fixedGroupTemplate:null},events:["click",d,"activate","deactivate","dataBinding","dataBound","selectedItemChange"],setDataSource:function(e){var n,s=e||{};s=t.isArray(s)?{data:s}:s,s=i.data.DataSource.create(s),this.dataSource?(this.dataSource.unbind(d,this._refreshHandler),n=this.value(),this.value([]),this.bound(!1),this.value(n)):this._refreshHandler=m(this.refresh,this),this.setDSFilter(s.filter()),this.dataSource=s.bind(d,this._refreshHandler),this._fixedHeader()},skip:function(){return this.dataSource.skip()},setOptions:function(t){r.fn.setOptions.call(this,t),this._getter(),this._templates(),this._render()},destroy:function(){this.element.off(C),this._refreshHandler&&this.dataSource.unbind(d,this._refreshHandler),clearTimeout(this._scrollId),r.fn.destroy.call(this)},dataItemByIndex:function(t){return this.dataSource.flatView()[t]},screenHeight:function(){return this.content[0].clientHeight},scrollToIndex:function(t){var e=this.element[0].children[t];e&&this.scroll(e)},scrollWith:function(t){this.content.scrollTop(this.content.scrollTop()+t)},scroll:function(t){if(t){t[0]&&(t=t[0]);var e=this.content[0],i=t.offsetTop,n=t.offsetHeight,s=e.scrollTop,r=e.clientHeight,a=i+n;s>i?s=i:a>s+r&&(s=a-r),e.scrollTop=s}},selectedDataItems:function(t){if(t===e)return this._dataItems.slice();this._dataItems=t,this._values=this._getValues(t)},_getValues:function(e){var i=this._valueGetter;return t.map(e,function(t){return i(t)})},focusNext:function(){var t=this.focus();t=t?t.next():0,this.focus(t)},focusPrev:function(){var t=this.focus();t=t?t.prev():this.element[0].children.length-1,this.focus(t)},focusFirst:function(){this.focus(this.element[0].children[0])},focusLast:function(){this.focus(S(this.element[0].children))},focus:function(i){var n,s=this._optionID;if(i===e)return this._current;i=S(this._get(i)),i=t(this.element[0].children[i]),this._current&&(this._current.removeClass("k-state-focused").removeAttr("id"),this.trigger("deactivate")),(n=!!i[0])&&(i.addClass("k-state-focused"),this.scroll(i),i.attr("id",s)),this._current=n?i:null,this.trigger("activate")},focusIndex:function(){return this.focus()?this.focus().index():e},skipUpdate:function(t){this._skipUpdate=t},select:function(i){var n,s,r=this.options.selectable,a="multiple"!==r&&!1!==r,o=this._selectedIndices,l=[];if(i===e)return o.slice();1===(i=this._get(i)).length&&-1===i[0]&&(i=[]);var h=t.Deferred().resolve(),u=this.isFiltered();return u&&!a&&this._deselectFiltered(i)?h:a&&!u&&-1!==t.inArray(S(i),o)?(this._dataItems.length&&this._view.length&&(this._dataItems=[this._view[o[0]].item]),h):(n=(s=this._deselect(i)).removed,(i=s.indices).length&&(a&&(i=[S(i)]),l=this._select(i)),(l.length||n.length)&&(this._valueComparer=null,this.trigger(d,{added:l,removed:n})),h)},removeAt:function(t){return this._selectedIndices.splice(t,1),this._values.splice(t,1),this._valueComparer=null,{position:t,dataItem:this._dataItems.splice(t,1)[0]}},setValue:function(e){e=t.isArray(e)||e instanceof c?e.slice(0):[e],this._values=e,this._valueComparer=null},value:function(i){var n,s=this._valueDeferred;return i===e?this._values.slice():(this.setValue(i),s&&"resolved"!==s.state()||(this._valueDeferred=s=t.Deferred()),this.bound()&&(n=this._valueIndices(this._values),"multiple"===this.options.selectable&&this.select(-1),this.select(n),s.resolve()),this._skipUpdate=!1,s)},items:function(){return this.element.children(".k-item")},_click:function(e){e.isDefaultPrevented()||this.trigger("click",{item:t(e.currentTarget)})||this.select(e.currentTarget)},_valueExpr:function(t,e){var i,n,s=0,r=[];if(!this._valueComparer||this._valueType!==t){for(this._valueType=t;s<e.length;s++)r.push(x(e[s],t));i="for (var idx = 0; idx < "+r.length+"; idx++) { if (current === values[idx]) {   return idx; }} return -1;",n=new Function("current","values",i),this._valueComparer=function(t){return n(t,r)}}return this._valueComparer},_dataItemPosition:function(t,e){var i=this._valueGetter(t);return this._valueExpr(typeof i,e)(i)},_getter:function(){this._valueGetter=i.getter(this.options.dataValueField)},_deselect:function(e){var i,n,s,r=this.element[0].children,a=this.options.selectable,o=this._selectedIndices,l=this._dataItems,h=this._values,u=[],c=0,d=0;if(e=e.slice(),!0!==a&&e.length){if("multiple"===a)for(;c<e.length;c++)if(n=e[c],t(r[n]).hasClass("k-state-selected"))for(i=0;i<o.length;i++)if((s=o[i])===n){t(r[s]).removeClass("k-state-selected").attr("aria-selected",!1),u.push({position:i+d,dataItem:l.splice(i,1)[0]}),o.splice(i,1),e.splice(c,1),h.splice(i,1),d+=1,c-=1,i-=1;break}}else{for(;c<o.length;c++)t(r[o[c]]).removeClass("k-state-selected").attr("aria-selected",!1),u.push({position:c,dataItem:l[c]});this._values=[],this._dataItems=[],this._selectedIndices=[]}return{indices:e,removed:u}},_deselectFiltered:function(e){for(var i,n,s,r=this.element[0].children,a=[],o=0;o<e.length;o++)n=e[o],i=this._view[n].item,(s=this._dataItemPosition(i,this._values))>-1&&(a.push(this.removeAt(s)),t(r[n]).removeClass("k-state-selected"));return!!a.length&&(this.trigger(d,{added:[],removed:a}),!0)},_select:function(e){var i,n,s=this.element[0].children,r=this._view,a=[],o=0;for(-1!==S(e)&&this.focus(e);o<e.length;o++)i=r[n=e[o]],-1!==n&&i&&(i=i.item,this._selectedIndices.push(n),this._dataItems.push(i),this._values.push(this._valueGetter(i)),t(s[n]).addClass("k-state-selected").attr("aria-selected",!0),a.push({dataItem:i}));return a},getElementIndex:function(e){return t(e).data("offset-index")},_get:function(t){return"number"==typeof t?t=[t]:g(t)||(t=[(t=this.getElementIndex(t))!==e?t:-1]),t},_template:function(){var t=this.options,e=t.template;return e?(e=i.template(e),e=function(t){return'<li tabindex="-1" role="option" unselectable="on" class="k-item">'+e(t)+"</li>"}):e=i.template('<li tabindex="-1" role="option" unselectable="on" class="k-item">${'+i.expr(t.dataTextField,"data")+"}</li>",{useWithBlock:!1}),e},_templates:function(){var t,e=this.options,n={template:e.template,groupTemplate:e.groupTemplate,fixedGroupTemplate:e.fixedGroupTemplate};for(var s in n)(t=n[s])&&"function"!=typeof t&&(n[s]=i.template(t));this.templates=n},_normalizeIndices:function(t){for(var i=[],n=0;n<t.length;n++)t[n]!==e&&i.push(t[n]);return i},_valueIndices:function(t,e){var i,n=this._view,s=0;if(e=e?e.slice():[],!t.length)return[];for(;s<n.length;s++)-1!==(i=this._dataItemPosition(n[s].item,t))&&(e[i]=s);return this._normalizeIndices(e)},_firstVisibleItem:function(){for(var e=this.element[0],i=this.content[0].scrollTop,n=t(e.children[0]).height(),s=Math.floor(i/n)||0,r=e.children[s]||e.lastChild,a=r.offsetTop<i;r;)if(a){if(r.offsetTop+n>i||!r.nextSibling)break;r=r.nextSibling}else{if(r.offsetTop<=i||!r.previousSibling)break;r=r.previousSibling}return this._view[t(r).data("offset-index")]},_fixedHeader:function(){this.isGrouped()&&this.templates.fixedGroupTemplate?(this.header.show(),this.content.scroll(this._onScroll)):(this.header.hide(),this.content.off("scroll",this._onScroll))},_renderHeader:function(){var t=this.templates.fixedGroupTemplate;if(t){var e=this._firstVisibleItem();e&&e.group&&this.header.html(t(e.group))}},_renderItem:function(t){var e='<li tabindex="-1" role="option" unselectable="on" class="k-item',i=t.item,n=0!==t.index,s=t.selected;return n&&t.newGroup&&(e+=" k-first"),s&&(e+=" k-state-selected"),e+='" aria-selected="'+(s?"true":"false")+'" data-offset-index="'+t.index+'">',e+=this.templates.template(i),n&&t.newGroup&&(e+='<div class="k-group">'+this.templates.groupTemplate(t.group)+"</div>"),e+"</li>"},_render:function(){var t,e,i,n,s="",r=0,a=0,o=[],l=this.dataSource.view(),h=this.value(),u=this.isGrouped();if(u)for(r=0;r<l.length;r++)for(e=l[r],i=!0,n=0;n<e.items.length;n++)t={selected:this._selected(e.items[n],h),item:e.items[n],group:e.value,newGroup:i,index:a},o[a]=t,a+=1,s+=this._renderItem(t),i=!1;else for(r=0;r<l.length;r++)t={selected:this._selected(l[r],h),item:l[r],index:r},o[r]=t,s+=this._renderItem(t);this._view=o,this.element[0].innerHTML=s,u&&o.length&&this._renderHeader()},_selected:function(t,e){return(!this.isFiltered()||"multiple"===this.options.selectable)&&-1!==this._dataItemPosition(t,e)},setDSFilter:function(t){this._lastDSFilter=f({},t)},isFiltered:function(){return this._lastDSFilter||this.setDSFilter(this.dataSource.filter()),!i.data.Query.compareFilters(this.dataSource.filter(),this._lastDSFilter)},refresh:function(t){var e,i=t&&t.action,n=this.options.skipUpdateOnBind,s="itemchange"===i;this.trigger("dataBinding"),this._angularItems("cleanup"),this._fixedHeader(),this._render(),this.bound(!0),s||"remove"===i?(e=function(t,e){var i,n,s,r,a=e.length,o=t.length,l=[],h=[];if(o)for(s=0;s<o;s++){for(i=t[s],n=!1,r=0;r<a;r++)if(i===e[r]){n=!0,l.push({index:s,item:i});break}n||h.push(i)}return{changed:l,unchanged:h}}(this._dataItems,t.items)).changed.length&&(s?this.trigger("selectedItemChange",{items:e.changed}):this.value(this._getValues(e.unchanged))):this.isFiltered()||this._skipUpdate||this._emptySearch?(this.focus(0),this._skipUpdate&&(this._skipUpdate=!1,this._selectedIndices=this._valueIndices(this._values,this._selectedIndices))):n||i&&"add"!==i||this.value(this._values),this._valueDeferred&&this._valueDeferred.resolve(),this._angularItems("compile"),this.trigger("dataBound")},bound:function(t){if(t===e)return this._bound;this._bound=t},isGrouped:function(){return(this.dataSource.group()||[]).length}});function S(t){return t[t.length-1]}function I(t){var e=t.selectedIndex;return e>-1?t.options[e]:{}}function D(e){return!(!e||t.isEmptyObject(e)||e.filters&&!e.filters.length)}function F(e,i){var n,s=!1;return e.filters&&(n=t.grep(e.filters,function(t){return s=F(t,i),t.filters?t.filters.length:t.field!=i}),s||e.filters.length===n.length||(s=!0),e.filters=n),s}n.plugin(T)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.fx",["kendo.core"],function(){return function(t,e){var i=window.kendo,n=i.effects,s=t.each,r=t.extend,a=t.proxy,o=i.support,l=o.browser,h=o.transforms,u=o.transitions,c={scale:0,scalex:0,scaley:0,scale3d:0},d={translate:0,translatex:0,translatey:0,translate3d:0},p=void 0!==document.documentElement.style.zoom&&!h,f=/matrix3?d?\s*\(.*,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?,\s*([\d\.\-]+)\w*?/i,m=/^(-?[\d\.\-]+)?[\w\s]*,?\s*(-?[\d\.\-]+)?[\w\s]*/i,g=/translatex?$/i,_=/(zoom|fade|expand)(\w+)/,v=/(zoom|fade|expand)/,w=/[xy]$/i,b=["perspective","rotate","rotatex","rotatey","rotatez","rotate3d","scale","scalex","scaley","scalez","scale3d","skew","skewx","skewy","translate","translatex","translatey","translatez","translate3d","matrix","matrix3d"],y=["rotate","scale","scalex","scaley","skew","skewx","skewy","translate","translatex","translatey","matrix"],k={rotate:"deg",scale:"",skew:"px",translate:"px"},x=h.css,C=Math.round,T="none",S="width",I="height",D="translate",F=x+"transition",E=x+"transform",O=x+"backface-visibility",A=x+"perspective",M="perspective(1500px)",H={left:{reverse:"right",property:"left",transition:"translatex",vertical:!1,modifier:-1},right:{reverse:"left",property:"left",transition:"translatex",vertical:!1,modifier:1},down:{reverse:"up",property:"top",transition:"translatey",vertical:!0,modifier:1},up:{reverse:"down",property:"top",transition:"translatey",vertical:!0,modifier:-1},top:{reverse:"bottom"},bottom:{reverse:"top"},in:{reverse:"out",modifier:-1},out:{reverse:"in",modifier:1},vertical:{reverse:"vertical"},horizontal:{reverse:"horizontal"}};if(i.directions=H,r(t.fn,{kendoStop:function(t,e){return u?n.stopQueue(this,t||!1,e||!1):this.stop(t,e)}}),h&&!u){s(y,function(e,i){t.fn[i]=function(e){if(void 0===e)return L(this,i);var n=t(this)[0],s=i+"("+e+k[i.replace(w,"")]+")";return-1==n.style.cssText.indexOf(E)?t(this).css(E,s):n.style.cssText=n.style.cssText.replace(new RegExp(i+"\\(.*?\\)","i"),s),this},t.fx.step[i]=function(e){t(e.elem)[i](e.now)}});var P=t.fx.prototype.cur;t.fx.prototype.cur=function(){return-1!=y.indexOf(this.prop)?parseFloat(t(this.elem)[this.prop]()):P.apply(this,arguments)}}function z(t){return parseInt(t,10)}function V(t,e){return z(t.css(e))}function R(t){for(var e in t)-1!=b.indexOf(e)&&-1==y.indexOf(e)&&delete t[e];return t}function B(t,e){var i,n,s,r,a=[],l={};for(n in e)i=n.toLowerCase(),r=h&&-1!=b.indexOf(i),!o.hasHW3D&&r&&-1==y.indexOf(i)?delete e[n]:(s=e[n],r?a.push(n+"("+s+")"):l[n]=s);return a.length&&(l[E]=a.join(" ")),l}function L(t,e){if(h){var i=t.css(E);if(i==T)return"scale"==e?1:0;var n=i.match(new RegExp(e+"\\s*\\(([\\d\\w\\.]+)")),s=0;return n?s=z(n[1]):(n=i.match(f)||[0,0,0,0,0],e=e.toLowerCase(),g.test(e)?s=parseFloat(n[3]/n[2]):"translatey"==e?s=parseFloat(n[4]/n[2]):"scale"==e?s=parseFloat(n[2]):"rotate"==e&&(s=parseFloat(Math.atan2(n[2],n[1])))),s}return parseFloat(t.css(e))}i.toggleClass=function(t,e,i,n){return e&&(e=e.split(" "),u&&(i=r({exclusive:"all",duration:400,ease:"ease-out"},i),t.css(F,i.exclusive+" "+i.duration+"ms "+i.ease),setTimeout(function(){t.css(F,"").css(I)},i.duration)),s(e,function(e,i){t.toggleClass(i,n)})),t},i.parseEffects=function(t,e){var i={};return"string"==typeof t?s(t.split(" "),function(t,n){var s=!v.test(n),r=n.replace(_,function(t,e,i){return e+":"+i.toLowerCase()}).split(":"),a=r[1],o={};r.length>1&&(o.direction=e&&s?H[a].reverse:a),i[r[0]]=o}):s(t,function(t){var n=this.direction;n&&e&&!v.test(t)&&(this.direction=H[n].reverse),i[t]=this}),i},u&&r(n,{transition:function(e,i,n){var s,a,o=0,l=e.data("keys")||[],h=!1,c=function(){h||(h=!0,a&&(clearTimeout(a),a=null),e.removeData("abortId").dequeue().css(F,"").css(F),n.complete.call(e))};(n=r({duration:200,ease:"ease-out",complete:null,exclusive:"all"},n)).duration=t.fx?t.fx.speeds[n.duration]||n.duration:n.duration,s=B(0,i),t.merge(l,function(t){var e=[];for(var i in t)e.push(i);return e}(s)),e.data("keys",t.unique(l)).height(),e.css(F,n.exclusive+" "+n.duration+"ms "+n.ease).css(F),e.css(s).css(E),u.event&&(e.one(u.event,c),0!==n.duration&&(o=500)),a=setTimeout(c,n.duration+o),e.data("abortId",a),e.data("completeCallback",c)},stopQueue:function(t,e,n){var s,r=t.data("keys"),a=!n&&r,o=t.data("completeCallback");return a&&(s=i.getComputedStyles(t[0],r)),o&&o(),a&&t.css(s),t.removeData("keys").stop(e)}});var N=i.Class.extend({init:function(t,e){this.element=t,this.effects=[],this.options=e,this.restore=[]},run:function(e){var i,s,a,o,l,c,d=e.length,p=this.element,f=this.options,m=t.Deferred(),g={},_={};for(this.effects=e,m.done(t.proxy(this,"complete")),p.data("animating",!0),s=0;s<d;s++)for((i=e[s]).setReverse(f.reverse),i.setOptions(f),this.addRestoreProperties(i.restore),i.prepare(g,_),a=0,c=(l=i.children()).length;a<c;a++)l[a].duration(f.duration).run();for(var v in f.effects)r(_,f.effects[v].properties);for(p.is(":visible")||r(g,{display:p.data("olddisplay")||"block"}),h&&!f.reset&&(o=p.data("targetTransform"))&&(g=r(o,g)),g=B(0,g),h&&!u&&(g=R(g)),p.css(g).css(E),s=0;s<d;s++)e[s].setup();return f.init&&f.init(),p.data("targetTransform",_),n.animate(p,_,r({},f,{complete:m.resolve})),m.promise()},stop:function(){t(this.element).kendoStop(!0,!0)},addRestoreProperties:function(t){for(var e,i=this.element,n=0,s=t.length;n<s;n++)e=t[n],this.restore.push(e),i.data(e)||i.data(e,i.css(e))},restoreCallback:function(){for(var t=this.element,e=0,i=this.restore.length;e<i;e++){var n=this.restore[e];t.css(n,t.data(n))}},complete:function(){var e=0,i=this.element,n=this.options,s=this.effects,r=s.length;for(i.removeData("animating").dequeue(),n.hide&&i.data("olddisplay",i.css("display")).hide(),this.restoreCallback(),p&&!h&&setTimeout(t.proxy(this,"restoreCallback"),0);e<r;e++)s[e].teardown();n.completeCallback&&n.completeCallback(i)}});n.promise=function(t,e){var s,r,a=[],o=new N(t,e),l=i.parseEffects(e.effects);e.effects=l;for(var h in l)(s=n[U(h)])&&(r=new s(t,l[h].direction),a.push(r));a[0]?o.run(a):(t.is(":visible")||t.css({display:t.data("olddisplay")||"block"}).css("display"),e.init&&e.init(),t.dequeue(),o.complete())},r(n,{animate:function(e,i,a){var o=!1!==a.transition;delete a.transition,u&&"transition"in n&&o?n.transition(e,i,a):h?e.animate(R(i),{queue:!1,show:!1,hide:!1,duration:a.duration,complete:a.complete}):e.each(function(){var e=t(this),n={};s(b,function(t,s){var a,o=i?i[s]+" ":null;if(o){var l=i;if(s in c&&void 0!==i[s])a=o.match(m),h&&r(l,{scale:+a[0]});else if(s in d&&void 0!==i[s]){var u=e.css("position"),p="absolute"==u||"fixed"==u;e.data(D)||(p?e.data(D,{top:V(e,"top")||0,left:V(e,"left")||0,bottom:V(e,"bottom"),right:V(e,"right")}):e.data(D,{top:V(e,"marginTop")||0,left:V(e,"marginLeft")||0}));var f=e.data(D);if(a=o.match(m)){var g=s==D+"y"?0:+a[1],_=s==D+"y"?+a[1]:+a[2];p?(isNaN(f.right)?isNaN(g)||r(l,{left:f.left+g}):isNaN(g)||r(l,{right:f.right-g}),isNaN(f.bottom)?isNaN(_)||r(l,{top:f.top+_}):isNaN(_)||r(l,{bottom:f.bottom-_})):(isNaN(g)||r(l,{marginLeft:f.left+g}),isNaN(_)||r(l,{marginTop:f.top+_}))}}!h&&"scale"!=s&&s in l&&delete l[s],l&&r(n,l)}}),l.msie&&delete n.scale,e.animate(n,{queue:!1,show:!1,hide:!1,duration:a.duration,complete:a.complete})})}}),n.animatedPromise=n.promise;var W=i.Class.extend({init:function(t,e){this.element=t,this._direction=e,this.options={},this._additionalEffects=[],this.restore||(this.restore=[])},reverse:function(){return this._reverse=!0,this.run()},play:function(){return this._reverse=!1,this.run()},add:function(t){return this._additionalEffects.push(t),this},direction:function(t){return this._direction=t,this},duration:function(t){return this._duration=t,this},compositeRun:function(){var t=new N(this.element,{reverse:this._reverse,duration:this._duration}),e=this._additionalEffects.concat([this]);return t.run(e)},run:function(){if(this._additionalEffects&&this._additionalEffects[0])return this.compositeRun();var e,i,s=this.element,a=0,o=this.restore,l=o.length,c=t.Deferred(),d={},p={},f=this.children(),m=f.length;for(c.done(t.proxy(this,"_complete")),s.data("animating",!0),a=0;a<l;a++)e=o[a],s.data(e)||s.data(e,s.css(e));for(a=0;a<m;a++)f[a].duration(this._duration).run();return this.prepare(d,p),s.is(":visible")||r(d,{display:s.data("olddisplay")||"block"}),h&&(i=s.data("targetTransform"))&&(d=r(i,d)),d=B(0,d),h&&!u&&(d=R(d)),s.css(d).css(E),this.setup(),s.data("targetTransform",p),n.animate(s,p,{duration:this._duration,complete:c.resolve}),c.promise()},stop:function(){var e=0,i=this.children(),n=i.length;for(e=0;e<n;e++)i[e].stop();return t(this.element).kendoStop(!0,!0),this},restoreCallback:function(){for(var t=this.element,e=0,i=this.restore.length;e<i;e++){var n=this.restore[e];t.css(n,t.data(n))}},_complete:function(){var e=this.element;e.removeData("animating").dequeue(),this.restoreCallback(),this.shouldHide()&&e.data("olddisplay",e.css("display")).hide(),p&&!h&&setTimeout(t.proxy(this,"restoreCallback"),0),this.teardown()},setOptions:function(t){r(!0,this.options,t)},children:function(){return[]},shouldHide:t.noop,setup:t.noop,prepare:t.noop,teardown:t.noop,directions:[],setReverse:function(t){return this._reverse=t,this}});function U(t){return t.charAt(0).toUpperCase()+t.substring(1)}function q(t,e){var i=W.extend(e),r=i.prototype.directions;n[U(t)]=i,n.Element.prototype[t]=function(t,e,n,s){return new i(this.element,t,e,n,s)},s(r,function(e,s){n.Element.prototype[t+U(s)]=function(t,e,n){return new i(this.element,s,t,e,n)}})}var j=["left","right","up","down"],G=["in","out"];function Y(t,e,i,n){q(t,{directions:G,startValue:function(t){return this._startValue=t,this},endValue:function(t){return this._endValue=t,this},shouldHide:function(){return this._shouldHide},prepare:function(t,s){var r,a,o="out"===this._direction,l=this.element.data(e);r=isNaN(l)||l==i?void 0!==this._startValue?this._startValue:o?i:n:l,a=void 0!==this._endValue?this._endValue:o?n:i,this._reverse?(t[e]=a,s[e]=r):(t[e]=r,s[e]=a),this._shouldHide=s[e]===n}})}q("slideIn",{directions:j,divisor:function(t){return this.options.divisor=t,this},prepare:function(t,e){var n,s=this.element,r=i._outerWidth,a=i._outerHeight,o=H[this._direction],l=-o.modifier*(o.vertical?a(s):r(s))/(this.options&&this.options.divisor||1)+"px";this._reverse&&(n=t,t=e,e=n),h?(t[o.transition]=l,e[o.transition]="0px"):(t[o.property]=l,e[o.property]="0px")}}),q("tile",{directions:j,init:function(t,e,i){W.prototype.init.call(this,t,e),this.options={previous:i}},previousDivisor:function(t){return this.options.previousDivisor=t,this},children:function(){var t=this._reverse,e=this.options.previous,n=this.options.previousDivisor||1,s=this._direction,r=[i.fx(this.element).slideIn(s).setReverse(t)];return e&&r.push(i.fx(e).slideIn(H[s].reverse).divisor(n).setReverse(!t)),r}}),Y("fade","opacity",1,0),Y("zoom","scale",1,.01),q("slideMargin",{prepare:function(t,e){var i,n=this.element,s=this.options,r=n.data("origin"),a=s.offset,o=this._reverse;o||null!==r||n.data("origin",parseFloat(n.css("margin-"+s.axis))),i=n.data("origin")||0,e["margin-"+s.axis]=o?i:i+a}}),q("slideTo",{prepare:function(t,e){var i=this.element,n=this.options.offset.split(","),s=this._reverse;h?(e.translatex=s?0:n[0],e.translatey=s?0:n[1]):(e.left=s?0:n[0],e.top=s?0:n[1]),i.css("left")}}),q("expand",{directions:["horizontal","vertical"],restore:["overflow"],prepare:function(t,e){var i=this.element,n=this.options,s=this._reverse,r="vertical"===this._direction?I:S,a=i[0].style[r],o=i.data(r),l=parseFloat(o||a),h=C(i.css(r,"auto")[r]());t.overflow="hidden",l=n&&n.reset?h||l:l||h,e[r]=(s?0:l)+"px",t[r]=(s?l:0)+"px",void 0===o&&i.data(r,a)},shouldHide:function(){return this._reverse},teardown:function(){var t=this.element,e="vertical"===this._direction?I:S,i=t.data(e);"auto"!=i&&""!==i||setTimeout(function(){t.css(e,"auto").css(e)},0)}});var $={position:"absolute",marginLeft:0,marginTop:0,scale:1};q("transfer",{init:function(t,e){this.element=t,this.options={target:e},this.restore=[]},setup:function(){this.element.appendTo(document.body)},prepare:function(t,e){var i=this.element,s=n.box(i),a=n.box(this.options.target),o=L(i,"scale"),l=n.fillScale(a,s),h=n.transformOrigin(a,s);r(t,$),e.scale=1,i.css(E,"scale(1)").css(E),i.css(E,"scale("+o+")"),t.top=s.top,t.left=s.left,t.transformOrigin=h.x+"px "+h.y+"px",this._reverse?t.scale=l:e.scale=l}});var K={top:"rect(auto auto $size auto)",bottom:"rect($size auto auto auto)",left:"rect(auto $size auto auto)",right:"rect(auto auto auto $size)"},J={top:{start:"rotatex(0deg)",end:"rotatex(180deg)"},bottom:{start:"rotatex(-180deg)",end:"rotatex(0deg)"},left:{start:"rotatey(0deg)",end:"rotatey(-180deg)"},right:{start:"rotatey(180deg)",end:"rotatey(0deg)"}};function Q(t,e){var n=t[i.directions[e].vertical?I:S]()/2+"px";return K[e].replace("$size",n)}q("turningPage",{directions:j,init:function(t,e,i){W.prototype.init.call(this,t,e),this._container=i},prepare:function(t,e){var n=this._reverse,s=n?H[this._direction].reverse:this._direction,r=J[s];t.zIndex=1,this._clipInHalf&&(t.clip=Q(this._container,i.directions[s].reverse)),t[O]="hidden",e[E]=M+(n?r.start:r.end),t[E]=M+(n?r.end:r.start)},setup:function(){this._container.append(this.element)},face:function(t){return this._face=t,this},shouldHide:function(){var t=this._reverse,e=this._face;return t&&!e||!t&&e},clipInHalf:function(t){return this._clipInHalf=t,this},temporary:function(){return this.element.addClass("temp-page"),this}}),q("staticPage",{directions:j,init:function(t,e,i){W.prototype.init.call(this,t,e),this._container=i},restore:["clip"],prepare:function(t,e){var i=this._reverse?H[this._direction].reverse:this._direction;t.clip=Q(this._container,i),t.opacity=.999,e.opacity=1},shouldHide:function(){var t=this._reverse,e=this._face;return t&&!e||!t&&e},face:function(t){return this._face=t,this}}),q("pageturn",{directions:["horizontal","vertical"],init:function(t,e,i,n){W.prototype.init.call(this,t,e),this.options={},this.options.face=i,this.options.back=n},children:function(){var t,e=this.options,n="horizontal"===this._direction?"left":"top",s=i.directions[n].reverse,r=this._reverse,a=e.face.clone(!0).removeAttr("id"),o=e.back.clone(!0).removeAttr("id"),l=this.element;return r&&(t=n,n=s,s=t),[i.fx(e.face).staticPage(n,l).face(!0).setReverse(r),i.fx(e.back).staticPage(s,l).setReverse(r),i.fx(a).turningPage(n,l).face(!0).clipInHalf(!0).temporary().setReverse(r),i.fx(o).turningPage(s,l).clipInHalf(!0).temporary().setReverse(r)]},prepare:function(t,e){t[A]="1500px",t.transformStyle="preserve-3d",t.opacity=.999,e.opacity=1},teardown:function(){this.element.find(".temp-page").remove()}}),q("flip",{directions:["horizontal","vertical"],init:function(t,e,i,n){W.prototype.init.call(this,t,e),this.options={},this.options.face=i,this.options.back=n},children:function(){var t,e=this.options,n="horizontal"===this._direction?"left":"top",s=i.directions[n].reverse,r=this._reverse,a=this.element;return r&&(t=n,n=s,s=t),[i.fx(e.face).turningPage(n,a).face(!0).setReverse(r),i.fx(e.back).turningPage(s,a).setReverse(r)]},prepare:function(t){t[A]="1500px",t.transformStyle="preserve-3d"}});var X=!o.mobileOS.android;q("replace",{_before:t.noop,_after:t.noop,init:function(e,i,n){W.prototype.init.call(this,e),this._previous=t(i),this._transitionClass=n},duration:function(){throw new Error("The replace effect does not support duration setting; the effect duration may be customized through the transition class rule")},beforeTransition:function(t){return this._before=t,this},afterTransition:function(t){return this._after=t,this},_both:function(){return t().add(this._element).add(this._previous)},_containerClass:function(){var t=this._direction,e="k-fx k-fx-start k-fx-"+this._transitionClass;return t&&(e+=" k-fx-"+t),this._reverse&&(e+=" k-fx-reverse"),e},complete:function(e){if(!(!this.deferred||e&&t(e.target).is(".km-touch-scrollbar, .km-actionsheet-wrapper"))){var i=this.container;i.removeClass("k-fx-end").removeClass(this._containerClass()).off(u.event,this.completeProxy),this._previous.hide().removeClass("k-fx-current"),this.element.removeClass("k-fx-next"),X&&i.css("overflow",""),this.isAbsolute||this._both().css("position",""),this.deferred.resolve(),delete this.deferred}},run:function(){if(this._additionalEffects&&this._additionalEffects[0])return this.compositeRun();var e=this,n=e.element,s=e._previous,r=n.parents().filter(s.parents()).first(),a=e._both(),o=t.Deferred(),l=n.css("position");return r.length||(r=n.parent()),this.container=r,this.deferred=o,this.isAbsolute="absolute"==l,this.isAbsolute||a.css("position","absolute"),X&&(r.css("overflow"),r.css("overflow","hidden")),u?(n.addClass("k-fx-hidden"),r.addClass(this._containerClass()),this.completeProxy=t.proxy(this,"complete"),r.on(u.event,this.completeProxy),i.animationFrame(function(){n.removeClass("k-fx-hidden").addClass("k-fx-next"),s.css("display","").addClass("k-fx-current"),e._before(s,n),i.animationFrame(function(){r.removeClass("k-fx-start").addClass("k-fx-end"),e._after(s,n)})})):this.complete(),o.promise()},stop:function(){this.complete()}});var Z=i.Class.extend({init:function(){this._tickProxy=a(this._tick,this),this._started=!1},tick:t.noop,done:t.noop,onEnd:t.noop,onCancel:t.noop,start:function(){this.enabled()&&(this.done()?this.onEnd():(this._started=!0,i.animationFrame(this._tickProxy)))},enabled:function(){return!0},cancel:function(){this._started=!1,this.onCancel()},_tick:function(){this._started&&(this.tick(),this.done()?(this._started=!1,this.onEnd()):i.animationFrame(this._tickProxy))}}),tt=Z.extend({init:function(t){r(this,t),Z.fn.init.call(this)},done:function(){return this.timePassed()>=this.duration},timePassed:function(){return Math.min(this.duration,new Date-this.startDate)},moveTo:function(t){var e=this.movable;this.initial=e[this.axis],this.delta=t.location-this.initial,this.duration="number"==typeof t.duration?t.duration:300,this.tick=this._easeProxy(t.ease),this.startDate=new Date,this.start()},_easeProxy:function(t){var e=this;return function(){e.movable.moveAxis(e.axis,t(e.timePassed(),e.initial,e.delta,e.duration))}}});r(tt,{easeOutExpo:function(t,e,i,n){return t==n?e+i:i*(1-Math.pow(2,-10*t/n))+e},easeOutBack:function(t,e,i,n,s){return i*((t=t/n-1)*t*(2.70158*t+1.70158)+1)+e}}),n.Animation=Z,n.Transition=tt,n.createEffect=q,n.box=function(e){var n=(e=t(e)).offset();return n.width=i._outerWidth(e),n.height=i._outerHeight(e),n},n.transformOrigin=function(t,e){var i=(t.left-e.left)*e.width/(e.width-t.width),n=(t.top-e.top)*e.height/(e.height-t.height);return{x:isNaN(i)?0:i,y:isNaN(n)?0:n}},n.fillScale=function(t,e){return Math.min(t.width/e.width,t.height/e.height)},n.fitScale=function(t,e){return Math.max(t.width/e.width,t.height/e.height)}}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.userevents",["kendo.core"],function(){return function(t,e){var i=window.kendo,n=i.support,s=i.Class,r=i.Observable,a=t.now,o=t.extend,l=n.mobileOS,h=l&&l.android,u=800,c=n.browser.msie?5:0,d={api:0,touch:0,mouse:9,pointer:9},p=!n.touch||n.mouseAndTouchPresent;function f(t,e){var i=t.x.location,n=t.y.location,s=e.x.location,r=e.y.location,a=i-s,o=n-r;return{center:{x:(i+s)/2,y:(n+r)/2},distance:Math.sqrt(a*a+o*o)}}function m(t){var e,i,s,r=[],a=t.originalEvent,o=t.currentTarget,l=0;if(t.api)r.push({id:2,event:t,target:t.target,currentTarget:t.target,location:t,type:"api"});else if(t.type.match(/touch/))for(e=(i=a?a.changedTouches:[]).length;l<e;l++)s=i[l],r.push({location:s,event:t,target:s.target,currentTarget:o,id:s.identifier,type:"touch"});else n.pointers||n.msPointers?r.push({location:a,event:t,target:t.target,currentTarget:o,id:a.pointerId,type:"pointer"}):r.push({id:1,event:t,target:t.target,currentTarget:o,location:t,type:"mouse"});return r}var g=s.extend({init:function(t,e){this.axis=t,this._updateLocationData(e),this.startLocation=this.location,this.velocity=this.delta=0,this.timeStamp=a()},move:function(t){var e=t["page"+this.axis],i=a(),n=i-this.timeStamp||1;!e&&h||(this.delta=e-this.location,this._updateLocationData(t),this.initialDelta=e-this.startLocation,this.velocity=this.delta/n,this.timeStamp=i)},_updateLocationData:function(t){var e=this.axis;this.location=t["page"+e],this.client=t["client"+e],this.screen=t["screen"+e]}}),_=s.extend({init:function(t,e,i){o(this,{x:new g("X",i.location),y:new g("Y",i.location),type:i.type,useClickAsTap:t.useClickAsTap,threshold:t.threshold||d[i.type],userEvents:t,target:e,currentTarget:i.currentTarget,initialTouch:i.target,id:i.id,pressEvent:i,_moved:!1,_finished:!1})},press:function(){this._holdTimeout=setTimeout(t.proxy(this,"_hold"),this.userEvents.minHold),this._trigger("press",this.pressEvent)},_hold:function(){this._trigger("hold",this.pressEvent)},move:function(t){if(!this._finished){if(this.x.move(t.location),this.y.move(t.location),!this._moved){if(this._withinIgnoreThreshold())return;if(w.current&&w.current!==this.userEvents)return this.dispose();this._start(t)}this._finished||this._trigger("move",t)}},end:function(t){this.endTime=a(),this._finished||(this._finished=!0,this._trigger("release",t),this._moved?this._trigger("end",t):this.useClickAsTap||this._trigger("tap",t),clearTimeout(this._holdTimeout),this.dispose())},dispose:function(){var e=this.userEvents.touches;this._finished=!0,this.pressEvent=null,clearTimeout(this._holdTimeout),e.splice(t.inArray(this,e),1)},skip:function(){this.dispose()},cancel:function(){this.dispose()},isMoved:function(){return this._moved},_start:function(t){clearTimeout(this._holdTimeout),this.startTime=a(),this._moved=!0,this._trigger("start",t)},_trigger:function(t,e){var i=e.event,n={touch:this,x:this.x,y:this.y,target:this.target,event:i};this.userEvents.notify(t,n)&&i.preventDefault()},_withinIgnoreThreshold:function(){var t=this.x.initialDelta,e=this.y.initialDelta;return Math.sqrt(t*t+e*e)<=this.threshold}});function v(t){for(var e=i.eventMap.up.split(" "),n=0,s=e.length;n<s;n++)t(e[n])}var w=r.extend({init:function(e,s){var a,l=i.guid();if(s=s||{},a=this.filter=s.filter,this.threshold=s.threshold||c,this.minHold=s.minHold||u,this.touches=[],this._maxTouches=s.multiTouch?2:1,this.allowSelection=s.allowSelection,this.captureUpIfMoved=s.captureUpIfMoved,this.useClickAsTap=!s.fastTap&&!n.delayedClick(),this.eventNS=l,e=t(e).handler(this),r.fn.init.call(this),o(this,{element:e,surface:s.global&&p?t(e[0].ownerDocument.documentElement):t(s.surface||e),stopPropagation:s.stopPropagation,pressed:!1}),this.surface.handler(this).on(i.applyEventMap("move",l),"_move").on(i.applyEventMap("up cancel",l),"_end"),e.on(i.applyEventMap("down",l),a,"_start"),this.useClickAsTap&&e.on(i.applyEventMap("click",l),a,"_click"),n.pointers||n.msPointers)if(n.browser.version<11){var h="pinch-zoom double-tap-zoom";e.css("-ms-touch-action",s.touchAction&&"none"!=s.touchAction?h+" "+s.touchAction:h)}else e.css("touch-action",s.touchAction||"none");if(s.preventDragEvent&&e.on(i.applyEventMap("dragstart",l),i.preventDefault),e.on(i.applyEventMap("mousedown",l),a,{root:e},"_select"),this.captureUpIfMoved&&n.eventCapture){var d=this.surface[0],f=t.proxy(this.preventIfMoving,this);v(function(t){d.addEventListener(t,f,!0)})}this.bind(["press","hold","tap","start","move","end","release","cancel","gesturestart","gesturechange","gestureend","gesturetap","select"],s)},preventIfMoving:function(t){this._isMoved()&&t.preventDefault()},destroy:function(){var t=this;if(!t._destroyed){if(t._destroyed=!0,t.captureUpIfMoved&&n.eventCapture){var e=t.surface[0];v(function(i){e.removeEventListener(i,t.preventIfMoving)})}t.element.kendoDestroy(t.eventNS),t.surface.kendoDestroy(t.eventNS),t.element.removeData("handler"),t.surface.removeData("handler"),t._disposeAll(),t.unbind(),delete t.surface,delete t.element,delete t.currentTarget}},capture:function(){w.current=this},cancel:function(){this._disposeAll(),this.trigger("cancel")},notify:function(t,e){var i=this.touches;if(this._isMultiTouch()){switch(t){case"move":t="gesturechange";break;case"end":t="gestureend";break;case"tap":t="gesturetap"}o(e,{touches:i},f(i[0],i[1]))}return this.trigger(t,o(e,{type:t}))},press:function(t,e,i){this._apiCall("_start",t,e,i)},move:function(t,e){this._apiCall("_move",t,e)},end:function(t,e){this._apiCall("_end",t,e)},_isMultiTouch:function(){return this.touches.length>1},_maxTouchesReached:function(){return this.touches.length>=this._maxTouches},_disposeAll:function(){for(var t=this.touches;t.length>0;)t.pop().dispose()},_isMoved:function(){return t.grep(this.touches,function(t){return t.isMoved()}).length},_select:function(t){this.allowSelection&&!this.trigger("select",{event:t})||t.preventDefault()},_start:function(e){var i,n,s=0,r=this.filter,a=m(e),o=a.length,l=e.which;if(!(l&&l>1||this._maxTouchesReached()))for(w.current=null,this.currentTarget=e.currentTarget,this.stopPropagation&&e.stopPropagation();s<o&&!this._maxTouchesReached();s++)n=a[s],(i=r?t(n.currentTarget):this.element).length&&(n=new _(this,i,n),this.touches.push(n),n.press(),this._isMultiTouch()&&this.notify("gesturestart",{}))},_move:function(t){this._eachTouch("move",t)},_end:function(t){this._eachTouch("end",t)},_click:function(e){var i={touch:{initialTouch:e.target,target:t(e.currentTarget),endTime:a(),x:{location:e.pageX,client:e.clientX},y:{location:e.pageY,client:e.clientY}},x:e.pageX,y:e.pageY,target:t(e.currentTarget),event:e,type:"tap"};this.trigger("tap",i)&&e.preventDefault()},_eachTouch:function(t,e){var i,n,s,r,a={},o=m(e),l=this.touches;for(i=0;i<l.length;i++)a[(n=l[i]).id]=n;for(i=0;i<o.length;i++)(r=a[(s=o[i]).id])&&r[t](s)},_apiCall:function(e,i,n,s){this[e]({api:!0,pageX:i,pageY:n,clientX:i,clientY:n,target:t(s||this.element)[0],stopPropagation:t.noop,preventDefault:t.noop})}});w.defaultThreshold=function(t){c=t},w.minHold=function(t){u=t},i.getTouches=m,i.touchDelta=f,i.UserEvents=w}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.menu",["kendo.popup"],function(){return function(t,e){var i=window.kendo,n=i.ui,s=i._activeElement,r=i.support.touch&&i.support.mobileOS,a="mousedown",o=t.extend,l=t.proxy,h=t.each,u=i.template,c=i.keys,d=n.Widget,p=/^(ul|a|div)$/i,f=".kendoMenu",m="img",g="k-menu",_="k-link k-menu-link",v=".k-link",w="k-last",b="k-first",y="k-image",k="touchstart.kendoMenu MSPointerDown.kendoMenu pointerdown"+f,x=i.support.pointers,C=i.support.msPointers,T=C||x,S=x?"pointerenter":C?"MSPointerEnter":"mouseenter",I=x?"pointerleave":C?"MSPointerLeave":"mouseleave",D="DOMMouseScroll.kendoMenu mousewheel"+f,F=i.support.resize+f,E="group",O="groupparent",A=t(document.documentElement),M="kendoPopup",H="k-state-default",P="k-state-hover",z="k-state-focused",V="k-state-disabled",R=".k-menu-group",B=".k-animation-container",L=R+","+B,N=":not(.k-list) > .k-item",W=".k-item:not(.k-state-disabled)",U=":not(.k-item.k-separator)",q=".k-item"+U+":eq(0)",j=".k-item"+U+":last",G=".k-menu-scroll-button",Y={2:1,touch:1},$={content:u("<div #= contentCssAttributes(item) # tabindex='-1'>#= content(item) #</div>"),group:u("<ul class='#= groupCssClass(group) #'#= groupAttributes(group) # role='menu' aria-hidden='true'>#= renderItems(data) #</ul>"),itemWrapper:u("<#= tag(item) # class='#= textClass(item) #'#= textAttributes(item) #>#= image(data) ##= sprite(item) ##= text(item) ##= arrow(data) #</#= tag(item) #>"),item:u("<li class='#= wrapperCssClass(group, item) #' #= itemCssAttributes(item) # role='menuitem'  #=item.items ? \"aria-haspopup='true'\": \"\"##=item.enabled === false ? \"aria-disabled='true'\" : ''#>#= itemWrapper(data) ## if (item.items) { ##= subGroup({ items: item.items, menu: menu, group: { expanded: item.expanded } }) ## } else if (item.content || item.contentUrl) { ##= renderContent(data) ## } #</li>"),scrollButton:u("<span class='k-button k-button-icon k-menu-scroll-button k-scroll-#= direction #' unselectable='on'><span class='k-icon k-i-arrow-60-#= direction #'></span></span>"),image:u("<img #= imageCssAttributes(item) # alt='' src='#= item.imageUrl #' />"),arrow:u("<span class='#= arrowClass(item, group) #'></span>"),sprite:u("<span class='k-sprite #= spriteCssClass #'></span>"),empty:u("")},K={wrapperCssClass:function(t,e){var i="k-item",n=e.index;return!1===e.enabled?i+=" k-state-disabled":i+=" k-state-default",t.firstLevel&&0===n&&(i+=" k-first"),n==t.length-1&&(i+=" k-last"),e.cssClass&&(i+=" "+e.cssClass),e.attr&&e.attr.hasOwnProperty("class")&&(i+=" "+e.attr.class),e.selected&&(i+=" k-state-selected"),i},itemCssAttributes:function(t){var e="",i=t.attr||{};for(var n in i)i.hasOwnProperty(n)&&"class"!==n&&(e+=n+'="'+i[n]+'" ');return e},imageCssAttributes:function(t){var e="",i=t.imageAttr||{};i.class?i.class+=" "+y:i.class=y;for(var n in i)i.hasOwnProperty(n)&&(e+=n+'="'+i[n]+'" ');return e},contentCssAttributes:function(t){var e="",i=t.contentAttr||{},n="k-content k-group k-menu-group";i.class?i.class+=" "+n:i.class=n;for(var s in i)i.hasOwnProperty(s)&&(e+=s+'="'+i[s]+'" ');return e},textClass:function(){return _},textAttributes:function(t){return t.url?" href='"+t.url+"'":""},arrowClass:function(t,e){var i="k-icon";return e.horizontal?i+=" k-i-arrow-60-down":i+=" k-i-arrow-60-right",i},text:function(t){return!1===t.encoded?t.text:i.htmlEncode(t.text)},tag:function(t){return t.url?"a":"span"},groupAttributes:function(t){return!0!==t.expanded?" style='display:none'":""},groupCssClass:function(){return"k-group k-menu-group"},content:function(t){return t.content?t.content:"&nbsp;"}};function J(e,i){try{return t.contains(e,i)}catch(t){return!1}}function Q(e){(e=t(e)).addClass("k-item").children(m).addClass(y),e.children("a").addClass(_).children(m).addClass(y),e.filter(":not([disabled])").addClass(H),e.filter(".k-separator").empty().append("&nbsp;"),e.filter("li[disabled]").addClass(V).removeAttr("disabled").attr("aria-disabled",!0),e.filter("[role]").length||e.attr("role","menuitem"),e.children(v).length||e.contents().filter(function(){return!(this.nodeName.match(p)||3==this.nodeType&&!t.trim(this.nodeValue))}).wrapAll("<span class='"+_+"'/>"),X(e),Z(e)}function X(e){(e=t(e)).find("> .k-link > [class*=k-i-arrow-60]:not(.k-sprite)").remove(),e.filter(":has(.k-menu-group)").children(".k-link:not(:has([class*=k-i-arrow]:not(.k-sprite)))").each(function(){var e,n,s=t(this),r=(e=s.parent().parent(),n=i.support.isRtl(e),e.hasClass(g+"-horizontal")?" k-i-arrow-60-down":n?" k-i-arrow-60-left":" k-i-arrow-60-right");s.append("<span class='k-icon"+r+" k-menu-expand-arrow'/>")})}function Z(e){(e=t(e)).filter(".k-first:not(:first-child)").removeClass(b),e.filter(".k-last:not(:last-child)").removeClass(w),e.filter(":first-child").addClass(b),e.filter(":last-child").addClass(w)}function tt(t){if(t&&t.length)for(var e in t){var i=t.eq(e);i.find("ul").length?i.attr("aria-haspopup",!0):i.removeAttr("aria-haspopup")}}function et(t){if(!t.hasClass(g))return t.parentsUntil("."+g,"li")}function it(e,n){var s,r,a,o=(r=n.select,a=i.isFunction,r&&a(r)?r:null);o&&(s=o,t(e).children(".k-link").data({selectHandler:s})),n.items&&t(e).children("ul").children("li").each(function(t){it(this,n.items[t])})}function nt(t){return t?"li[data-groupparent='"+t+"']":"li[data-groupparent]"}function st(t){return t?"ul[data-group='"+t+"']":"ul[data-group]"}function rt(e,i){var n=[];return e.find(nt()).each(function(s,r){for(var a=(r=t(r)).data(O),o=e;a;)(o=i.find(st(a)+":visible")).length&&n.push(o),a=(r=o.find(nt())).data(O)}),n}function at(e,i){var n=e.data(E);return n?i.find(nt(n)):t([])}function ot(e,i){var n=e.data(O);return n?i.children(B).children(st(n)):t([])}function lt(t,e){for(var i=0,n=t.parentNode;n&&!isNaN(n[e]);)i+=n[e],n=n.parentNode;return i}function ht(t){return T&&t.originalEvent&&t.originalEvent.pointerType in Y}function ut(t){t.contents().filter(function(){return"LI"!=this.nodeName}).remove()}var ct=d.extend({init:function(t,e){d.fn.init.call(this,t,e),t=this.wrapper=this.element,e=this.options,this._initData(e),this._updateClasses(),this._animations(e),this.nextItemZIndex=100,this._tabindex(),this._initOverflow(e),this._attachMenuEventsHandlers(),e.openOnClick&&(this.clicked=!1),t.attr("role","menubar"),t[0].id&&(this._ariaId=i.format("{0}_mn_active",t[0].id)),i.notify(this)},events:["open","close","activate","deactivate","select"],options:{name:"Menu",animation:{open:{duration:200},close:{duration:100}},orientation:"horizontal",direction:"default",openOnClick:!1,closeOnClick:!0,hoverDelay:100,scrollable:!1,popupCollision:void 0},_initData:function(t){var e=this;t.dataSource&&(e.angular("cleanup",function(){return{elements:e.element.children()}}),e.element.empty(),e.append(t.dataSource,e.element),e.angular("compile",function(){return{elements:e.element.children()}}))},_attachMenuEventsHandlers:function(){var e=this.element,i=this.options,n=this._overflowWrapper();(n||e).on(k,".k-item",l(this._focusHandler,this)).on("click"+f,".k-item.k-state-disabled",!1).on("click"+f,".k-item",l(this._click,this)).on(k+" "+a+f,".k-content",l(this._preventClose,this)).on(S+f,W,l(this._mouseenter,this)).on(I+f,W,l(this._mouseleave,this)).on(a+f,W,l(this._mousedown,this)).on(S+f+" "+I+f+" "+a+f+" click"+f,".k-item:not(.k-state-disabled) > .k-link",l(this._toggleHover,this)),e.on("keydown"+f,l(this._keydown,this)).on("focus"+f,l(this._focus,this)).on("focus"+f,".k-content",l(this._focus,this)).on("blur"+f,l(this._removeHoverItem,this)).on("blur"+f,"[tabindex]",l(this._checkActiveElement,this)),n&&n.on(I+f,L,l(this._mouseleavePopup,this)).on(S+f,L,l(this._mouseenterPopup,this)),i.openOnClick&&(this._documentClickHandler=l(this._documentClick,this),t(document).click(this._documentClickHandler))},_detachMenuEventsHandlers:function(){var e=this._overflowWrapper();e&&e.off(f),this.element.off(f),this._documentClickHandler&&t(document).unbind("click",this._documentClickHandler)},_initOverflow:function(e){var n,s,r=this,a="horizontal"==e.orientation;if(e.scrollable){r._openedPopups={},r._scrollWrapper=r.element.wrap("<div class='k-menu-scroll-wrapper "+e.orientation+"'></div>").parent(),a&&ut(r.element),n=t($.scrollButton({direction:a?"left":"up"})),s=t($.scrollButton({direction:a?"right":"down"})),n.add(s).appendTo(r._scrollWrapper),r._initScrolling(r.element,n,s,a);var o=r.element.outerWidth(),l=r.element[0].style.width;l="auto"===l?"":l,a&&t(window).on(F,i.throttle(function(){r._setOverflowWrapperWidth(o,l),r._toggleScrollButtons(r.element,n,s,a)},100)),r._setOverflowWrapperWidth(o,l),r._toggleScrollButtons(r.element,n,s,a)}},_overflowWrapper:function(){return this._scrollWrapper||this._popupsWrapper},_setOverflowWrapperWidth:function(t,e){var i=this._scrollWrapper.css("width");this._scrollWrapper.css({width:""});var n=this._scrollWrapper.outerWidth();this._scrollWrapper.css({width:i});var s=this.element.outerWidth(),r=this.element[0].offsetWidth-this.element[0].clientWidth;if(s!=n&&n>0){var a=e?Math.min(t,n):n;this.element.width(a-r),this._scrollWrapper.width(a)}},_reinitOverflow:function(t){(t.scrollable&&!this.options.scrollable||!t.scrollable&&this.options.scrollable||t.scrollable&&this.options.scrollable&&t.scrollable.distance!=this.options.scrollable.distance||t.orientation!=this.options.orientation)&&(this._detachMenuEventsHandlers(),this._destroyOverflow(),this._initOverflow(t),this._attachMenuEventsHandlers())},_destroyOverflow:function(){var e=this._overflowWrapper();e&&(e.off(f),e.find(G).off(f).remove(),e.children(B).each(function(i,n){var s=t(n).children(R);s.off(D);var r=at(s,e);r.length&&r.append(n)}),e.find(nt()).removeAttr("data-groupparent"),e.find(st()).removeAttr("data-group"),this.element.off(D),t(window).off(F),e.contents().unwrap(),this._scrollWrapper=this._popupsWrapper=this._openedPopups=void 0)},_initScrolling:function(e,n,s,a){var o=this,l=o.options.scrollable,h=t.isNumeric(l.distance)?l.distance:50,u=h/2,c="-="+h,d="+="+h,p="-="+2*h,m="+="+2*h,g=!1,_=!1,v=function(t){var i=a?{scrollLeft:t}:{scrollTop:t};e.finish().animate(i,"fast","linear",function(){g&&v(t)}),o._toggleScrollButtons(e,n,s,a)},w=function(t){g||_||(v(t.data.direction),g=!0)},b=function(i){var l,h=a?{scrollLeft:i.data.direction}:{scrollTop:i.data.direction};l=i.originalEvent,_=r&&/touch/i.test(l.type||"")||ht(i),e.stop().animate(h,"fast","linear",function(){_?(o._toggleScrollButtons(e,n,s,a),g=!0):t(i.currentTarget).trigger(S)}),g=!1,i.stopPropagation(),i.preventDefault()};n.on(S+f,{direction:c},w).on(i.eventMap.down+f,{direction:p},b),s.on(S+f,{direction:d},w).on(i.eventMap.down+f,{direction:m},b),n.add(s).on(I+f,function(){e.stop(),g=!1,o._toggleScrollButtons(e,n,s,a)}),e.on(D,function(t){if(!t.ctrlKey&&!t.shiftKey&&!t.altKey){var i=(c=t.originalEvent,d=0,c.wheelDelta&&(d=(d=-c.wheelDelta/120)>0?Math.ceil(d):Math.floor(d)),c.detail&&(d=Math.round(c.detail/3)),d),r=Math.abs(i)*u,l=(i>0?"+=":"-=")+r,h=a?{scrollLeft:l}:{scrollTop:l};o._closeChildPopups(e),e.finish().animate(h,"fast","linear",function(){o._toggleScrollButtons(e,n,s,a)}),t.preventDefault()}var c,d})},_toggleScrollButtons:function(t,e,i,n){var s=n?t.scrollLeft():t.scrollTop(),r=n?"scrollWidth":"scrollHeight",a=n?"offsetWidth":"offsetHeight";e.toggle(0!==s),i.toggle(s<t[0][r]-t[0][a]-1)},setOptions:function(t){var e=this.options.animation;this._animations(t),t.animation=o(!0,e,t.animation),"dataSource"in t&&this._initData(t),this._updateClasses(),this._reinitOverflow(t),d.fn.setOptions.call(this,t)},destroy:function(){d.fn.destroy.call(this),this._detachMenuEventsHandlers(),this._destroyOverflow(),i.destroy(this.element)},enable:function(t,e){return this._toggleDisabled(t,!1!==e),this},disable:function(t){return this._toggleDisabled(t,!1),this},append:function(t,e){e=this.element.find(e);var i=this._insert(t,e,e.length?e.find("> .k-menu-group, > .k-animation-container > .k-menu-group"):null);return h(i.items,function(e){i.group.append(this),X(this),it(this,t[e]||t)}),X(e),Z(i.group.find(".k-first, .k-last").add(i.items)),tt(et(i.group)),this},insertBefore:function(t,e){e=this.element.find(e);var i=this._insert(t,e,e.parent());return h(i.items,function(i){e.before(this),X(this),Z(this),it(this,t[i]||t)}),Z(e),this},insertAfter:function(t,e){e=this.element.find(e);var i=this._insert(t,e,e.parent());return h(i.items,function(i){e.after(this),X(this),Z(this),it(this,t[i]||t)}),Z(e),this},_insert:function(e,i,n){var s,r;i&&i.length||(n=this.element);var a=t.isPlainObject(e),l={firstLevel:n.hasClass(g),horizontal:n.hasClass(g+"-horizontal"),expanded:!0,length:n.children().length};return i&&!n.length&&(n=t(ct.renderGroup({group:l})).appendTo(i)),a||t.isArray(e)?s=t(t.map(a?[e]:e,function(e,i){return"string"==typeof e?t(e).get():t(ct.renderItem({group:l,item:o(e,{index:i})})).get()})):(r=(s="string"==typeof e&&"<"!=e.charAt(0)?this.element.find(e):t(e)).find("> ul").addClass("k-menu-group").attr("role","menu"),(s=s.filter("li")).add(r.find("> li")).each(function(){Q(this)})),{items:s,group:n}},remove:function(t){var e=(t=this.element.find(t)).parentsUntil(this.element,N),i=t.parent("ul:not(.k-menu)");if(t.remove(),i&&!i.children(N).length){var n=et(i),s=i.parent(B);s.length?s.remove():i.remove(),tt(n)}return e.length&&(X(e=e.eq(0)),Z(e)),this},open:function(e){var n=this,s=n.options,a="horizontal"==s.orientation,h=s.direction,u=i.support.isRtl(n.wrapper),c=n._overflowWrapper();e=(c||n.element).find(e),/^(top|bottom|default)$/.test(h)&&(h=u?a?(h+" left").replace("default","bottom"):"left":a?(h+" right").replace("default","bottom"):"right");var d=">.k-popup:visible,>.k-animation-container>.k-popup:visible",p=function(){t(this).data(M)&&n.close(t(this).closest("li.k-item"),!0)};return e.siblings().find(d).each(p),c&&e.find(d).each(p),n.options.openOnClick&&(n.clicked=!0),e.each(function(){var e=t(this);clearTimeout(e.data("timer")),e.data("timer",setTimeout(function(){var d,p,f,m,_=e.find(".k-menu-group:first:hidden");if(!_[0]&&c&&(p=n._getPopup(e),_=p&&p.element),!_.is(":visible")&&_[0]&&!1===n._triggerEvent({item:e[0],type:"open"})){if(!_.find(".k-menu-group")[0]&&_.children(".k-item").length>1){var v=t(window).height(),w=function(){_.css({maxHeight:v-(i._outerHeight(_)-_.height())-i.getShadows(_).bottom,overflow:"auto"})};i.support.browser.msie&&i.support.browser.version<=7?setTimeout(w,0):w()}else _.css({maxHeight:"",overflow:""});e.data("zIndex",e.css("zIndex"));var b=n.nextItemZIndex++;e.css("zIndex",b),n.options.scrollable&&e.parent().siblings(G).css({zIndex:++b}),d=_.data(M);var y=e.parent().hasClass(g),k=y&&a,x=function(t,e,n){t=t.split(" ")[!e+0]||t;var s={origin:["bottom",n?"right":"left"],position:["top",n?"right":"left"]};return/left|right/.test(t)?(s.origin=["top",t],s.position[1]=i.directions[t].reverse):(s.origin[0]=t,s.position[0]=i.directions[t].reverse),s.origin=s.origin.join(" "),s.position=s.position.join(" "),s}(h,y,u),C=s.animation.open.effects,S=void 0!==C?C:"slideIn:"+(m=y,(f=(f=h).split(" ")[!m+0]||f).replace("top","up").replace("bottom","down"));d?((d=_.data(M)).options.origin=x.origin,d.options.position=x.position,d.options.animation.open.effects=S):d=_.kendoPopup({activate:function(){n._triggerEvent({item:this.wrapper.parent(),type:"activate"})},deactivate:function(t){t.sender.element.removeData("targetTransform").css({opacity:""}),n._triggerEvent({item:this.wrapper.parent(),type:"deactivate"})},origin:x.origin,position:x.position,collision:void 0!==s.popupCollision?s.popupCollision:k?"fit":"fit flip",anchor:e,appendTo:c||e,animation:{open:o(!0,{effects:S},s.animation.open),close:s.animation.close},open:l(n._popupOpen,n),close:function(t){var e=t.sender.wrapper.parent();if(c){var i=t.sender.element.data(E);i&&(e=(c||n.element).find(nt(i))),t.sender.wrapper.children(G).hide()}n._triggerEvent({item:e[0],type:"close"})?t.preventDefault():(e.css("zIndex",e.data("zIndex")),e.removeData("zIndex"),n.options.scrollable&&e.parent().siblings(G).css({zIndex:""}),(r||T)&&(e.removeClass(P),n._removeHoverItem()))}}).data(M),_.removeAttr("aria-hidden"),n._configurePopupOverflow(d,e),d._hovered=!0,d.open(),n._initPopupScrolling(d)}},n.options.hoverDelay))}),n},_configurePopupOverflow:function(t,e){if(this.options.scrollable&&(this._wrapPopupElement(t),!e.attr("data-groupparent"))){var i=(new Date).getTime();e.attr("data-groupparent",i),t.element.attr("data-group",i)}},_wrapPopupElement:function(t){t.element.parent().is(B)||(t.wrapper=i.wrap(t.element,t.options.autosize).css({overflow:"hidden",display:"block",position:"absolute"}))},_initPopupScrolling:function(t,e,i){this.options.scrollable&&t.element[0].scrollHeight>t.element[0].offsetHeight&&this._initPopupScrollButtons(t,e,i)},_initPopupScrollButtons:function(e,i,n){var s=this,r=e.wrapper.children(G),a=s.options.animation,o=(a&&a.open&&a.open.duration||0)+30;setTimeout(function(){if(!r.length){var a=t($.scrollButton({direction:i?"left":"up"})),o=t($.scrollButton({direction:i?"right":"down"}));r=a.add(o).appendTo(e.wrapper),s._initScrolling(e.element,a,o,i),n||r.on(S+f,function(){var i=s._overflowWrapper();t(rt(e.element,i)).each(function(t,e){var n=i.find(nt(e.data(E)));s.close(n)})}).on(I+f,function(){setTimeout(function(){t.isEmptyObject(s._openedPopups)&&s._closeParentPopups(e.element)},30)})}s._toggleScrollButtons(e.element,r.first(),r.last(),i)},o)},_popupOpen:function(t){this._keyTriggered||t.sender.element.children("."+z).removeClass(z),this.options.scrollable&&this._setPopupHeight(t.sender)},_setPopupHeight:function(e,n){var s=e.element,r=s.add(s.parent(B));r.height(s.hasClass(g)&&this._initialHeight||"");var a=e._location(n),o=t(window).height(),l=a.height,h=n?0:Math.max(a.top,0),u=n?0:lt(this._overflowWrapper()[0],"scrollTop"),c=window.innerHeight-o,d=o-i.getShadows(s).bottom+c;if(!(d+u>l+h)){var p=Math.min(d,d-h+u);r.css({overflow:"hidden",height:p+"px"})}},close:function(e,i){var n=this,s=n._overflowWrapper(),r=s||n.element;return(e=r.find(e)).length||(e=r.find(">.k-item")),e.each(function(){var r=t(this);!i&&n._isRootItem(r)&&(n.clicked=!1),clearTimeout(r.data("timer")),r.data("timer",setTimeout(function(){var i,a,o,l=n._getPopup(r);if(l&&(o=r.data(O),!s||!o||!n._openedPopups[o.toString()]||n._forceClose)){if(!n._forceClose&&(i=l.element,a=!1,t.isEmptyObject(n._openedPopups)?a:(t(rt(i,s)).each(function(t,e){return!(a=!!n._openedPopups[e.data(E).toString()])}),a)))return;l.close(),l.element.attr("aria-hidden",!0),s&&n._forceClose&&e.last().is(r[0])&&delete n._forceClose}},n.options.hoverDelay))}),n},_getPopup:function(t){var e=t.find(".k-menu-group:not(.k-list-container):not(.k-calendar-container):first:visible").data(M),i=this._overflowWrapper();if(!e&&i){var n=t.data(O);n&&(e=i.find(st(n)).data(M))}return e},_toggleDisabled:function(e,i){this.element.find(e).each(function(){t(this).toggleClass(H,i).toggleClass(V,!i).attr("aria-disabled",!i)})},_toggleHover:function(e){var n=t(i.eventTarget(e)||e.target).closest(N),s=e.type==S||-1!==a.indexOf(e.type);n.parents("li."+V).length||n.toggleClass(P,s||"mousedown"==e.type||"click"==e.type),this._removeHoverItem()},_preventClose:function(){this.options.closeOnClick||(this._closurePrevented=!0)},_checkActiveElement:function(e){var n=this,s=t(e?e.currentTarget:this._hoverItem()),r=n._findRootParent(s)[0];this._closurePrevented||setTimeout(function(){document.hasFocus()&&(J(r,i._activeElement())||!e||J(r,e.currentTarget))||n.close(r)},0),this._closurePrevented=!1},_removeHoverItem:function(){var t=this._hoverItem();t&&t.hasClass(z)&&(t.removeClass(z),this._oldHoverItem=null)},_updateClasses:function(){var t,e=this.element;e.removeClass("k-menu-horizontal k-menu-vertical"),e.addClass("k-widget k-reset k-header k-menu-init "+g).addClass(g+"-"+this.options.orientation),e.find("li > ul").filter(function(){return!i.support.matchesSelector.call(this,".k-menu-init div ul")}).addClass("k-group k-menu-group").attr("role","menu").attr("aria-hidden",e.is(":visible")).parent("li").attr("aria-haspopup","true").end().find("li > div").addClass("k-content").attr("tabindex","-1"),t=e.find("> li,.k-menu-group > li"),e.removeClass("k-menu-init"),t.each(function(){Q(this)})},_mouseenter:function(e){var i=this,n=t(e.currentTarget),s=i._itemHasChildren(n),a=n.data(O)||n.parent().data(E),o=ht(e);a&&(i._openedPopups[a.toString()]=!0),e.delegateTarget!=n.parents(".k-menu")[0]&&e.delegateTarget!=n.parents(".k-menu-scroll-wrapper,.k-popups-wrapper")[0]||(i._keyTriggered=!1,i.options.openOnClick.rootMenuItems&&i._isRootItem(n.closest(N))||i.options.openOnClick.subMenuItems&&!i._isRootItem(n.closest(N))||(!(!1===i.options.openOnClick||!1===i.options.openOnClick.rootMenuItems&&i._isRootItem(n.closest(N))||!1===i.options.openOnClick.subMenuItems&&!i._isRootItem(n.closest(N))||i.clicked)||r||o&&i._isRootItem(n.closest(N))||!J(e.currentTarget,e.relatedTarget)&&s&&i.open(n),(!0===i.options.openOnClick&&i.clicked||r)&&n.siblings().each(l(function(t,e){i.close(e,!0)},i))))},_mousedown:function(e){var i=this,n=t(e.currentTarget);(i.options.openOnClick.subMenuItems&&!i._isRootItem(n)||r)&&n.siblings().each(l(function(t,e){i.close(e,!0)},i))},_mouseleave:function(e){var n=t(e.currentTarget),s=n.data(O),a=n.children(B).length||n.children(R).length||s,o=t(window);s&&delete this._openedPopups[s.toString()],n.parentsUntil(B,".k-list-container,.k-calendar-container")[0]?e.stopImmediatePropagation():!1!==this.options.openOnClick&&(this.options.openOnClick.rootMenuItems||!this._isRootItem(n))&&(this.options.openOnClick.subMenuItems||this._isRootItem(n))||r||ht(e)||J(e.currentTarget,e.relatedTarget||e.target)||!a||J(e.currentTarget,i._activeElement())?(i.support.browser.msie&&!e.toElement&&!e.relatedTarget&&!ht(e)||e.clientX<0||e.clientY<0||e.clientY>o.height()||e.clientX>o.width())&&this.close(n):this.close(n,!0)},_mouseenterPopup:function(e){var i=t(e.currentTarget);if(!i.parent().is(B)){var n=(i=i.children("ul")).data(E);n&&(this._openedPopups[n.toString()]=!0)}},_mouseleavePopup:function(e){var i=t(e.currentTarget);!ht(e)&&i.is(B)&&this._closePopups(i.children("ul"))},_closePopups:function(e){var i=this,n=i._overflowWrapper(),s=e.data(E);if(s){delete i._openedPopups[s.toString()];var r=n.find(nt(s));setTimeout(function(){if(i.options.openOnClick)i._closeChildPopups(e);else if(t.isEmptyObject(i._openedPopups)){var n=i._innerPopup(e);i._closeParentPopups(n)}else i.close(r,!0)},0)}},_closeChildPopups:function(e){var i=this,n=i._overflowWrapper();t(rt(e,n)).each(function(){var t=n.find(nt(this.data(E)));i.close(t,!0)})},_innerPopup:function(t){var e=rt(t,this._overflowWrapper());return e[e.length-1]||t},_closeParentPopups:function(t){var e=this._overflowWrapper(),i=t.data(E),n=e.find(nt(i));for(i=n.parent().data(E),this.close(n,!0);i&&!this._openedPopups[i]&&!n.parent().is(".k-menu");)n=e.find(nt(i)),this.close(n,!0),i=n.parent().data(E)},_click:function(e){for(var n,s,r,a,o=this.options,l=t(i.eventTarget(e)),h=l[0],u=l[0]?l[0].nodeName.toUpperCase():"",c="INPUT"==u||"SELECT"==u||"BUTTON"==u||"LABEL"==u,d=l.closest(v),p=l.closest(N),f=p[0],m=d.attr("href"),g=l.attr("href"),_=t("<a href='#' />").attr("href"),w=!!m&&m!==_,b=w&&!!m.match(/^#/),y=!!g&&g!==_,k=this._overflowWrapper();h&&h.parentNode!=f;)h=h.parentNode;if(!t(h).is("div:not(.k-animation-container,.k-list-container)"))if(p.hasClass(V))e.preventDefault();else{if(e.handled||!this._triggerSelect(l,f)||c||e.preventDefault(),e.handled=!0,s=p.children(L),k){var x=p.data(O);x&&(s=k.find(st(x)))}if(r=s.is(":visible"),a=o.openOnClick&&r&&this._isRootItem(p),o.closeOnClick&&(!w||b)&&(!s.length||a)){p.removeClass(P).css("height"),this._oldHoverItem=this._findRootParent(p);var C=this._parentsUntil(d,this.element,N);return this._forceClose=!!k,this.close(C),this.clicked=!1,void(-1!="MSPointerUp".indexOf(e.type)&&e.preventDefault())}w&&e.enterKey&&d[0].click(),(this._isRootItem(p)&&!1!==o.openOnClick||o.openOnClick.subMenuItems||i.support.touch||ht(e)&&this._isRootItem(p.closest(N)))&&(w||c||y||e.preventDefault(),this.clicked=!0,n=s.is(":visible")?"close":"open",(o.closeOnClick||"close"!=n)&&this[n](p))}},_parentsUntil:function(e,i,n){var s=this._overflowWrapper();if(s){var r=function(e,i){var n=[],s=function(t){for(;t.parentNode&&!i.is(t.parentNode);)n.push(t.parentNode),t=t.parentNode},r=e[0]||e;s(r);for(var a=n[n.length-1];t(a).is(B)&&(r=at(t(a).children("ul"),i)[0]);)n.push(r),s(r),a=n[n.length-1];return n}(e,s),a=[];return t(r).each(function(){var e=t(this);if(e.is(i))return!1;e.is(n)&&a.push(this)}),t(a)}return e.parentsUntil(i,n)},_triggerSelect:function(t,e){var i,n=(t=t.is(".k-link")?t:t.closest(".k-link")).data("selectHandler");n&&(i=this._getEventData(t),n.call(this,i));var s=i&&i.isDefaultPrevented(),r=this._triggerEvent({item:e,type:"select"});return s||r},_getEventData:function(t){return{sender:this,target:t,_defaultPrevented:!1,preventDefault:function(){this._defaultPrevented=!0},isDefaultPrevented:function(){return this._defaultPrevented}}},_documentClick:function(t){J((this._overflowWrapper()||this.element)[0],t.target)||(this.clicked=!1)},_focus:function(e){var i=e.target,n=this._hoverItem(),r=s();if(i!=this.wrapper[0]&&!t(i).is(":kendoFocusable"))return e.stopPropagation(),t(i).closest(".k-content").closest(".k-menu-group").closest(".k-item").addClass(z),void this.wrapper.focus();r===e.currentTarget&&(n.length?this._moveHover([],n):this._oldHoverItem||this._moveHover([],this.wrapper.children().first()))},_keydown:function(t){var e,n,s,r=t.keyCode,a=this._oldHoverItem,o=i.support.isRtl(this.wrapper);if(t.target==t.currentTarget||r==c.ESC){if(a||(a=this._oldHoverItem=this._hoverItem()),n=this._itemBelongsToVertival(a),s=this._itemHasChildren(a),this._keyTriggered=!0,r==c.RIGHT)e=this[o?"_itemLeft":"_itemRight"](a,n,s);else if(r==c.LEFT)e=this[o?"_itemRight":"_itemLeft"](a,n,s);else if(r==c.DOWN)e=this._itemDown(a,n,s);else if(r==c.UP)e=this._itemUp(a,n,s);else if(r==c.HOME)this._moveHover(a,a.parent().children().first()),t.preventDefault();else if(r==c.END)this._moveHover(a,a.parent().children().last()),t.preventDefault();else if(r==c.ESC)e=this._itemEsc(a,n);else if(r==c.ENTER||r==c.SPACEBAR)(e=a.children(".k-link")).length>0&&(this._click({target:e[0],preventDefault:function(){},enterKey:!0}),s&&!a.hasClass(V)?(this.open(a),this._moveHover(a,this._childPopupElement(a).children().first())):this._moveHover(a,this._findRootParent(a)));else if(r==c.TAB)return e=this._findRootParent(a),this._moveHover(a,e),void this._checkActiveElement();e&&e[0]&&(t.preventDefault(),t.stopPropagation())}},_hoverItem:function(){return this.wrapper.find(".k-item.k-state-hover,.k-item.k-state-focused").filter(":visible")},_itemBelongsToVertival:function(t){var e=this.wrapper.hasClass("k-menu-vertical");return t.length?t.parent().hasClass("k-menu-group")||e:e},_itemHasChildren:function(t){return!!(t&&t.length&&t[0].nodeType)&&(t.children(".k-menu-group, div.k-animation-container").length>0||!!t.data(O)&&!!this._overflowWrapper().children(st(t.data(O))))},_moveHover:function(e,i){var n=this._ariaId;e.length&&i.length&&e.removeClass(z),i.length&&(i[0].id&&(n=i[0].id),i.addClass(z),this._oldHoverItem=i,n&&(this.element.removeAttr("aria-activedescendant"),t("#"+n).removeAttr("id"),i.attr("id",n),this.element.attr("aria-activedescendant",n)),this._scrollToItem(i))},_findRootParent:function(t){return this._isRootItem(t)?t:this._parentsUntil(t,".k-menu","li.k-item").last()},_isRootItem:function(t){return t.parent().hasClass(g)},_itemRight:function(t,e,i){var n,s,r;if(e){if(i&&!t.hasClass(V))this.open(t),n=this._childPopupElement(t).children().first();else if("horizontal"==this.options.orientation){if(s=this._findRootParent(t),r=this._overflowWrapper()){var a=ot(s,r);this._closeChildPopups(a)}this.close(s),n=s.nextAll(q)}}else(n=t.nextAll(q)).length||(n=t.prevAll(j)),this.close(t);return n&&!n.length?n=this.wrapper.children(".k-item").first():n||(n=[]),this._moveHover(t,n),n},_itemLeft:function(t,e){var i,n;return e?(i=t.parent().closest(".k-item"),n=this._overflowWrapper(),!i.length&&n&&(i=at(t.parent(),n)),this.close(i),this._isRootItem(i)&&"horizontal"==this.options.orientation&&(i=i.prevAll(q))):((i=t.prevAll(q)).length||(i=t.nextAll(j)),this.close(t)),i.length||(i=this.wrapper.children(".k-item").last()),this._moveHover(t,i),i},_itemDown:function(t,e,i){var n;if(e)n=t.nextAll(q);else{if(!i||t.hasClass(V))return;this.open(t),n=this._childPopupElement(t).children().first()}return!n.length&&t.length?n=t.parent().children().first():t.length||(n=this.wrapper.children(".k-item").first()),this._moveHover(t,n),n},_itemUp:function(t,e){var i;if(e)return!(i=t.prevAll(q)).length&&t.length?i=t.parent().children().last():t.length||(i=this.wrapper.children(".k-item").last()),this._moveHover(t,i),i},_scrollToItem:function(t){var e=this;if(e.options.scrollable&&t&&t.length){var n,s=t.parent(),r=!!s.hasClass(g)&&"horizontal"==e.options.orientation,a=r?"scrollLeft":"scrollTop",o=r?i._outerWidth:i._outerHeight,l=s[a](),h=o(t),u=t[0][r?"offsetLeft":"offsetTop"],c=o(s),d=s.siblings(G),p=d.length?o(d.first()):0;if(l+c<u+h+p?n=u+h-c+p:l>u-p&&(n=u-p),!isNaN(n)){var f={};f[a]=n,s.finish().animate(f,"fast","linear",function(){e._toggleScrollButtons(s,d.first(),d.last(),r)})}}},_itemEsc:function(t,e){var i;return e?(i=t.parent().closest(".k-item"),this.close(i),this._moveHover(t,i),i):t},_childPopupElement:function(t){var e=t.find(".k-menu-group"),i=this._overflowWrapper();return!e.length&&i&&(e=ot(t,i)),e},_triggerEvent:function(t){return this.trigger(t.type,{type:t.type,item:t.item})},_focusHandler:function(e){var n=this,s=t(i.eventTarget(e)).closest(N);s.hasClass(V)||setTimeout(function(){n._moveHover([],s),s.children(".k-content")[0]&&s.parent().closest(".k-item").removeClass(z)},200)},_animations:function(t){t&&"animation"in t&&!t.animation&&(t.animation={open:{effects:{}},close:{hide:!0,effects:{}}})}});o(ct,{renderItem:function(t){t=o({menu:{},group:{}},t);var e=$.empty,i=t.item;return $.item(o(t,{image:i.imageUrl?$.image:e,sprite:i.spriteCssClass?$.sprite:e,itemWrapper:$.itemWrapper,renderContent:ct.renderContent,arrow:i.items||i.content?$.arrow:e,subGroup:ct.renderGroup},K))},renderGroup:function(t){return $.group(o({renderItems:function(t){for(var e="",i=0,n=t.items,s=n?n.length:0,r=o({length:s},t.group);i<s;i++)e+=ct.renderItem(o(t,{group:r,item:o({index:i},n[i])}));return e}},t,K))},renderContent:function(t){return $.content(o(t,K))}});var dt=ct.extend({init:function(e,n){ct.fn.init.call(this,e,n),this._marker=i.guid().substring(0,8),this.target=t(this.options.target),this._popup(),this._wire()},_initOverflow:function(t){t.scrollable&&!this._overflowWrapper()&&(this._openedPopups={},this._popupsWrapper=(this.element.parent().is(B)?this.element.parent():this.element).wrap("<div class='k-popups-wrapper "+t.orientation+"'></div>").parent(),"horizontal"==this.options.orientation&&ut(this.element),t.appendTo&&t.appendTo.append(this._popupsWrapper),this._initialHeight=this.element[0].style.height,this._initialWidth=this.element[0].style.width)},options:{name:"ContextMenu",filter:null,showOn:"contextmenu",orientation:"vertical",alignToAnchor:!1,target:"body"},events:["open","close","activate","deactivate","select"],setOptions:function(e){ct.fn.setOptions.call(this,e),this.target.off(this.showOn+f+this._marker,this._showProxy),this.userEvents&&this.userEvents.destroy(),this.target=t(this.options.target),e.orientation&&this.popup.wrapper[0]&&this.popup.element.unwrap(),this._wire(),ct.fn.setOptions.call(this,e)},destroy:function(){this.target.off(this.options.showOn+f+this._marker),A.off(i.support.mousedown+f+this._marker,this._closeProxy),this.userEvents&&this.userEvents.destroy(),ct.fn.destroy.call(this)},open:function(e,n){if(e=t(e)[0],J(this.element[0],t(e)[0])||this._itemHasChildren(t(e)))ct.fn.open.call(this,e);else if(!1===this._triggerEvent({item:this.element,type:"open"})){if(this.popup.visible()&&this.options.filter&&(this.popup.close(!0),this.popup.element.kendoStop(!0)),void 0!==n){var s=this._overflowWrapper();if(s){var r=s.offset();e-=r.left,n-=r.top}this.popup.wrapper.hide(),this._configurePopupScrolling(e,n),this.popup.open(e,n)}else this.popup.options.anchor=e||this.popup.anchor||this.target,this.popup.element.kendoStop(!0),this._configurePopupScrolling(),this.popup.open();A.off(this.popup.downEvent,this.popup._mousedownProxy),A.on(i.support.mousedown+f+this._marker,this._closeProxy)}return this},_configurePopupScrolling:function(t,e){var i=this.popup,n="horizontal"==this.options.orientation;this.options.scrollable&&(this._wrapPopupElement(i),i.element.parent().css({position:"",height:""}),i.element.css({visibility:"hidden",display:"",position:""}),n?this._setPopupWidth(i,isNaN(t)?void 0:{isFixed:!0,x:t,y:e}):this._setPopupHeight(i,isNaN(t)?void 0:{isFixed:!0,x:t,y:e}),i.element.css({visibility:"",display:"none",position:"absolute"}),this._initPopupScrollButtons(i,n,!0),i.element.siblings(G).hide())},_setPopupWidth:function(e,n){var s=e.element,r=s.add(s.parent(B));r.width(this._initialWidth||"");var a=e._location(n),o=t(window).width(),l=a.width,h=Math.max(a.left,0),u=n?0:lt(this._overflowWrapper()[0],"scrollLeft"),c=i.getShadows(s),d=o-c.left-c.right;d+u>l+h||r.css({overflow:"hidden",width:d-h+u+"px"})},close:function(){J(this.element[0],t(arguments[0])[0])||this._itemHasChildren(arguments[0])?ct.fn.close.call(this,arguments[0]):this.popup.visible()&&!1===this._triggerEvent({item:this.element,type:"close"})&&(this.popup.close(),A.off(i.support.mousedown+f+this._marker,this._closeProxy),this.unbind("select",this._closeTimeoutProxy))},_showHandler:function(t){var e,n=t,s=this.options;t.event&&((n=t.event).pageX=t.x.location,n.pageY=t.y.location),J(this.element[0],t.relatedTarget||t.target)||(this._eventOrigin=n,n.preventDefault(),n.stopImmediatePropagation(),this.element.find("."+z).removeClass(z),(s.filter&&i.support.matchesSelector.call(n.currentTarget,s.filter)||!s.filter)&&(s.alignToAnchor?(this.popup.options.anchor=n.currentTarget,this.open(n.currentTarget)):(this.popup.options.anchor=n.currentTarget,this._targetChild?(e=this.target.offset(),this.open(n.pageX-e.left,n.pageY-e.top)):this.open(n.pageX,n.pageY))))},_closeHandler:function(e){var i=t(e.relatedTarget||e.target),n=i.closest(this.target.selector)[0]==this.target[0],s=i.closest(".k-item"),r=this._itemHasChildren(s),a=this._overflowWrapper(),o=J(this.element[0],i[0])||a&&J(a[0],i[0]);this._eventOrigin=e;var l=3!==e.which;this.popup.visible()&&(l&&n||!n)&&(this.options.closeOnClick&&!r&&o||!o)&&(o?(this.unbind("select",this._closeTimeoutProxy),this.bind("select",this._closeTimeoutProxy)):this.close())},_wire:function(){var t=this.options,e=this.target;this._showProxy=l(this._showHandler,this),this._closeProxy=l(this._closeHandler,this),this._closeTimeoutProxy=l(this.close,this),e[0]&&(i.support.mobileOS&&"contextmenu"==t.showOn?(this.userEvents=new i.UserEvents(e,{filter:t.filter,allowSelection:!1}),e.on(t.showOn+f+this._marker,!1),this.userEvents.bind("hold",this._showProxy)):t.filter?e.on(t.showOn+f+this._marker,t.filter,this._showProxy):e.on(t.showOn+f+this._marker,this._showProxy))},_triggerEvent:function(e){var i=t(this.popup.options.anchor)[0],n=this._eventOrigin;return this._eventOrigin=void 0,this.trigger(e.type,o({type:e.type,item:e.item||this.element[0],target:i},n?{event:n}:{}))},_popup:function(){var e=this._overflowWrapper();this._triggerProxy=l(this._triggerEvent,this),this.popup=this.element.addClass("k-context-menu").kendoPopup({anchor:this.target||"body",copyAnchorStyles:this.options.copyAnchorStyles,collision:this.options.popupCollision||"fit",animation:this.options.animation,activate:this._triggerProxy,deactivate:this._triggerProxy,appendTo:e||this.options.appendTo,close:e?function(i){t(rt(i.sender.element,e)).each(function(t,e){var i=e.data(M);i&&i.close(!0)})}:t.noop}).data(M),this._targetChild=J(this.target[0],this.popup.element[0])}});n.plugin(ct),n.plugin(dt)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.draganddrop",["kendo.core","kendo.userevents"],function(){return function(t,e){var i,n=window.kendo,s=n.support,r=window.document,a=t(window),o=n.Class,l=n.ui.Widget,h=n.Observable,u=n.UserEvents,c=t.proxy,d=t.extend,p=n.getOffset,f={},m={},g={},_=n.elementUnderCursor,v="dragleave";function w(t,e){return parseInt(t.css(e),10)||0}function b(t,e){return Math.min(Math.max(t,e.min),e.max)}function y(t,e){var i=p(t),s=n._outerWidth,r=n._outerHeight,a=i.left+w(t,"borderLeftWidth")+w(t,"paddingLeft"),o=i.top+w(t,"borderTopWidth")+w(t,"paddingTop");return{x:{min:a,max:a+t.width()-s(e,!0)},y:{min:o,max:o+t.height()-r(e,!0)}}}var k,x=h.extend({init:function(e,i){var s=this,r=e[0];s.capture=!1,r.addEventListener?(t.each(n.eventMap.down.split(" "),function(){r.addEventListener(this,c(s._press,s),!0)}),t.each(n.eventMap.up.split(" "),function(){r.addEventListener(this,c(s._release,s),!0)})):(t.each(n.eventMap.down.split(" "),function(){r.attachEvent(this,c(s._press,s))}),t.each(n.eventMap.up.split(" "),function(){r.attachEvent(this,c(s._release,s))})),h.fn.init.call(s),s.bind(["press","release"],i||{})},captureNext:function(){this.capture=!0},cancelCapture:function(){this.capture=!1},_press:function(t){this.trigger("press"),this.capture&&t.preventDefault()},_release:function(t){this.trigger("release"),this.capture&&(t.preventDefault(),this.cancelCapture())}}),C=h.extend({init:function(e){h.fn.init.call(this),this.forcedEnabled=!1,t.extend(this,e),this.scale=1,this.horizontal?(this.measure="offsetWidth",this.scrollSize="scrollWidth",this.axis="x"):(this.measure="offsetHeight",this.scrollSize="scrollHeight",this.axis="y")},makeVirtual:function(){t.extend(this,{virtual:!0,forcedEnabled:!0,_virtualMin:0,_virtualMax:0})},virtualSize:function(t,e){this._virtualMin===t&&this._virtualMax===e||(this._virtualMin=t,this._virtualMax=e,this.update())},outOfBounds:function(t){return t>this.max||t<this.min},forceEnabled:function(){this.forcedEnabled=!0},getSize:function(){return this.container[0][this.measure]},getTotal:function(){return this.element[0][this.scrollSize]},rescale:function(t){this.scale=t},update:function(t){var e=this.virtual?this._virtualMax:this.getTotal(),i=e*this.scale,n=this.getSize();(0!==e||this.forcedEnabled)&&(this.max=this.virtual?-this._virtualMin:0,this.size=n,this.total=i,this.min=Math.min(this.max,n-i),this.minScale=n/e,this.centerOffset=(i-n)/2,this.enabled=this.forcedEnabled||i>n,t||this.trigger("change",this))}}),T=h.extend({init:function(t){h.fn.init.call(this),this.x=new C(d({horizontal:!0},t)),this.y=new C(d({horizontal:!1},t)),this.container=t.container,this.forcedMinScale=t.minScale,this.maxScale=t.maxScale||100,this.bind("change",t)},rescale:function(t){this.x.rescale(t),this.y.rescale(t),this.refresh()},centerCoordinates:function(){return{x:Math.min(0,-this.x.centerOffset),y:Math.min(0,-this.y.centerOffset)}},refresh:function(){this.x.update(),this.y.update(),this.enabled=this.x.enabled||this.y.enabled,this.minScale=this.forcedMinScale||Math.min(this.x.minScale,this.y.minScale),this.fitScale=Math.max(this.x.minScale,this.y.minScale),this.trigger("change")}}),S=h.extend({init:function(t){d(this,t),h.fn.init.call(this)},outOfBounds:function(){return this.dimension.outOfBounds(this.movable[this.axis])},dragMove:function(t){var e=this.dimension,i=this.axis,n=this.movable,s=n[i]+t;e.enabled&&((s<e.min&&t<0||s>e.max&&t>0)&&(t*=this.resistance),n.translateAxis(i,t),this.trigger("change",this))}}),I=o.extend({init:function(e){var i,n,s,r,a=this;d(a,{elastic:!0},e),s=a.elastic?.5:0,r=a.movable,a.x=i=new S({axis:"x",dimension:a.dimensions.x,resistance:s,movable:r}),a.y=n=new S({axis:"y",dimension:a.dimensions.y,resistance:s,movable:r}),a.userEvents.bind(["press","move","end","gesturestart","gesturechange"],{gesturestart:function(t){a.gesture=t,a.offset=a.dimensions.container.offset()},press:function(e){t(e.event.target).closest("a").is("[data-navigate-on-press=true]")&&e.sender.cancel()},gesturechange:function(t){var e,s=a.gesture,o=s.center,l=t.center,h=t.distance/s.distance,u=a.dimensions.minScale,c=a.dimensions.maxScale;r.scale<=u&&h<1&&(h+=.8*(1-h)),r.scale*h>=c&&(h=c/r.scale);var d=r.x+a.offset.left,p=r.y+a.offset.top;e={x:(d-o.x)*h+l.x-d,y:(p-o.y)*h+l.y-p},r.scaleWith(h),i.dragMove(e.x),n.dragMove(e.y),a.dimensions.rescale(r.scale),a.gesture=t,t.preventDefault()},move:function(t){t.event.target.tagName.match(/textarea|input/i)||(i.dimension.enabled||n.dimension.enabled?(i.dragMove(t.x.delta),n.dragMove(t.y.delta),t.preventDefault()):t.touch.skip())},end:function(t){t.preventDefault()}})}}),D=s.transitions.prefix+"Transform";k=s.hasHW3D?function(t,e,i){return"translate3d("+t+"px,"+e+"px,0) scale("+i+")"}:function(t,e,i){return"translate("+t+"px,"+e+"px) scale("+i+")"};var F=h.extend({init:function(e){h.fn.init.call(this),this.element=t(e),this.element[0].style.webkitTransformOrigin="left top",this.x=0,this.y=0,this.scale=1,this._saveCoordinates(k(this.x,this.y,this.scale))},translateAxis:function(t,e){this[t]+=e,this.refresh()},scaleTo:function(t){this.scale=t,this.refresh()},scaleWith:function(t){this.scale*=t,this.refresh()},translate:function(t){this.x+=t.x,this.y+=t.y,this.refresh()},moveAxis:function(t,e){this[t]=e,this.refresh()},moveTo:function(t){d(this,t),this.refresh()},refresh:function(){var t,e=this.x,i=this.y;this.round&&(e=Math.round(e),i=Math.round(i)),(t=k(e,i,this.scale))!=this.coordinates&&(n.support.browser.msie&&n.support.browser.version<10?(this.element[0].style.position="absolute",this.element[0].style.left=this.x+"px",this.element[0].style.top=this.y+"px"):this.element[0].style[D]=t,this._saveCoordinates(t),this.trigger("change"))},_saveCoordinates:function(t){this.coordinates=t}});function E(t,e){var i,n=e.options.group,s=t[n];if(l.fn.destroy.call(e),s.length>1){for(i=0;i<s.length;i++)if(s[i]==e){s.splice(i,1);break}}else s.length=0,delete t[n]}var O=l.extend({init:function(t,e){l.fn.init.call(this,t,e);var i=this.options.group;i in m?m[i].push(this):m[i]=[this]},events:["dragenter",v,"drop"],options:{name:"DropTarget",group:"default"},destroy:function(){E(m,this)},_trigger:function(t,e){var i=f[this.options.group];if(i)return this.trigger(t,d({},e.event,{draggable:i,dropTarget:e.dropTarget}))},_over:function(t){this._trigger("dragenter",t)},_out:function(t){this._trigger(v,t)},_drop:function(t){var e=f[this.options.group];e&&(e.dropped=!this._trigger("drop",t))}});O.destroyGroup=function(t){var e,i=m[t]||g[t];if(i){for(e=0;e<i.length;e++)l.fn.destroy.call(i[e]);i.length=0,delete m[t],delete g[t]}},O._cache=m;var A=O.extend({init:function(t,e){l.fn.init.call(this,t,e);var i=this.options.group;i in g?g[i].push(this):g[i]=[this]},destroy:function(){E(g,this)},options:{name:"DropTargetArea",group:"default",filter:null}}),M=l.extend({init:function(t,e){l.fn.init.call(this,t,e),this._activated=!1,this.userEvents=new u(this.element,{global:!0,allowSelection:!0,filter:this.options.filter,threshold:this.options.distance,start:c(this._start,this),hold:c(this._hold,this),move:c(this._drag,this),end:c(this._end,this),cancel:c(this._cancel,this),select:c(this._select,this)}),this._afterEndHandler=c(this._afterEnd,this),this._captureEscape=c(this._captureEscape,this)},events:["hold","dragstart","drag","dragend","dragcancel","hintDestroyed"],options:{name:"Draggable",distance:n.support.touch?0:5,group:"default",cursorOffset:null,axis:null,container:null,filter:null,ignore:null,holdToDrag:!1,autoScroll:!1,dropped:!1},cancelHold:function(){this._activated=!1},_captureEscape:function(t){t.keyCode===n.keys.ESC&&(this._trigger("dragcancel",{event:t}),this.userEvents.cancel())},_updateHint:function(e){var i,n=this.options,s=this.boundaries,r=n.axis,a=this.options.cursorOffset;a?i={left:e.x.location+a.left,top:e.y.location+a.top}:(this.hintOffset.left+=e.x.delta,this.hintOffset.top+=e.y.delta,i=t.extend({},this.hintOffset)),s&&(i.top=b(i.top,s.y),i.left=b(i.left,s.x)),"x"===r?delete i.top:"y"===r&&delete i.left,this.hint.css(i)},_shouldIgnoreTarget:function(e){var i=this.options.ignore;return i&&t(e).is(i)},_select:function(t){this._shouldIgnoreTarget(t.event.target)||t.preventDefault()},_start:function(e){var i=this,s=i.options,a=s.container?t(s.container):null,o=s.hint;if(this._shouldIgnoreTarget(e.touch.initialTouch)||s.holdToDrag&&!i._activated)i.userEvents.cancel();else{if(i.currentTarget=e.target,i.currentTargetOffset=p(i.currentTarget),o){i.hint&&i.hint.stop(!0,!0).remove(),i.hint=n.isFunction(o)?t(o.call(i,i.currentTarget)):o;var l=p(i.currentTarget);i.hintOffset=l,i.hint.css({position:"absolute",zIndex:2e4,left:l.left,top:l.top}).appendTo(r.body),i.angular("compile",function(){i.hint.removeAttr("ng-repeat");for(var n=t(e.target);!n.data("$$kendoScope")&&n.length;)n=n.parent();return{elements:i.hint.get(),scopeFrom:n.data("$$kendoScope")}})}f[s.group]=i,i.dropped=!1,a&&(i.boundaries=y(a,i.hint)),t(r).on("keyup",i._captureEscape),i._trigger("dragstart",e)&&(i.userEvents.cancel(),i._afterEnd()),i.userEvents.capture()}},_hold:function(t){this.currentTarget=t.target,this._trigger("hold",t)?this.userEvents.cancel():this._activated=!0},_drag:function(e){e.preventDefault();var i=this._elementUnderCursor(e);if(this.options.autoScroll&&this._cursorElement!==i&&(this._scrollableParent=z(i),this._cursorElement=i),this._lastEvent=e,this._processMovement(e,i),this.options.autoScroll&&this._scrollableParent[0]){var n=V(e.x.location,e.y.location,H(this._scrollableParent));this._scrollCompenstation=t.extend({},this.hintOffset),this._scrollVelocity=n,0===n.y&&0===n.x?(clearInterval(this._scrollInterval),this._scrollInterval=null):this._scrollInterval||(this._scrollInterval=setInterval(t.proxy(this,"_autoScroll"),50))}this.hint&&this._updateHint(e)},_processMovement:function(e,n){this._withDropTarget(n,function(n,s){if(n){if(i){if(s===i.targetElement)return;i._trigger(v,d(e,{dropTarget:t(i.targetElement)}))}n._trigger("dragenter",d(e,{dropTarget:t(s)})),i=d(n,{targetElement:s})}else i&&(i._trigger(v,d(e,{dropTarget:t(i.targetElement)})),i=null)}),this._trigger("drag",d(e,{dropTarget:i,elementUnderCursor:n}))},_autoScroll:function(){var t=this._scrollableParent[0],e=this._scrollVelocity,i=this._scrollCompenstation;if(t){var n,s,o=this._elementUnderCursor(this._lastEvent);this._processMovement(this._lastEvent,o);var l=t===P()[0];l?(n=r.body.scrollHeight>a.height(),s=r.body.scrollWidth>a.width()):(n=t.offsetHeight<=t.scrollHeight,s=t.offsetWidth<=t.scrollWidth);var h=t.scrollTop+e.y,u=n&&h>0&&h<t.scrollHeight,c=t.scrollLeft+e.x,d=s&&c>0&&c<t.scrollWidth;u&&(t.scrollTop+=e.y),d&&(t.scrollLeft+=e.x),this.hint&&l&&(d||u)&&(u&&(i.top+=e.y),d&&(i.left+=e.x),this.hint.css(i))}},_end:function(e){this._withDropTarget(this._elementUnderCursor(e),function(n,s){n&&(n._drop(d({},e,{dropTarget:t(s)})),i=null)}),this._cancel(this._trigger("dragend",e))},_cancel:function(t){var e=this;e._scrollableParent=null,this._cursorElement=null,clearInterval(this._scrollInterval),e._activated=!1,e.hint&&!e.dropped?setTimeout(function(){e.hint.stop(!0,!0),t?e._afterEndHandler():e.hint.animate(e.currentTargetOffset,"fast",e._afterEndHandler)},0):e._afterEnd()},_trigger:function(t,e){return this.trigger(t,d({},e.event,{x:e.x,y:e.y,currentTarget:this.currentTarget,initialTarget:e.touch?e.touch.initialTouch:null,dropTarget:e.dropTarget,elementUnderCursor:e.elementUnderCursor}))},_elementUnderCursor:function(e){var i=_(e),n=this.hint;return n&&function(e,i){try{return t.contains(e,i)||e==i}catch(t){return!1}}(n[0],i)&&(n.hide(),(i=_(e))||(i=_(e)),n.show()),i},_withDropTarget:function(i,n){var r,a=this.options.group,o=m[a],l=g[a];(o&&o.length||l&&l.length)&&((r=function(i,n,r){for(var a,o,l=0,h=n&&n.length,u=r&&r.length;i&&i.parentNode;){for(l=0;l<h;l++)if((a=n[l]).element[0]===i)return{target:a,targetElement:i};for(l=0;l<u;l++)if(o=r[l],t.contains(o.element[0],i)&&s.matchesSelector.call(i,o.options.filter))return{target:o,targetElement:i};i=i.parentNode}return e}(i,o,l))?n(r.target,r.targetElement):n())},destroy:function(){l.fn.destroy.call(this),this._afterEnd(),this.userEvents.destroy(),this._scrollableParent=null,this._cursorElement=null,clearInterval(this._scrollInterval),this.currentTarget=null},_afterEnd:function(){this.hint&&this.hint.remove(),delete f[this.options.group],this.trigger("destroy"),this.trigger("hintDestroyed"),t(r).off("keyup",this._captureEscape)}});function H(t){var e,i,n,s=P()[0];return t[0]===s?{top:i=s.scrollTop,left:n=s.scrollLeft,bottom:i+a.height(),right:n+a.width()}:((e=t.offset()).bottom=e.top+t.height(),e.right=e.left+t.width(),e)}function P(){return t(n.support.browser.edge||n.support.browser.safari?r.body:r.documentElement)}function z(e){var i=P();if(!e||e===r.body||e===r.documentElement)return i;for(var s=t(e)[0];s&&!n.isScrollable(s)&&s!==r.body;)s=s.parentNode;return s===r.body?i:t(s)}function V(t,e,i){var n={x:0,y:0};return t-i.left<50?n.x=-(50-(t-i.left)):i.right-t<50&&(n.x=50-(i.right-t)),e-i.top<50?n.y=-(50-(e-i.top)):i.bottom-e<50&&(n.y=50-(i.bottom-e)),n}n.ui.plugin(O),n.ui.plugin(A),n.ui.plugin(M),n.TapCapture=x,n.containerBoundaries=y,d(n.ui,{Pane:I,PaneDimensions:T,Movable:F}),n.ui.Draggable.utils={autoScrollVelocity:V,scrollableViewPort:H,findScrollableParent:z}}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.slider",["kendo.draganddrop"],function(){return function(t,e){var i=window.kendo,n=i.ui.Widget,s=i.ui.Draggable,r=i._outerWidth,a=i._outerHeight,o=t.extend,l=i.format,h=i.parseFloat,u=t.proxy,c=t.isArray,d=Math,p=i.support,f=p.pointers,m=p.msPointers,g=".slider",_="touchstart.slider mousedown.slider",v=f?"pointerdown.slider":m?"MSPointerDown.slider":_,w="touchend.slider mouseup.slider",b=f?"pointerup":m?"MSPointerUp.slider":w,y="moveSelection",k="keydown.slider",x="click.slider",C="mouseover.slider",T="focus.slider",S="blur.slider",I=".k-draghandle",D=".k-slider-track",F=".k-tick",E="k-state-selected",O="k-state-focused",A="k-state-default",M="k-state-disabled",H="disabled",P="undefined",z=i.getTouches,V=n.extend({init:function(t,e){if(n.fn.init.call(this,t,e),e=this.options,this._isHorizontal="horizontal"==e.orientation,this._isRtl=this._isHorizontal&&i.support.isRtl(t),this._position=this._isHorizontal?"left":"bottom",this._sizeFn=this._isHorizontal?"width":"height",this._outerSize=this._isHorizontal?r:a,e.tooltip.format=e.tooltip.enabled?e.tooltip.format||"{0}":"{0}",e.smallStep<=0)throw new Error("Kendo UI Slider smallStep must be a positive number.");this._createHtml(),this.wrapper=this.element.closest(".k-slider"),this._trackDiv=this.wrapper.find(D),this._setTrackDivWidth(),this._maxSelection=this._trackDiv[this._sizeFn](),this._sliderItemsInit(),this._reset(),this._tabindex(this.wrapper.find(I)),this[e.enabled?"enable":"disable"]();var s=i.support.isRtl(this.wrapper)?-1:1;this._keyMap={37:B(-1*s*e.smallStep),40:B(-e.smallStep),39:B(1*s*e.smallStep),38:B(+e.smallStep),35:L(e.max),36:L(e.min),33:B(+e.largeStep),34:B(-e.largeStep)},i.notify(this)},events:["change","slide"],options:{enabled:!0,min:0,max:10,smallStep:1,largeStep:5,orientation:"horizontal",tickPlacement:"both",tooltip:{enabled:!0,format:"{0}"}},_distance:function(){return W(this.options.max-this.options.min)},_resize:function(){this._setTrackDivWidth(),this.wrapper.find(".k-slider-items").remove(),this._maxSelection=this._trackDiv[this._sizeFn](),this._sliderItemsInit(),this._refresh(),this.options.enabled&&this.enable(!0)},_sliderItemsInit:function(){var e=this.options,i=this._maxSelection/((e.max-e.min)/e.smallStep),n=this._calculateItemsWidth(d.floor(this._distance()/e.smallStep));"none"!=e.tickPlacement&&i>=2&&(t(this.element).parent().find(".k-slider-items").remove(),this._trackDiv.before(function(t,e){var i,n="<ul class='k-reset k-slider-items'>",s=d.floor(W(e/t.smallStep))+1;for(i=0;i<s;i++)n+="<li class='k-tick' role='presentation'>&nbsp;</li>";return n+="</ul>"}(e,this._distance())),this._setItemsWidth(n),this._setItemsTitle()),this._calculateSteps(n),"none"!=e.tickPlacement&&i>=2&&e.largeStep>=e.smallStep&&this._setItemsLargeTick()},getSize:function(){return i.dimensions(this.wrapper)},_setTrackDivWidth:function(){var t=2*parseFloat(this._trackDiv.css(this._isRtl?"right":this._position),10);this._trackDiv[this._sizeFn](this.wrapper[this._sizeFn]()-2-t)},_setItemsWidth:function(e){var i,n=this.options,s=e.length-1,r=this.wrapper.find(F),a=0,o=r.length,l=0;for(i=0;i<o-2;i++)t(r[i+1])[this._sizeFn](e[i]);if(this._isHorizontal?(t(r[0]).addClass("k-first")[this._sizeFn](e[s-1]),t(r[s]).addClass("k-last")[this._sizeFn](e[s])):(t(r[s]).addClass("k-first")[this._sizeFn](e[s]),t(r[0]).addClass("k-last")[this._sizeFn](e[s-1])),this._distance()%n.smallStep!=0&&!this._isHorizontal){for(i=0;i<e.length;i++)l+=e[i];a=this._maxSelection-l,a+=parseFloat(this._trackDiv.css(this._position),10)+2,this.wrapper.find(".k-slider-items").css("padding-top",a)}},_setItemsTitle:function(){for(var e=this.options,i=this.wrapper.find(F),n=e.min,s=i.length,r=this._isHorizontal&&!this._isRtl?0:s-1,a=this._isHorizontal&&!this._isRtl?s:-1,o=this._isHorizontal&&!this._isRtl?1:-1;r-a!=0;r+=o)t(i[r]).attr("title",l(e.tooltip.format,W(n))),n+=e.smallStep},_setItemsLargeTick:function(){var e,i=this.options,n=this.wrapper.find(F),s=0;if(j(i.largeStep)%j(i.smallStep)==0||this._distance()/i.largeStep>=3)for(this._isHorizontal||this._isRtl||(n=t.makeArray(n).reverse()),s=0;s<n.length;s++){e=t(n[s]);var r=W(j(this._values[s]-this.options.min));r%j(i.smallStep)==0&&r%j(i.largeStep)==0&&(e.addClass("k-tick-large").html("<span class='k-label'>"+e.attr("title")+"</span>"),0!==s&&s!==n.length-1&&e.css("line-height",e[this._sizeFn]()+"px"))}},_calculateItemsWidth:function(t){var e,i,n,s=this.options,r=parseFloat(this._trackDiv.css(this._sizeFn))+1,a=this._distance(),o=r/a;for(a/s.smallStep-d.floor(a/s.smallStep)>0&&(r-=a%s.smallStep*o),e=r/t,i=[],n=0;n<t-1;n++)i[n]=e;return i[t-1]=i[t]=e/2,this._roundWidths(i)},_roundWidths:function(t){var e,i=0,n=t.length;for(e=0;e<n;e++)i+=t[e]-d.floor(t[e]),t[e]=d.floor(t[e]);return i=d.round(i),this._addAdditionalSize(i,t)},_addAdditionalSize:function(t,e){if(0===t)return e;var i,n=parseFloat(e.length-1)/parseFloat(1==t?t:t-1);for(i=0;i<t;i++)e[parseInt(d.round(n*i),10)]+=1;return e},_calculateSteps:function(t){var e,i=this.options,n=i.min,s=0,r=this._distance(),a=d.ceil(r/i.smallStep),o=1;if(a+=r/i.smallStep%1==0?1:0,t.splice(0,0,2*t[a-2]),t.splice(a-1,1,2*t.pop()),this._pixelSteps=[s],this._values=[n],0!==a){for(;o<a;)s+=(t[o-1]+t[o])/2,this._pixelSteps[o]=s,n+=i.smallStep,this._values[o]=W(n),o++;e=r%i.smallStep==0?a-1:a,this._pixelSteps[e]=this._maxSelection,this._values[e]=i.max,this._isRtl&&(this._pixelSteps.reverse(),this._values.reverse())}},_getValueFromPosition:function(t,e){var i,n=this.options,s=d.max(n.smallStep*(this._maxSelection/this._distance()),0),r=0,a=s/2;if(this._isHorizontal?(r=t-e.startPoint,this._isRtl&&(r=this._maxSelection-r)):r=e.startPoint-t,this._maxSelection-(parseInt(this._maxSelection%s,10)-3)/2<r)return n.max;for(i=0;i<this._pixelSteps.length;i++)if(d.abs(this._pixelSteps[i]-r)-1<=a)return W(this._values[i])},_getFormattedValue:function(t,e){var n,s,r,a="",o=this.options.tooltip;return c(t)?(s=t[0],r=t[1]):e&&e.type&&(s=e.selectionStart,r=e.selectionEnd),e&&(n=e.tooltipTemplate),!n&&o.template&&(n=i.template(o.template)),c(t)||e&&e.type?a=n?n({selectionStart:s,selectionEnd:r}):(s=l(o.format,s))+" - "+(r=l(o.format,r)):(e&&(e.val=t),a=n?n({value:t}):l(o.format,t)),a},_getDraggableArea:function(){var t=i.getOffset(this._trackDiv);return{startPoint:this._isHorizontal?t.left:t.top+this._maxSelection,endPoint:this._isHorizontal?t.left+this._maxSelection:t.top}},_createHtml:function(){var t,e,i,n,s,r,a,o,l,h=this.element,u=this.options,c=h.find("input");2==c.length?(c.eq(0).prop("value",N(u.selectionStart)),c.eq(1).prop("value",N(u.selectionEnd))):h.prop("value",N(u.value)),h.wrap((i=u,n=h,s=this._isHorizontal,r=s?" k-slider-horizontal":" k-slider-vertical",a=i.style?i.style:n.attr("style"),o=n.attr("class")?" "+n.attr("class"):"",l="","bottomRight"==i.tickPlacement?l=" k-slider-bottomright":"topLeft"==i.tickPlacement&&(l=" k-slider-topleft"),"<div class='k-widget k-slider"+r+o+"'"+(a=a?" style='"+a+"'":"")+"><div class='k-slider-wrap"+(i.showButtons?" k-slider-buttons":"")+l+"'></div></div>")).hide(),u.showButtons&&h.before(R(u,"increase",this._isHorizontal,this._isRtl)).before(R(u,"decrease",this._isHorizontal,this._isRtl)),h.before((t=u,"<div class='k-slider-track'><div class='k-slider-selection'>\x3c!-- --\x3e</div><a href='#' class='k-draghandle' title='"+(2==(e=h.is("input")?1:2)?t.leftDragHandleTitle:t.dragHandleTitle)+"' role='slider' aria-valuemin='"+t.min+"' aria-valuemax='"+t.max+"' aria-valuenow='"+(e>1?t.selectionStart||t.min:t.value||t.min)+"'>Drag</a>"+(e>1?"<a href='#' class='k-draghandle' title='"+t.rightDragHandleTitle+"'role='slider' aria-valuemin='"+t.min+"' aria-valuemax='"+t.max+"' aria-valuenow='"+(t.selectionEnd||t.max)+"'>Drag</a>":"")+"</div>"))},_focus:function(e){var i=e.target,n=this.value(),s=this._drag;s||(i==this.wrapper.find(I).eq(0)[0]?(s=this._firstHandleDrag,this._activeHandle=0):(s=this._lastHandleDrag,this._activeHandle=1),n=n[this._activeHandle]),t(i).addClass(O+" "+E),s&&(this._activeHandleDrag=s,s.selectionStart=this.options.selectionStart,s.selectionEnd=this.options.selectionEnd,s._updateTooltip(n))},_focusWithMouse:function(e){var i=this,n=(e=t(e)).is(I)?e.index():0;window.setTimeout(function(){i.wrapper.find(I)[2==n?1:0].focus()},1),i._setTooltipTimeout()},_blur:function(e){var i=this._activeHandleDrag;t(e.target).removeClass(O+" "+E),i&&(i._removeTooltip(),delete this._activeHandleDrag,delete this._activeHandle)},_setTooltipTimeout:function(){var t=this;t._tooltipTimeout=window.setTimeout(function(){var e=t._drag||t._activeHandleDrag;e&&e._removeTooltip()},300)},_clearTooltipTimeout:function(){window.clearTimeout(this._tooltipTimeout);var t=this._drag||this._activeHandleDrag;t&&t.tooltipDiv&&t.tooltipDiv.stop(!0,!1).css("opacity",1)},_reset:function(){var e=this.element,i=e.attr("form"),n=i?t("#"+i):e.closest("form");n[0]&&(this._form=n.on("reset",u(this._formResetHandler,this)))},min:function(t){if(!t)return this.options.min;this.setOptions({min:t})},max:function(t){if(!t)return this.options.max;this.setOptions({max:t})},setOptions:function(t){n.fn.setOptions.call(this,t),this._sliderItemsInit(),this._refresh()},destroy:function(){this._form&&this._form.off("reset",this._formResetHandler),n.fn.destroy.call(this)}});function R(t,e,i,n){var s="";return s=i?!n&&"increase"==e||n&&"increase"!=e?"k-i-arrow-60-right":"k-i-arrow-60-left":"increase"==e?"k-i-arrow-60-up":"k-i-arrow-60-down","<a class='k-button k-button-"+e+"' title='"+t[e+"ButtonTitle"]+"' aria-label='"+t[e+"ButtonTitle"]+"'><span class='k-icon "+s+"'></span></a>"}function B(t){return function(e){return e+t}}function L(t){return function(){return t}}function N(t){return(t+"").replace(".",i.cultures.current.numberFormat["."])}function W(t){var e,i,n,s;return t=parseFloat(t,10),n=t.toString(),s=0,(n=n.split("."))[1]&&(s=n[1].length),e=s=s>10?10:s,i=d.pow(10,e||0),d.round(t*i)/i}function U(t,i){var n=h(t.getAttribute(i));return null===n&&(n=e),n}function q(t){return typeof t!==P}function j(t){return 1e4*t}var G=V.extend({init:function(i,n){var s;i.type="text",n=o({},{value:U(i,"value"),min:U(i,"min"),max:U(i,"max"),smallStep:U(i,"step")},n),i=t(i),n&&n.enabled===e&&(n.enabled=!i.is("[disabled]")),V.fn.init.call(this,i,n),q((n=this.options).value)&&null!==n.value||(n.value=n.min,i.prop("value",N(n.min))),n.value=d.max(d.min(n.value,n.max),n.min),s=this.wrapper.find(I),this._selection=new G.Selection(s,this,n),this._drag=new G.Drag(s,"",this,n)},options:{name:"Slider",showButtons:!0,increaseButtonTitle:"Increase",decreaseButtonTitle:"Decrease",dragHandleTitle:"drag",tooltip:{format:"{0:#,#.##}"},value:null},enable:function(e){var n,s,r=this,a=r.options;if(r.disable(),!1!==e){if(r.wrapper.removeClass(M).addClass(A),r.wrapper.find("input").removeAttr(H),n=function(e){var i=z(e)[0];if(i){var n=r._isHorizontal?i.location.pageX:i.location.pageY,s=r._getDraggableArea(),a=t(e.target);a.hasClass("k-draghandle")?a.addClass(O+" "+E):(r._update(r._getValueFromPosition(n,s)),r._focusWithMouse(e.target),r._drag.dragstart(e),e.preventDefault())}},r.wrapper.find(".k-tick, "+D).on(v,n).end().on(v,function(){t(document.documentElement).one("selectstart",i.preventDefault)}).on(b,function(){r._drag._end()}),r.wrapper.find(I).attr("tabindex",0).on(w,function(){r._setTooltipTimeout()}).on(x,function(t){r._focusWithMouse(t.target),t.preventDefault()}).on(T,u(r._focus,r)).on(S,u(r._blur,r)),s=u(function(t){var e=r._nextValueByIndex(r._valueIndex+1*t);r._setValueInRange(e),r._drag._updateTooltip(e)},r),a.showButtons){var o=u(function(t,e){this._clearTooltipTimeout(),(1===t.which||p.touch&&0===t.which)&&(s(e),this.timeout=setTimeout(u(function(){this.timer=setInterval(function(){s(e)},60)},this),200))},r);r.wrapper.find(".k-button").on(w,u(function(t){this._clearTimer(),r._focusWithMouse(t.target)},r)).on(C,function(e){t(e.currentTarget).addClass("k-state-hover")}).on("mouseout.slider",u(function(e){t(e.currentTarget).removeClass("k-state-hover"),this._clearTimer()},r)).eq(0).on(_,u(function(t){o(t,1)},r)).click(!1).end().eq(1).on(_,u(function(t){o(t,-1)},r)).click(i.preventDefault)}r.wrapper.find(I).off(k,!1).on(k,u(this._keydown,r)),a.enabled=!0}},disable:function(){this.wrapper.removeClass(A).addClass(M),t(this.element).prop(H,H),this.wrapper.find(".k-button").off(_).on(_,function(e){e.preventDefault(),t(this).addClass("k-state-active")}).off(w).on(w,function(e){e.preventDefault(),t(this).removeClass("k-state-active")}).off("mouseleave.slider").on("mouseleave.slider",i.preventDefault).off(C).on(C,i.preventDefault),this.wrapper.find(".k-tick, "+D).off(v).off(b),this.wrapper.find(I).attr("tabindex",-1).off(w).off(k).off(x).off(T).off(S),this.options.enabled=!1},_update:function(t){var e=this.value()!=t;this.value(t),e&&this.trigger("change",{value:this.options.value})},value:function(t){var e=this.options;if(t=W(t),isNaN(t))return e.value;t>=e.min&&t<=e.max&&e.value!=t&&(this.element.prop("value",N(t)),e.value=t,this._refreshAriaAttr(t),this._refresh())},_refresh:function(){this.trigger(y,{value:this.options.value})},_refreshAriaAttr:function(t){var e,i=this._drag;e=i&&i._tooltipDiv?i._tooltipDiv.text():this._getFormattedValue(t,null),this.wrapper.find(I).attr("aria-valuenow",t).attr("aria-valuetext",e)},_clearTimer:function(){clearTimeout(this.timeout),clearInterval(this.timer)},_keydown:function(t){t.keyCode in this._keyMap&&(this._clearTooltipTimeout(),this._setValueInRange(this._keyMap[t.keyCode](this.options.value)),this._drag._updateTooltip(this.value()),t.preventDefault())},_setValueInRange:function(t){var e=this.options;t=W(t),isNaN(t)?this._update(e.min):(t=d.max(d.min(t,e.max),e.min),this._update(t))},_nextValueByIndex:function(t){var e=this._values.length;return this._isRtl&&(t=e-1-t),this._values[d.max(0,d.min(t,e-1))]},_formResetHandler:function(){var t=this,e=t.options.min;setTimeout(function(){var i=t.element[0].value;t.value(""===i||isNaN(i)?e:i)})},destroy:function(){V.fn.destroy.call(this),this.wrapper.off(g).find(".k-button").off(g).end().find(I).off(g).end().find(".k-tick, "+D).off(g).end(),this._drag.draggable.destroy(),this._drag._removeTooltip(!0)}});G.Selection=function(t,e,i){function n(n){var s=n-i.min,r=e._valueIndex=d.ceil(W(s/i.smallStep)),a=parseInt(e._pixelSteps[r],10),o=e._trackDiv.find(".k-slider-selection"),l=parseInt(e._outerSize(t)/2,10),h=e._isRtl?2:0;o[e._sizeFn](e._isRtl?e._maxSelection-a:a),t.css(e._position,a-l-h)}n(i.value),e.bind(["slide",y],function(t){n(parseFloat(t.value,10))}),e.bind("change",function(t){n(parseFloat(t.sender.value(),10))})},G.Drag=function(t,e,i,n){this.owner=i,this.options=n,this.element=t,this.type=e,this.draggable=new s(t,{distance:0,dragstart:u(this._dragstart,this),drag:u(this.drag,this),dragend:u(this.dragend,this),dragcancel:u(this.dragcancel,this)}),t.click(!1),t.on("dragstart",function(t){t.preventDefault()})},G.Drag.prototype={dragstart:function(t){this.owner._activeDragHandle=this,this.draggable.userEvents.cancel(),this._dragstart(t),this.dragend()},_dragstart:function(e){var i=this.owner,n=this.options;n.enabled?(this.owner._activeDragHandle=this,i.element.off(C),i.wrapper.find("."+O).removeClass(O+" "+E),this.element.addClass(O+" "+E),t(document.documentElement).css("cursor","pointer"),this.dragableArea=i._getDraggableArea(),this.step=d.max(n.smallStep*(i._maxSelection/i._distance()),0),this.type?(this.selectionStart=n.selectionStart,this.selectionEnd=n.selectionEnd,i._setZIndex(this.type)):this.oldVal=this.val=n.value,this._removeTooltip(!0),this._createTooltip()):e.preventDefault()},_createTooltip:function(){var e,n=this.owner,s=this.options.tooltip,r="",a=t(window);s.enabled&&(s.template&&(this.tooltipTemplate=i.template(s.template)),t(".k-slider-tooltip").remove(),this.tooltipDiv=t("<div class='k-widget k-tooltip k-slider-tooltip'>\x3c!-- --\x3e</div>").appendTo(document.body),r=n._getFormattedValue(this.val||n.value(),this),this.type||(e="k-callout-"+(n._isHorizontal?"s":"e"),this.tooltipInnerDiv="<div class='k-callout "+e+"'>\x3c!-- --\x3e</div>",r+=this.tooltipInnerDiv),this.tooltipDiv.html(r),this._scrollOffset={top:a.scrollTop(),left:a.scrollLeft()},this.moveTooltip())},drag:function(t){var e,i=this.owner,n=t.x.location,s=t.y.location,r=this.dragableArea.startPoint,a=this.dragableArea.endPoint;t.preventDefault(),i._isHorizontal?i._isRtl?this.val=this.constrainValue(n,r,a,n<a):this.val=this.constrainValue(n,r,a,n>=a):this.val=this.constrainValue(s,a,r,s<=a),this.oldVal!=this.val&&(this.oldVal=this.val,this.type?("firstHandle"==this.type?this.val<this.selectionEnd?this.selectionStart=this.val:this.selectionStart=this.selectionEnd=this.val:this.val>this.selectionStart?this.selectionEnd=this.val:this.selectionStart=this.selectionEnd=this.val,e={values:[this.selectionStart,this.selectionEnd],value:[this.selectionStart,this.selectionEnd]}):e={value:this.val},i.trigger("slide",e)),this._updateTooltip(this.val)},_updateTooltip:function(t){var e="";this.options.tooltip.enabled&&(this.tooltipDiv||this._createTooltip(),e=this.owner._getFormattedValue(W(t),this),this.type||(e+=this.tooltipInnerDiv),this.tooltipDiv.html(e),this.moveTooltip())},dragcancel:function(){return this.owner._refresh(),t(document.documentElement).css("cursor",""),this._end()},dragend:function(){var e=this.owner;return t(document.documentElement).css("cursor",""),this.type?e._update(this.selectionStart,this.selectionEnd):(e._update(this.val),this.draggable.userEvents._disposeAll()),this.draggable.userEvents.cancel(),this._end()},_end:function(){var t=this.owner;return t._focusWithMouse(this.element),t.element.on(C),!1},_removeTooltip:function(e){var i=this,n=i.owner;i.tooltipDiv&&n.options.tooltip.enabled&&n.options.enabled&&(e?(i.tooltipDiv.remove(),i.tooltipDiv=null):i.tooltipDiv.fadeOut("slow",function(){t(this).remove(),i.tooltipDiv=null}))},moveTooltip:function(){var e,n,s,o,l=this.owner,h=0,u=0,c=this.element,d=i.getOffset(c),p=t(window),f=this.tooltipDiv.find(".k-callout"),m=r(this.tooltipDiv),g=a(this.tooltipDiv);this.type?(e=l.wrapper.find(I),d=i.getOffset(e.eq(0)),n=i.getOffset(e.eq(1)),l._isHorizontal?(h=n.top,u=d.left+(n.left-d.left)/2):(h=d.top+(n.top-d.top)/2,u=n.left),o=r(e.eq(0))+16):(h=d.top,u=d.left,o=r(c)+16),l._isHorizontal?(u-=parseInt((m-l._outerSize(c))/2,10),h-=g+8+(f.length?f.height():0)):(h-=parseInt((g-l._outerSize(c))/2,10),u-=m+8+(f.length?f.width():0)),l._isHorizontal?(h+=s=this._flip(h,g,o,a(p)+this._scrollOffset.top),u+=this._fit(u,m,r(p)+this._scrollOffset.left)):(s=this._flip(u,m,o,r(p)+this._scrollOffset.left),h+=this._fit(h,g,a(p)+this._scrollOffset.top),u+=s),s>0&&f&&(f.removeClass(),f.addClass("k-callout k-callout-"+(l._isHorizontal?"n":"w"))),this.tooltipDiv.css({top:h,left:u})},_fit:function(t,e,i){var n=0;return t+e>i&&(n=i-(t+e)),t<0&&(n=-t),n},_flip:function(t,e,i,n){var s=0;return t+e>n&&(s+=-(i+e)),t+s<0&&(s+=i+e),s},constrainValue:function(t,e,i,n){return e<t&&t<i?this.owner._getValueFromPosition(t,this.dragableArea):n?this.options.max:this.options.min}},i.ui.plugin(G);var Y=V.extend({init:function(i,n){var s=t(i).find("input"),r=s.eq(0)[0],a=s.eq(1)[0];r.type="text",a.type="text",n&&n.showButtons&&(window.console&&window.console.warn("showbuttons option is not supported for the range slider, ignoring"),n.showButtons=!1),(n=o({},{selectionStart:U(r,"value"),min:U(r,"min"),max:U(r,"max"),smallStep:U(r,"step")},{selectionEnd:U(a,"value"),min:U(a,"min"),max:U(a,"max"),smallStep:U(a,"step")},n))&&n.enabled===e&&(n.enabled=!s.is("[disabled]")),V.fn.init.call(this,i,n),q((n=this.options).selectionStart)&&null!==n.selectionStart||(n.selectionStart=n.min,s.eq(0).prop("value",N(n.min))),q(n.selectionEnd)&&null!==n.selectionEnd||(n.selectionEnd=n.max,s.eq(1).prop("value",N(n.max)));var l=this.wrapper.find(I);this._selection=new Y.Selection(l,this,n),this._firstHandleDrag=new G.Drag(l.eq(0),"firstHandle",this,n),this._lastHandleDrag=new G.Drag(l.eq(1),"lastHandle",this,n)},options:{name:"RangeSlider",leftDragHandleTitle:"drag",rightDragHandleTitle:"drag",tooltip:{format:"{0:#,#.##}"},selectionStart:null,selectionEnd:null},enable:function(e){var n,s=this,r=s.options;s.disable(),!1!==e&&(s.wrapper.removeClass(M).addClass(A),s.wrapper.find("input").removeAttr(H),n=function(e){var i=z(e)[0];if(i){var n,a,o,l=s._isHorizontal?i.location.pageX:i.location.pageY,h=s._getDraggableArea(),u=s._getValueFromPosition(l,h),c=t(e.target);if(c.hasClass("k-draghandle"))return s.wrapper.find("."+O).removeClass(O+" "+E),void c.addClass(O+" "+E);u<r.selectionStart?(n=u,a=r.selectionEnd,o=s._firstHandleDrag):u>s.selectionEnd?(n=r.selectionStart,a=u,o=s._lastHandleDrag):u-r.selectionStart<=r.selectionEnd-u?(n=u,a=r.selectionEnd,o=s._firstHandleDrag):(n=r.selectionStart,a=u,o=s._lastHandleDrag),o.dragstart(e),s._setValueInRange(n,a),s._focusWithMouse(o.element)}},s.wrapper.find(".k-tick, "+D).on(v,n).end().on(v,function(){t(document.documentElement).one("selectstart",i.preventDefault)}).on(b,function(){s._activeDragHandle&&s._activeDragHandle._end()}),s.wrapper.find(I).attr("tabindex",0).on(w,function(){s._setTooltipTimeout()}).on(x,function(t){s._focusWithMouse(t.target),t.preventDefault()}).on(T,u(s._focus,s)).on(S,u(s._blur,s)),s.wrapper.find(I).off(k,i.preventDefault).eq(0).on(k,u(function(t){this._keydown(t,"firstHandle")},s)).end().eq(1).on(k,u(function(t){this._keydown(t,"lastHandle")},s)),s.options.enabled=!0)},disable:function(){this.wrapper.removeClass(A).addClass(M),this.wrapper.find("input").prop(H,H),this.wrapper.find(".k-tick, "+D).off(v).off(b),this.wrapper.find(I).attr("tabindex",-1).off(w).off(k).off(x).off(T).off(S),this.options.enabled=!1},_keydown:function(t,e){var i,n,s,r=this.options.selectionStart,a=this.options.selectionEnd;t.keyCode in this._keyMap&&(this._clearTooltipTimeout(),"firstHandle"==e?(s=this._activeHandleDrag=this._firstHandleDrag,(r=this._keyMap[t.keyCode](r))>a&&(a=r)):(s=this._activeHandleDrag=this._lastHandleDrag,r>(a=this._keyMap[t.keyCode](a))&&(r=a)),this._setValueInRange(W(r),W(a)),i=Math.max(r,this.options.selectionStart),n=Math.min(a,this.options.selectionEnd),s.selectionEnd=Math.max(n,this.options.selectionStart),s.selectionStart=Math.min(i,this.options.selectionEnd),s._updateTooltip(this.value()[this._activeHandle]),t.preventDefault())},_update:function(t,e){var i=this.value(),n=i[0]!=t||i[1]!=e;this.value([t,e]),n&&this.trigger("change",{values:[t,e],value:[t,e]})},value:function(t){return t&&t.length?this._value(t[0],t[1]):this._value()},_value:function(t,e){var i=this.options,n=i.selectionStart,s=i.selectionEnd;if(isNaN(t)&&isNaN(e))return[n,s];t=W(t),e=W(e),t>=i.min&&t<=i.max&&e>=i.min&&e<=i.max&&t<=e&&(n==t&&s==e||(this.element.find("input").eq(0).prop("value",N(t)).end().eq(1).prop("value",N(e)),i.selectionStart=t,i.selectionEnd=e,this._refresh(),this._refreshAriaAttr(t,e)))},values:function(t,e){return c(t)?this._value(t[0],t[1]):this._value(t,e)},_refresh:function(){var t=this.options;this.trigger(y,{values:[t.selectionStart,t.selectionEnd],value:[t.selectionStart,t.selectionEnd]}),t.selectionStart==t.max&&t.selectionEnd==t.max&&this._setZIndex("firstHandle")},_refreshAriaAttr:function(t,e){var i,n=this.wrapper.find(I),s=this._activeHandleDrag;i=this._getFormattedValue([t,e],s),n.eq(0).attr("aria-valuenow",t),n.eq(1).attr("aria-valuenow",e),n.attr("aria-valuetext",i)},_setValueInRange:function(t,e){var i=this.options;t=d.max(d.min(t,i.max),i.min),e=d.max(d.min(e,i.max),i.min),t==i.max&&e==i.max&&this._setZIndex("firstHandle"),this._update(d.min(t,e),d.max(t,e))},_setZIndex:function(e){this.wrapper.find(I).each(function(i){t(this).css("z-index","firstHandle"==e?1-i:i)})},_formResetHandler:function(){var t=this,e=t.options;setTimeout(function(){var i=t.element.find("input"),n=i[0].value,s=i[1].value;t.values(""===n||isNaN(n)?e.min:n,""===s||isNaN(s)?e.max:s)})},destroy:function(){V.fn.destroy.call(this),this.wrapper.off(g).find(".k-tick, "+D).off(g).end().find(I).off(g),this._firstHandleDrag.draggable.destroy(),this._lastHandleDrag.draggable.destroy()}});Y.Selection=function(t,e,i){function n(n){var s,r,a,o,l,h=(n=n||[])[0]-i.min,u=n[1]-i.min,c=d.ceil(W(h/i.smallStep)),p=d.ceil(W(u/i.smallStep)),f=e._pixelSteps[c],m=e._pixelSteps[p],g=parseInt(e._outerSize(t.eq(0))/2,10),_=e._isRtl?2:0;t.eq(0).css(e._position,f-g-_).end().eq(1).css(e._position,m-g-_),s=f,r=m,l=e._trackDiv.find(".k-slider-selection"),a=d.abs(s-r),l[e._sizeFn](a),e._isRtl?(o=d.max(s,r),l.css("right",e._maxSelection-o-1)):(o=d.min(s,r),l.css(e._position,o-1))}n(e.value()),e.bind(["change","slide",y],function(t){n(t.values)})},i.ui.plugin(Y)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.mobile.scroller",["kendo.fx","kendo.draganddrop"],function(){return t=window.kendo.jQuery,e=window.kendo,i=e.mobile,n=e.effects,s=i.ui,r=t.proxy,a=t.extend,o=s.Widget,l=e.Class,h=e.ui.Movable,u=e.ui.Pane,c=e.ui.PaneDimensions,d=n.Transition,p=n.Animation,f=Math.abs,m="km-scroller-release",g="km-scroller-refresh",_=p.extend({init:function(t){p.fn.init.call(this),a(this,t),this.userEvents.bind("gestureend",r(this.start,this)),this.tapCapture.bind("press",r(this.cancel,this))},enabled:function(){return this.movable.scale<this.dimensions.minScale},done:function(){return this.dimensions.minScale-this.movable.scale<.01},tick:function(){var t=this.movable;t.scaleWith(1.1),this.dimensions.rescale(t.scale)},onEnd:function(){var t=this.movable;t.scaleTo(this.dimensions.minScale),this.dimensions.rescale(t.scale)}}),v=p.extend({init:function(t){var e=this;p.fn.init.call(e),a(e,t,{transition:new d({axis:t.axis,movable:t.movable,onEnd:function(){e._end()}})}),e.tapCapture.bind("press",function(){e.cancel()}),e.userEvents.bind("end",r(e.start,e)),e.userEvents.bind("gestureend",r(e.start,e)),e.userEvents.bind("tap",r(e.onEnd,e))},onCancel:function(){this.transition.cancel()},freeze:function(t){this.cancel(),this._moveTo(t)},onEnd:function(){this.paneAxis.outOfBounds()?this._snapBack():this._end()},done:function(){return f(this.velocity)<1},start:function(t){var e;this.dimension.enabled&&(this.paneAxis.outOfBounds()?this._snapBack():(e=2===t.touch.id?0:t.touch[this.axis].velocity,this.velocity=Math.max(Math.min(e*this.velocityMultiplier,55),-55),this.tapCapture.captureNext(),p.fn.start.call(this)))},tick:function(){var t=this.dimension,e=this.paneAxis.outOfBounds()?.5:this.friction,i=this.velocity*=e,n=this.movable[this.axis]+i;!this.elastic&&t.outOfBounds(n)&&(n=Math.max(Math.min(n,t.max),t.min),this.velocity=0),this.movable.moveAxis(this.axis,n)},_end:function(){this.tapCapture.cancelCapture(),this.end()},_snapBack:function(){var t=this.dimension,e=this.movable[this.axis]>t.max?t.max:t.min;this._moveTo(e)},_moveTo:function(t){this.transition.moveTo({location:t,duration:500,ease:d.easeOutExpo})}}),w=p.extend({init:function(t){e.effects.Animation.fn.init.call(this),a(this,t,{origin:{},destination:{},offset:{}})},tick:function(){this._updateCoordinates(),this.moveTo(this.origin)},done:function(){return f(this.offset.y)<5&&f(this.offset.x)<5},onEnd:function(){this.moveTo(this.destination),this.callback&&this.callback.call()},setCoordinates:function(t,e){this.offset={},this.origin=t,this.destination=e},setCallback:function(t){t&&e.isFunction(t)?this.callback=t:t=void 0},_updateCoordinates:function(){this.offset={x:(this.destination.x-this.origin.x)/4,y:(this.destination.y-this.origin.y)/4},this.origin={y:this.origin.y+this.offset.y,x:this.origin.x+this.offset.x}}}),b=l.extend({init:function(e){var i="x"===e.axis,n=t('<div class="km-touch-scrollbar km-'+(i?"horizontal":"vertical")+'-scrollbar" />');a(this,e,{element:n,elementSize:0,movable:new h(n),scrollMovable:e.movable,alwaysVisible:e.alwaysVisible,size:i?"width":"height"}),this.scrollMovable.bind("change",r(this.refresh,this)),this.container.append(n),e.alwaysVisible&&this.show()},refresh:function(){var t=this.axis,e=this.dimension,i=e.size,n=this.scrollMovable,s=i/e.total,r=Math.round(-n[t]*s),a=Math.round(i*s);s>=1?this.element.css("display","none"):this.element.css("display",""),r+a>i?a=i-r:r<0&&(a+=r,r=0),this.elementSize!=a&&(this.element.css(this.size,a+"px"),this.elementSize=a),this.movable.moveAxis(t,r)},show:function(){this.element.css({opacity:.7,visibility:"visible"})},hide:function(){this.alwaysVisible||this.element.css({opacity:0})}}),y=o.extend({init:function(i,n){var s=this;if(o.fn.init.call(s,i,n),i=s.element,s._native=s.options.useNative&&e.support.hasNativeScrolling,s._native)return i.addClass("km-native-scroller").prepend('<div class="km-scroll-header"/>'),void a(s,{scrollElement:i,fixedContainer:i.children().first()});i.css("overflow","hidden").addClass("km-scroll-wrapper").wrapInner('<div class="km-scroll-container"/>').prepend('<div class="km-scroll-header"/>');var l=i.children().eq(1),d=new e.TapCapture(i),p=new h(l),m=new c({element:l,container:i,forcedEnabled:s.options.zoom}),g=this.options.avoidScrolling,v=new e.UserEvents(i,{touchAction:"pan-y",fastTap:!0,allowSelection:!0,preventDragEvent:!0,captureUpIfMoved:!0,multiTouch:s.options.zoom,start:function(e){m.refresh();var i=f(e.x.velocity),n=f(e.y.velocity),r=2*i>=n,a=2*n>=i;!t.contains(s.fixedContainer[0],e.event.target)&&!g(e)&&s.enabled&&(m.x.enabled&&r||m.y.enabled&&a)?v.capture():v.cancel()}}),b=new u({movable:p,dimensions:m,userEvents:v,elastic:s.options.elastic}),y=new _({movable:p,dimensions:m,userEvents:v,tapCapture:d}),k=new w({moveTo:function(t){s.scrollTo(t.x,t.y)}});p.bind("change",function(){s.scrollTop=-p.y,s.scrollLeft=-p.x,s.trigger("scroll",{scrollTop:s.scrollTop,scrollLeft:s.scrollLeft})}),s.options.mousewheelScrolling&&i.on("DOMMouseScroll mousewheel",r(this,"_wheelScroll")),a(s,{movable:p,dimensions:m,zoomSnapBack:y,animatedScroller:k,userEvents:v,pane:b,tapCapture:d,pulled:!1,enabled:!0,scrollElement:l,scrollTop:0,scrollLeft:0,fixedContainer:i.children().first()}),s._initAxis("x"),s._initAxis("y"),s._wheelEnd=function(){s._wheel=!1,s.userEvents.end(0,s._wheelY)},m.refresh(),s.options.pullToRefresh&&s._initPullToRefresh()},_wheelScroll:function(t){this._wheel||(this._wheel=!0,this._wheelY=0,this.userEvents.press(0,this._wheelY)),clearTimeout(this._wheelTimeout),this._wheelTimeout=setTimeout(this._wheelEnd,50);var i=e.wheelDeltaY(t);i&&(this._wheelY+=i,this.userEvents.move(0,this._wheelY)),t.preventDefault()},makeVirtual:function(){this.dimensions.y.makeVirtual()},virtualSize:function(t,e){this.dimensions.y.virtualSize(t,e)},height:function(){return this.dimensions.y.size},scrollHeight:function(){return this.scrollElement[0].scrollHeight},scrollWidth:function(){return this.scrollElement[0].scrollWidth},options:{name:"Scroller",zoom:!1,pullOffset:140,visibleScrollHints:!1,elastic:!0,useNative:!1,mousewheelScrolling:!0,avoidScrolling:function(){return!1},pullToRefresh:!1,messages:{pullTemplate:"Pull to refresh",releaseTemplate:"Release to refresh",refreshTemplate:"Refreshing"}},events:["pull","scroll","resize"],_resize:function(){this._native||this.contentResized()},setOptions:function(t){o.fn.setOptions.call(this,t),t.pullToRefresh&&this._initPullToRefresh()},reset:function(){this._native?this.scrollElement.scrollTop(0):(this.movable.moveTo({x:0,y:0}),this._scale(1))},contentResized:function(){this.dimensions.refresh(),this.pane.x.outOfBounds()&&this.movable.moveAxis("x",this.dimensions.x.min),this.pane.y.outOfBounds()&&this.movable.moveAxis("y",this.dimensions.y.min)},zoomOut:function(){var t=this.dimensions;t.refresh(),this._scale(t.fitScale),this.movable.moveTo(t.centerCoordinates())},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},scrollTo:function(t,e){this._native?(this.scrollElement.scrollLeft(f(t)),this.scrollElement.scrollTop(f(e))):(this.dimensions.refresh(),this.movable.moveTo({x:t,y:e}))},animatedScrollTo:function(t,e,i){var n,s;this._native?this.scrollTo(t,e):(n={x:this.movable.x,y:this.movable.y},s={x:t,y:e},this.animatedScroller.setCoordinates(n,s),this.animatedScroller.setCallback(i),this.animatedScroller.start())},pullHandled:function(){this.refreshHint.removeClass(g),this.hintContainer.html(this.pullTemplate({})),this.yinertia.onEnd(),this.xinertia.onEnd(),this.userEvents.cancel()},destroy:function(){o.fn.destroy.call(this),this.userEvents&&this.userEvents.destroy()},_scale:function(t){this.dimensions.rescale(t),this.movable.scaleTo(t)},_initPullToRefresh:function(){this.dimensions.y.forceEnabled(),this.pullTemplate=e.template(this.options.messages.pullTemplate),this.releaseTemplate=e.template(this.options.messages.releaseTemplate),this.refreshTemplate=e.template(this.options.messages.refreshTemplate),this.scrollElement.prepend('<span class="km-scroller-pull"><span class="km-icon"></span><span class="km-loading-left"></span><span class="km-loading-right"></span><span class="km-template">'+this.pullTemplate({})+"</span></span>"),this.refreshHint=this.scrollElement.children().first(),this.hintContainer=this.refreshHint.children(".km-template"),this.pane.y.bind("change",r(this._paneChange,this)),this.userEvents.bind("end",r(this._dragEnd,this))},_dragEnd:function(){this.pulled&&(this.pulled=!1,this.refreshHint.removeClass(m).addClass(g),this.hintContainer.html(this.refreshTemplate({})),this.yinertia.freeze(this.options.pullOffset/2),this.trigger("pull"))},_paneChange:function(){this.movable.y/.5>this.options.pullOffset?this.pulled||(this.pulled=!0,this.refreshHint.removeClass(g).addClass(m),this.hintContainer.html(this.releaseTemplate({}))):this.pulled&&(this.pulled=!1,this.refreshHint.removeClass(m),this.hintContainer.html(this.pullTemplate({})))},_initAxis:function(t){var e=this,i=e.movable,n=e.dimensions[t],s=e.tapCapture,r=e.pane[t],a=new b({axis:t,movable:i,dimension:n,container:e.element,alwaysVisible:e.options.visibleScrollHints});n.bind("change",function(){a.refresh()}),r.bind("change",function(){a.show()}),e[t+"inertia"]=new v({axis:t,paneAxis:r,movable:i,tapCapture:s,userEvents:e.userEvents,dimension:n,elastic:e.options.elastic,friction:e.options.friction||.96,velocityMultiplier:e.options.velocityMultiplier||10,end:function(){a.hide(),e.trigger("scrollEnd",{axis:t,scrollTop:e.scrollTop,scrollLeft:e.scrollLeft})}})}}),s.plugin(y),window.kendo;var t,e,i,n,s,r,a,o,l,h,u,c,d,p,f,m,g,_,v,w,b,y}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.autocomplete",["kendo.list","kendo.mobile.scroller","kendo.virtuallist"],function(){return function(t,e){var i=window.kendo,n=i.support,s=i.caret,r=i._activeElement,a=n.placeholder,o=i.ui,l=o.List,h=i.keys,u=i.data.DataSource,c="k-state-default",d="k-state-disabled",p=".kendoAutoComplete",f=t.proxy;function m(t,e,i){return i?e.substring(0,t).split(i).length-1:0}var g=l.extend({init:function(e,n){var s,r=this;r.ns=p,n=t.isArray(n)?{dataSource:n}:n,l.fn.init.call(r,e,n),e=r.element,(n=r.options).placeholder=n.placeholder||e.attr("placeholder"),a&&e.attr("placeholder",n.placeholder),r._wrapper(),r._loader(),r._clearButton(),r._dataSource(),r._ignoreCase(),e[0].type="text",s=r.wrapper,r._popup(),e.addClass("k-input").on("keydown"+p,f(r._keydown,r)).on("keypress"+p,f(r._keypress,r)).on("input"+p,f(r._search,r)).on("paste"+p,f(r._search,r)).on("focus"+p,function(){r._prev=r._accessor(),r._oldText=r._prev,r._placeholder(!1),s.addClass("k-state-focused")}).on("focusout"+p,function(){r._change(),r._placeholder(),r.close(),s.removeClass("k-state-focused")}).attr({autocomplete:"off",role:"textbox","aria-haspopup":!0}),r._clear.on("click"+p,f(r._clearValue,r)),r._enable(),r._old=r._accessor(),e[0].id&&e.attr("aria-owns",r.ul[0].id),r._aria(),r._placeholder(),r._initList(),t(r.element).parents("fieldset").is(":disabled")&&r.enable(!1),r.listView.bind("click",function(t){t.preventDefault()}),r._resetFocusItemHandler=t.proxy(r._resetFocusItem,r),i.notify(r),r._toggleCloseVisibility()},options:{name:"AutoComplete",enabled:!0,suggest:!1,template:"",groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",dataTextField:"",minLength:1,enforceMinLength:!1,delay:200,height:200,filter:"startswith",ignoreCase:!0,highlightFirst:!1,separator:null,placeholder:"",animation:{},virtual:!1,value:null,clearButton:!0,autoWidth:!1},_dataSource:function(){this.dataSource&&this._refreshHandler?this._unbindDataSource():(this._progressHandler=f(this._showBusy,this),this._errorHandler=f(this._hideBusy,this)),this.dataSource=u.create(this.options.dataSource).bind("progress",this._progressHandler).bind("error",this._errorHandler)},setDataSource:function(t){this.options.dataSource=t,this._dataSource(),this.listView.setDataSource(this.dataSource)},events:["open","close","change","select","filtering","dataBinding","dataBound"],setOptions:function(t){var e=this._listOptions(t);l.fn.setOptions.call(this,t),this.listView.setOptions(e),this._accessors(),this._aria(),this._clearButton()},_listOptions:function(e){var i=l.fn._listOptions.call(this,t.extend(e,{skipUpdateOnBind:!0}));return i.dataValueField=i.dataTextField,i.selectedItemChange=null,i},_editable:function(t){var e=this.element,i=this.wrapper.off(p),n=t.readonly,s=t.disable;n||s?(i.addClass(s?d:c).removeClass(s?c:d),e.attr("disabled",s).attr("readonly",n).attr("aria-disabled",s).attr("aria-readonly",n)):(i.addClass(c).removeClass(d).on("mouseenter.kendoAutoComplete mouseleave.kendoAutoComplete",this._toggleHover),e.removeAttr("disabled").removeAttr("readonly").attr("aria-disabled",!1).attr("aria-readonly",!1))},close:function(){var t=this.listView.focus();t&&t.removeClass("k-state-selected"),this.popup.close()},destroy:function(){this.element.off(p),this._clear.off(p),this.wrapper.off(p),l.fn.destroy.call(this)},refresh:function(){this.listView.refresh()},select:function(t){this._select(t)},search:function(e){var i,n,r,a,o=this.options,l=o.ignoreCase,h=this._separator();e=e||this._accessor(),clearTimeout(this._typingTimeout),h&&(n=s(this.element)[0],a=h,e=(r=e).split(a)[m(n,r,a)]),i=e.length,(!o.enforceMinLength&&!i||i>=o.minLength)&&(this._open=!0,this._mute(function(){this.listView.value([])}),this._filterSource({value:l?e.toLowerCase():e,operator:o.filter,field:o.dataTextField,ignoreCase:l}),this.one("close",t.proxy(this._unifySeparators,this))),this._toggleCloseVisibility()},suggest:function(t){var e,i=this._last,n=this._accessor(),a=this.element[0],o=s(a)[0],u=this._separator(),c=n.split(u),d=m(o,n,u),p=o;i!=h.BACKSPACE&&i!=h.DELETE?("string"!=typeof(t=t||"")&&(t[0]&&(t=this.dataSource.view()[l.inArray(t[0],this.ul[0])]),t=t?this._text(t):""),o<=0&&(o=n.toLowerCase().indexOf(t.toLowerCase())+1),e=(e=n.substring(0,o).lastIndexOf(u))>-1?o-(e+u.length):o,n=c[d].substring(0,e),t&&((e=(t=t.toString()).toLowerCase().indexOf(n.toLowerCase()))>-1&&(p=o+(t=t.substring(e+n.length)).length,n+=t),u&&""!==c[c.length-1]&&c.push("")),c[d]=n,this._accessor(c.join(u||"")),a===r()&&s(a,o,p)):this._last=void 0},value:function(t){if(void 0===t)return this._accessor();this.listView.value(t),this._accessor(t),this._old=this._accessor(),this._oldText=this._accessor(),this._toggleCloseVisibility()},_click:function(t){var e=t.item,i=this,n=i.element,r=i.listView.dataItemByIndex(i.listView.getElementIndex(e));t.preventDefault(),i._active=!0,i.trigger("select",{dataItem:r,item:e})?i.close():(i._oldText=n.val(),i._select(e).done(function(){i._blur(),s(n,n.val().length)}))},_clearText:t.noop,_resetFocusItem:function(){var t=this.options.highlightFirst?0:-1;this.options.virtual&&this.listView.scrollTo(0),this.listView.focus(t)},_listBound:function(){var t,e=this.popup,i=this.options,n=this.dataSource.flatView(),s=n.length,a=this.dataSource._group.length,o=this.element[0]===r();this._renderFooter(),this._renderNoData(),this._toggleNoData(!s),this._toggleHeader(!!a&&!!s),this._resizePopup(),e.position(),s&&i.suggest&&o&&this.suggest(n[0]),this._open&&(this._open=!1,t=this._allowOpening()?"open":"close",this._typingTimeout&&!o&&(t="close"),s&&(this._resetFocusItem(),i.virtual&&this.popup.unbind("activate",this._resetFocusItemHandler).one("activate",this._resetFocusItemHandler)),e[t](),this._typingTimeout=void 0),this._touchScroller&&this._touchScroller.reset(),this._hideBusy(),this._makeUnselectable(),this.trigger("dataBound")},_mute:function(t){this._muted=!0,t.call(this),this._muted=!1},_listChange:function(){(this._active||this.element[0]===r())&&!this._muted&&this._selectValue(this.listView.selectedDataItems()[0])},_selectValue:function(t){var e,i,n,r,a,o,l=this._separator(),h="";t&&(h=this._text(t)),null===h&&(h=""),l&&(e=s(this.element)[0],i=this._accessor(),n=h,r=l,a=this._defaultSeparator(),(o=i.split(r)).splice(m(e,i,r),1,n),r&&""!==o[o.length-1]&&o.push(""),h=o.join(a)),this._prev=h,this._accessor(h),this._placeholder()},_unifySeparators:function(){return this._accessor(this.value().split(this._separator()).join(this._defaultSeparator())),this},_preselect:function(t,e){this._inputValue(e),this._accessor(t),this._old=this.oldText=this._accessor(),this.listView.setValue(t),this._placeholder()},_change:function(){var t=this._unifySeparators().value(),e=t!==l.unifyType(this._old,typeof t),i=e&&!this._typing,n=this._oldText!==t;this._old=t,this._oldText=t,(i||n)&&this.element.trigger("change"),e&&this.trigger("change"),this.typing=!1,this._toggleCloseVisibility()},_accessor:function(t){var e=this.element[0];if(void 0===t)return t=e.value,e.className.indexOf("k-readonly")>-1&&t===this.options.placeholder?"":t;e.value=null===t?"":t,this._placeholder()},_keydown:function(t){var e=this,i=t.keyCode,n=e.listView,s=e.popup.visible(),r=n.focus();if(e._last=i,i===h.DOWN)s?this._move(r?"focusNext":"focusFirst"):e.value()&&e._filterSource({value:e.ignoreCase?e.value().toLowerCase():e.value(),operator:e.options.filter,field:e.options.dataTextField,ignoreCase:e.ignoreCase}).done(function(){e._resetFocusItem(),e.popup.open()}),t.preventDefault();else if(i===h.UP)s&&this._move(r?"focusPrev":"focusLast"),t.preventDefault();else if(i===h.HOME)this._move("focusFirst");else if(i===h.END)this._move("focusLast");else if(i===h.ENTER||i===h.TAB){if(i===h.ENTER&&s&&t.preventDefault(),s&&r){var a=n.dataItemByIndex(n.getElementIndex(r));if(e.trigger("select",{dataItem:a,item:r}))return;this._select(r)}this._blur()}else if(i===h.ESC)s?t.preventDefault():e._clearValue(),e.close();else if(!e.popup.visible()||i!==h.PAGEDOWN&&i!==h.PAGEUP)e.popup._hovered=!0,e._search();else{t.preventDefault();var o=i===h.PAGEDOWN?1:-1;n.scrollWith(o*n.screenHeight())}},_keypress:function(){this._oldText=this.element.val(),this._typing=!0},_move:function(t){this.listView[t](),this.options.suggest&&this.suggest(this.listView.focus())},_hideBusy:function(){clearTimeout(this._busy),this._loading.hide(),this.element.attr("aria-busy",!1),this._busy=null,this._showClear()},_showBusy:function(){var t=this;t._busy||(t._busy=setTimeout(function(){t.element.attr("aria-busy",!0),t._loading.show(),t._hideClear()},100))},_placeholder:function(t){if(!a){var e,i=this.element,n=this.options.placeholder;if(n){if(e=i.val(),void 0===t&&(t=!e),t||(n=e!==n?e:""),e===this._old&&!t)return;i.toggleClass("k-readonly",t).val(n),n||i[0]!==document.activeElement||s(i[0],0,0)}}},_separator:function(){var t=this.options.separator;return t instanceof Array?new RegExp(t.join("|"),"gi"):t},_defaultSeparator:function(){var t=this.options.separator;return t instanceof Array?t[0]:t},_inputValue:function(){return this.element.val()},_search:function(){var t=this;clearTimeout(t._typingTimeout),t._typingTimeout=setTimeout(function(){t._prev!==t._accessor()&&(t._prev=t._accessor(),t.search())},t.options.delay)},_select:function(t){var e=this;return e._active=!0,e.listView.select(t).done(function(){e._active=!1})},_loader:function(){this._loading=t('<span class="k-icon k-i-loading" style="display:none"></span>').insertAfter(this.element)},_clearButton:function(){l.fn._clearButton.call(this),this.options.clearButton&&(this._clear.insertAfter(this.element),this.wrapper.addClass("k-autocomplete-clearable"))},_toggleHover:function(e){t(e.currentTarget).toggleClass("k-state-hover","mouseenter"===e.type)},_toggleCloseVisibility:function(){this.value()?this._showClear():this._hideClear()},_wrapper:function(){var t,e=this.element,i=e[0];(t=e.parent()).is("span.k-widget")||(t=e.wrap("<span />").parent()),t.attr("tabindex",-1),t.attr("role","presentation"),t[0].style.cssText=i.style.cssText,e.css({width:"",height:i.style.height}),this._focused=this.element,this.wrapper=t.addClass("k-widget k-autocomplete k-header").addClass(i.className)}});o.plugin(g)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.combobox",["kendo.list","kendo.mobile.scroller","kendo.virtuallist"],function(){return t=window.kendo.jQuery,e=window.kendo,i=e.ui,n=i.List,s=i.Select,r=e.caret,a=e.support,o=a.placeholder,l=e._activeElement,h=e.keys,c=(u=".kendoComboBox")+"FocusEvent",d="click"+u,p="mousedown"+u,f="k-state-default",m="k-state-disabled",g=t.proxy,_=/(\r\n|\n|\r)/gm,v=s.extend({init:function(i,n){var r;this.ns=u,n=t.isArray(n)?{dataSource:n}:n,s.fn.init.call(this,i,n),n=this.options,i=this.element.on("focus"+u,g(this._focusHandler,this)),n.placeholder=n.placeholder||i.attr("placeholder"),this._reset(),this._wrapper(),this._input(),this._clearButton(),this._tabindex(this.input),this._popup(),this._dataSource(),this._ignoreCase(),this._enable(),this._attachFocusEvents(),this._oldIndex=this.selectedIndex=-1,this._aria(),this._initialIndex=n.index,this.requireValueMapper(this.options),this._initList(),this._cascade(),n.autoBind?this._filterSource():(!(r=n.text)&&this._isSelect&&(r=i.children(":selected").text()),r&&this._setText(r)),r||this._placeholder(),t(this.element).parents("fieldset").is(":disabled")&&this.enable(!1),e.notify(this),this._toggleCloseVisibility()},options:{name:"ComboBox",enabled:!0,index:-1,text:null,value:null,autoBind:!0,delay:200,dataTextField:"",dataValueField:"",minLength:1,enforceMinLength:!1,height:200,highlightFirst:!0,filter:"none",placeholder:"",suggest:!1,cascadeFrom:"",cascadeFromField:"",ignoreCase:!0,animation:{},virtual:!1,template:null,groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",clearButton:!0,syncValueAndText:!0,autoWidth:!1},events:["open","close","change","select","filtering","dataBinding","dataBound","cascade","set"],setOptions:function(t){var e=this._listOptions(t);s.fn.setOptions.call(this,t),this.listView.setOptions(e),this._accessors(),this._aria(),this._clearButton()},destroy:function(){this.input.off(u),this.input.off(c),this.element.off(u),this._inputWrapper.off(u),clearTimeout(this._pasteTimeout),this._arrow.off(d+" "+p),this._clear.off(d+" "+p),s.fn.destroy.call(this)},_change:function(){var t=this.text(),e=t&&t!==this._oldText&&t!==this.options.placeholder,i=this.selectedIndex,n=-1===i;if(!this.options.syncValueAndText&&!this.value()&&n&&e)return this._old="",this._oldIndex=i,this._oldText=t,this._typing||this.element.trigger("change"),this.trigger("change"),void(this._typing=!1);s.fn._change.call(this),this._toggleCloseVisibility()},_attachFocusEvents:function(){this.input.on("focus"+c,g(this._inputFocus,this)).on("focusout"+c,g(this._inputFocusout,this))},_focusHandler:function(){this.input.focus()},_arrowClick:function(){this._toggle()},_inputFocus:function(){this._inputWrapper.addClass("k-state-focused"),this._placeholder(!1)},_inputFocusout:function(){var t=this.value();this._userTriggered=!0,this._inputWrapper.removeClass("k-state-focused"),clearTimeout(this._typingTimeout),this._typingTimeout=null,this.text(this.text());var e=this._focus(),i=this.listView.dataItemByIndex(this.listView.getElementIndex(e));t!==this.value()&&this.trigger("select",{dataItem:i,item:e})?this.value(t):(this._placeholder(),this._blur(),this.element.blur())},_inputPaste:function(){var t=this;clearTimeout(t._pasteTimeout),t._pasteTimeout=null,t._pasteTimeout=setTimeout(function(){t.search()})},_editable:function(t){var e=t.disable,i=t.readonly,n=this._inputWrapper.off(u),s=this.element.add(this.input.off(u)),r=this._arrow.off(d+" "+p),a=this._clear;i||e?(n.addClass(e?m:f).removeClass(e?f:m),s.attr("disabled",e).attr("readonly",i).attr("aria-disabled",e)):(n.addClass(f).removeClass(m).on("mouseenter.kendoComboBox mouseleave.kendoComboBox",this._toggleHover),s.removeAttr("disabled").removeAttr("readonly").attr("aria-disabled",!1),r.on(d,g(this._arrowClick,this)).on(p,function(t){t.preventDefault()}),a.on(d,g(this._clearValue,this)).on(p,function(t){t.preventDefault()}),this.input.on("keydown"+u,g(this._keydown,this)).on("input"+u,g(this._search,this)).on("paste"+u,g(this._inputPaste,this))),this._toggleCloseVisibility()},open:function(){var t=this._state,e=!!this.dataSource.filter()&&this.dataSource.filter().filters.length>0;this.popup.visible()||(!this.listView.bound()&&"filter"!==t||"accept"===t?(this._open=!0,this._state="rebind",1!==this.options.minLength&&!e||e&&this.value()&&-1===this.selectedIndex?(this.refresh(),this._openPopup(),this.options.virtual||this.listView.bound(!1)):this._filterSource()):this._allowOpening()&&(this.popup._hovered=!0,this._openPopup(),this.options.virtual&&this._focusItem()))},_scrollToFocusedItem:function(){var t=this.listView;t.scrollToIndex(t.getElementIndex(t.focus()))},_openPopup:function(){this.popup.one("activate",g(this._scrollToFocusedItem,this)),this.popup.open()},_updateSelectionState:function(){var t=this.options.text,e=this.options.value;this.listView.isFiltered()||(-1===this.selectedIndex?(void 0!==t&&null!==t||(t=e),this._accessor(e),this.input.val(t||this.input.val()),this._placeholder()):-1===this._oldIndex&&(this._oldIndex=this.selectedIndex))},_buildOptions:function(t){if(this._isSelect){var e=this._customOption;"rebind"===this._state&&(this._state=""),this._customOption=void 0,this._options(t,"",this.value()),e&&e[0].selected&&this._custom(e.val())}},_updateSelection:function(){var e=this.listView,i=this._initialIndex,n=null!==i&&i>-1;if("filter"===this._state)t(e.focus()).removeClass("k-state-selected");else if(!this._fetch){e.value().length||(n?this.select(i):this._accessor()&&e.value(this._accessor())),this._initialIndex=null;var s=e.selectedDataItems()[0];s&&(this._value(s)!==this.value()&&this._custom(this._value(s)),this.text()&&this.text()!==this._text(s)&&this._selectValue(s))}},_updateItemFocus:function(){var t=this.listView;this.options.highlightFirst?t.focus()||t.focusIndex()||t.focus(0):t.focus(-1)},_listBound:function(){var t=this.input[0]===l(),e=this.dataSource.flatView(),i=this.listView.skip(),n=e.length,s=this.dataSource._group?this.dataSource._group.length:0,r=void 0===i||0===i;this._presetValue=!1,this._renderFooter(),this._renderNoData(),this._toggleNoData(!n),this._toggleHeader(!!s&&!!n),this._resizePopup(),this.popup.position(),this._buildOptions(e),this._makeUnselectable(),this._updateSelection(),e.length&&r&&(this._updateItemFocus(),this.options.suggest&&t&&this.input.val()&&this.suggest(e[0])),this._open&&(this._open=!1,this._typingTimeout&&!t?this.popup.close():this.toggle(this._allowOpening()),this._typingTimeout=null),this._hideBusy(),this.trigger("dataBound")},_listChange:function(){this._selectValue(this.listView.selectedDataItems()[0]),this._presetValue&&(this._oldIndex=this.selectedIndex)},_get:function(t){var e,i,n;if("function"==typeof t){for(e=this.dataSource.flatView(),n=0;n<e.length;n++)if(t(e[n])){t=n,i=!0;break}i||(t=-1)}return t},_select:function(t,e){var i=this;return-1===(t=i._get(t))&&(i.input[0].value="",i._accessor("")),i.listView.select(t).done(function(){e||"filter"!==i._state||(i._state="accept"),i._toggleCloseVisibility()})},_selectValue:function(t){var e=this.listView.select(),i="",n="";void 0===(e=e[e.length-1])&&(e=-1),this.selectedIndex=e,this.listView.isFiltered()&&-1!==e&&(this._valueBeforeCascade=this._old),-1!==e||t?((t||0===t)&&(i=this._dataValue(t),n=this._text(t)),null===i&&(i="")):(this.options.syncValueAndText?i=n=this.options.dataTextField===this.options.dataValueField?this._accessor():this.input[0].value:n=this.text(),this.listView.focus(-1)),this._setDomInputValue(n),this._accessor(void 0!==i?i:n,e),this._placeholder(),this._triggerCascade()},_setDomInputValue:function(t){var e,i=this,n=r(this.input);if(n&&n.length&&(e=n[0]),this._prev=this.input[0].value=t,e&&-1===this.selectedIndex){var s=a.mobileOS;s.wp||s.android?setTimeout(function(){i.input[0].setSelectionRange(e,e)},0):this.input[0].setSelectionRange(e,e)}},refresh:function(){this.listView.refresh()},_toggleCloseVisibility:function(){var t=this.element.is(":disabled")||this.element.is("[readonly]");this.text()&&!t?this._showClear():this._hideClear()},suggest:function(t){var e,i=this.input[0],s=this.text(),a=r(i)[0],o=this._last;o!=h.BACKSPACE&&o!=h.DELETE?("string"!=typeof(t=t||"")&&(t[0]&&(t=this.dataSource.view()[n.inArray(t[0],this.ul[0])]),t=t?this._text(t):""),a<=0&&(a=s.toLowerCase().indexOf(t.toLowerCase())+1),t?(e=(t=t.toString()).toLowerCase().indexOf(s.toLowerCase()))>-1&&(s+=t.substring(e+s.length)):s=s.substring(0,a),s.length===a&&t||(i.value=s,i===l()&&r(i,a,s.length))):this._last=void 0},text:function(t){t=null===t?"":t;var e,i,s=this,r=s.input[0],a=s.options.ignoreCase,o=t;if(void 0===t)return r.value;!1!==s.options.autoBind||s.listView.bound()?(e=s.dataItem())&&s._text(e).replace&&s._text(e).replace(_,"")===t&&(i=s._value(e))===n.unifyType(s._old,typeof i)?s._triggerCascade():(a&&(o=o.toLowerCase()),s._select(function(t){return t=s._text(t),a&&(t=(t+"").toLowerCase()),t===o}).done(function(){s.selectedIndex<0&&(r.value=t,s.options.syncValueAndText&&s._accessor(t),s._cascadeTriggered=!0,s._triggerCascade()),s._prev=r.value})):s._setText(t)},toggle:function(t){this._toggle(t,!0)},value:function(t){var e=this,i=e.options,n=e.listView;if(void 0===t)return void 0===(t=e._accessor()||e.listView.value()[0])||null===t?"":t;e.requireValueMapper(e.options,t),e.trigger("set",{value:t}),t===i.value&&e.input.val()===i.text||(e._accessor(t),e._isFilterEnabled()&&n.bound()&&n.isFiltered()?e._clearFilter():e._fetchData(),n.value(t).done(function(){-1===e.selectedIndex&&(e._accessor(t),e.input.val(t),e._placeholder(!0)),e._old=e._accessor(),e._oldIndex=e.selectedIndex,e._prev=e.input.val(),"filter"===e._state&&(e._state="accept"),e._toggleCloseVisibility()}))},_hideBusy:function(){clearTimeout(this._busy),this._arrowIcon.removeClass("k-i-loading"),this._focused.attr("aria-busy",!1),this._busy=null,this._toggleCloseVisibility()},_click:function(t){var e=this,i=t.item,n=e.listView.dataItemByIndex(e.listView.getElementIndex(i));t.preventDefault(),e.trigger("select",{dataItem:n,item:i})?e.close():(e._userTriggered=!0,e._select(i).done(function(){e._blur()}))},_syncValueAndText:function(){return this.options.syncValueAndText},_inputValue:function(){return this.text()},_searchByWord:function(t){var e=this,i=e.options,n=e.dataSource,s=i.ignoreCase;if(s&&(t=t.toLowerCase()),e.ul[0].firstChild){this.listView.focus(this._get(function(i){var n=e._text(i);if(void 0!==n)return(""==(n+="")||""!==t)&&(s&&(n=n.toLowerCase()),0===n.indexOf(t))}));var r=this.listView.focus();r&&(i.suggest&&e.suggest(r),this.open()),this.options.highlightFirst&&!t&&this.listView.focusFirst()}else n.one("change",function(){n.view()[0]&&e.search(t)}).fetch()},_input:function(){var e,i,n=this.element.removeClass("k-input")[0],s=n.accessKey,r=this.wrapper,a=n.name||"";a&&(a='name="'+a+'_input" '),(e=r.find("input.k-input"))[0]||(r.append('<span tabindex="-1" unselectable="on" class="k-dropdown-wrap k-state-default"><input '+a+'class="k-input" type="text" autocomplete="off"/><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span>').append(this.element),e=r.find("input.k-input")),e[0].style.cssText=n.style.cssText,e[0].title=n.title,(i=parseInt(this.element.prop("maxlength")||this.element.attr("maxlength"),10))>-1&&(e[0].maxLength=i),e.addClass(n.className).css({width:"",height:n.style.height}).attr({role:"combobox","aria-expanded":!1}).show(),o&&e.attr("placeholder",this.options.placeholder),s&&(n.accessKey="",e[0].accessKey=s),this._focused=this.input=e,this._inputWrapper=t(r[0].firstChild),this._arrow=r.find(".k-select").attr({role:"button",tabIndex:-1}),this._arrowIcon=this._arrow.find(".k-icon"),n.id&&this._arrow.attr("aria-controls",this.ul[0].id)},_clearButton:function(){n.fn._clearButton.call(this),this.options.clearButton&&(this._clear.insertAfter(this.input),this.wrapper.addClass("k-combobox-clearable"))},_keydown:function(t){var e=this,i=t.keyCode;if(e._last=i,clearTimeout(e._typingTimeout),e._typingTimeout=null,i===h.HOME)e._firstItem();else if(i===h.END)e._lastItem();else if(i===h.ENTER||i===h.TAB){var s=e.listView.focus(),r=e.dataItem(),a=!0;if(e.popup.visible()||r&&e.text()===e._text(r)||(s=null),s){if(e.popup.visible()&&t.preventDefault(),(r=e.listView.dataItemByIndex(e.listView.getElementIndex(s)))&&(a=e._value(r)!==n.unifyType(e.value(),typeof e._value(r))),a&&e.trigger("select",{dataItem:r,item:s}))return;e._userTriggered=!0,e._select(s).done(function(){e._blur(),e._valueBeforeCascade=e._old=e.value()})}else(e._syncValueAndText()||e._isSelect)&&e._accessor(e.input.val()),e.listView.value(e.input.val()),e._blur()}else i==h.TAB||e._move(t)?i!==h.ESC||e.popup.visible()||e._clearValue():e._search()},_placeholder:function(t){if(!o){var e,i=this.input,n=this.options.placeholder;if(n){if(e=this.value(),void 0===t&&(t=!e),i.toggleClass("k-readonly",t),!t){if(e)return;n=""}i.val(n),n||i[0]!==l()||r(i[0],0,0)}}},_search:function(){var t=this;t._typingTimeout=setTimeout(function(){var e=t.text();t._prev!==e&&(t._prev=e,"none"===t.options.filter&&t.options.virtual&&t.listView.select(-1),t.search(e),t._toggleCloseVisibility()),t._typingTimeout=null},t.options.delay)},_setText:function(t){this.input.val(t),this._prev=t},_wrapper:function(){var t=this.element,e=t.parent();e.is("span.k-widget")||((e=t.hide().wrap("<span />").parent())[0].style.cssText=t[0].style.cssText),this.wrapper=e.addClass("k-widget k-combobox k-header").addClass(t[0].className).css("display","")},_clearSelection:function(t,e){var i=t.value(),n=i&&-1===t.selectedIndex;-1==this.selectedIndex&&this.value()||(e||!i||n)&&(this.options.value="",this.value(""),this._selectedValue=null)},_preselect:function(t,e){this.input.val(e),this._accessor(t),this._old=this._accessor(),this._oldIndex=this.selectedIndex,this.listView.setValue(t),this._placeholder(),this._initialIndex=null,this._presetValue=!0,this._toggleCloseVisibility()}}),i.plugin(v),window.kendo;var t,e,i,n,s,r,a,o,l,h,u,c,d,p,f,m,g,_,v}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.dropdownlist",["kendo.list","kendo.mobile.scroller","kendo.virtuallist"],function(){return function(t,e){var i=window.kendo,n=i.ui,s=n.List,r=n.Select,a=i.support,o=i._activeElement,l=i.data.ObservableObject,h=i.keys,u=".kendoDropDownList",c=u+"FocusEvent",d="k-state-default",p="k-state-disabled",f="mouseenter"+u+" mouseleave"+u,m="tabindex",g=t.proxy,_=r.extend({init:function(e,n){var s,a,o=this,l=n&&n.index;o.ns=u,n=t.isArray(n)?{dataSource:n}:n,r.fn.init.call(o,e,n),n=o.options,e=o.element.on("focus"+u,g(o._focusHandler,o)),o._focusInputHandler=t.proxy(o._focusInput,o),o.optionLabel=t(),o._optionLabel(),o._inputTemplate(),o._reset(),o._prev="",o._word="",o._wrapper(),o._tabindex(),o.wrapper.data(m,o.wrapper.attr(m)),o._span(),o._popup(),o._mobile(),o._dataSource(),o._ignoreCase(),o._filterHeader(),o._aria(),o.wrapper.attr("aria-live","polite"),o._enable(),o._attachFocusHandlers(),o._oldIndex=o.selectedIndex=-1,void 0!==l&&(n.index=l),o._initialIndex=n.index,o.requireValueMapper(o.options),o._initList(),o._cascade(),o.one("set",function(t){!t.sender.listView.bound()&&o.hasOptionLabel()&&o._textAccessor(o._optionLabelText())}),n.autoBind?o.dataSource.fetch():-1===o.selectedIndex&&((a=n.text||"")||((s=n.optionLabel)&&0===n.index?a=s:o._isSelect&&(a=e.children(":selected").text())),o._textAccessor(a)),t(o.element).parents("fieldset").is(":disabled")&&o.enable(!1),o.listView.bind("click",function(t){t.preventDefault()}),i.notify(o)},options:{name:"DropDownList",enabled:!0,autoBind:!0,index:0,text:null,value:null,delay:500,height:200,dataTextField:"",dataValueField:"",optionLabel:"",cascadeFrom:"",cascadeFromField:"",ignoreCase:!0,animation:{},filter:"none",minLength:1,enforceMinLength:!1,virtual:!1,template:null,valueTemplate:null,optionLabelTemplate:null,groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",autoWidth:!1},events:["open","close","change","select","filtering","dataBinding","dataBound","cascade","set"],setOptions:function(t){r.fn.setOptions.call(this,t),this.listView.setOptions(this._listOptions(t)),this._optionLabel(),this._inputTemplate(),this._accessors(),this._filterHeader(),this._enable(),this._aria(),!this.value()&&this.hasOptionLabel()&&this.select(0)},destroy:function(){r.fn.destroy.call(this),this.wrapper.off(u),this.wrapper.off(c),this.element.off(u),this._inputWrapper.off(u),this._arrow.off(),this._arrow=null,this._arrowIcon=null,this.optionLabel.off()},open:function(){var t=!!this.dataSource.filter()&&this.dataSource.filter().filters.length>0;this.popup.visible()||(this.listView.bound()&&"accept"!==this._state?this._allowOpening()&&(this._focusFilter=!0,this.popup.one("activate",this._focusInputHandler),this.popup._hovered=!0,this.popup.open(),this._resizeFilterInput(),this._focusItem()):(this._open=!0,this._state="rebind",this.filterInput&&(this.filterInput.val(""),this._prev=""),this.filterInput&&1!==this.options.minLength&&!t?(this.refresh(),this.popup.one("activate",this._focusInputHandler),this.popup.open(),this._resizeFilterInput()):this._filterSource()))},_focusInput:function(){this._focusElement(this.filterInput)},_resizeFilterInput:function(){var t=this.filterInput,e=this._prevent;if(t){var n=this.filterInput[0]===o(),s=i.caret(this.filterInput[0])[0];this._prevent=!0,t.css("display","none").css("width",this.popup.element.css("width")).css("display","inline-block"),n&&(t.focus(),i.caret(t[0],s)),this._prevent=e}},_allowOpening:function(){return this.hasOptionLabel()||this.filterInput||r.fn._allowOpening.call(this)},toggle:function(t){this._toggle(t,!0)},current:function(t){var e;if(void 0===t)return!(e=this.listView.focus())&&0===this.selectedIndex&&this.hasOptionLabel()?this.optionLabel:e;this._focus(t)},dataItem:function(e){var i=null;if(null===e)return e;if(void 0===e)i=this.listView.selectedDataItems()[0];else{if("number"!=typeof e){if(this.options.virtual)return this.dataSource.getByUid(t(e).data("uid"));e=e.hasClass("k-list-optionlabel")?-1:t(this.items()).index(e)}else this.hasOptionLabel()&&(e-=1);i=this.dataSource.flatView()[e]}return i||(i=this._optionLabelDataItem()),i},refresh:function(){this.listView.refresh()},text:function(t){var e,i=this,n=i.options.ignoreCase;if(void 0===(t=null===t?"":t))return i._textAccessor();"string"==typeof t?(e=n?t.toLowerCase():t,i._select(function(t){return t=i._text(t),n&&(t=(t+"").toLowerCase()),t===e}).done(function(){i._textAccessor(i.dataItem()||t)})):i._textAccessor(t)},_clearFilter:function(){t(this.filterInput).val(""),r.fn._clearFilter.call(this)},value:function(t){var e=this,i=e.listView,n=e.dataSource;return void 0===t?void 0===(t=e._accessor()||e.listView.value()[0])||null===t?"":t:(e.requireValueMapper(e.options,t),!t&&e.hasOptionLabel()||(e._initialIndex=null),this.trigger("set",{value:t}),e._request&&e.options.cascadeFrom&&e.listView.bound()?(e._valueSetter&&n.unbind("change",e._valueSetter),e._valueSetter=g(function(){e.value(t)},e),void n.one("change",e._valueSetter)):(e._isFilterEnabled()&&i.bound()&&i.isFiltered()?e._clearFilter():e._fetchData(),void i.value(t).done(function(){e._old=e._accessor(),e._oldIndex=e.selectedIndex})))},hasOptionLabel:function(){return this.optionLabel&&!!this.optionLabel[0]},_optionLabel:function(){var e=this,n=e.options,s=n.optionLabel,r=n.optionLabelTemplate;if(!s)return e.optionLabel.off().remove(),void(e.optionLabel=t());r||(r="#:",r+="string"==typeof s?"data":i.expr(n.dataTextField,"data"),r+="#"),"function"!=typeof r&&(r=i.template(r)),e.optionLabelTemplate=r,e.hasOptionLabel()||(e.optionLabel=t('<div class="k-list-optionlabel"></div>').prependTo(e.list)),e.optionLabel.html(r(s)).off().click(g(e._click,e)).on(f,e._toggleHover),e.angular("compile",function(){return{elements:e.optionLabel,data:[{dataItem:e._optionLabelDataItem()}]}})},_optionLabelText:function(){var t=this.options.optionLabel;return"string"==typeof t?t:this._text(t)},_optionLabelDataItem:function(){var e=this.options.optionLabel;if(this.hasOptionLabel())return t.isPlainObject(e)?new l(e):this._assignInstance(this._optionLabelText(),"")},_buildOptions:function(t){if(this._isSelect){var e=this.listView.value()[0],i=this._optionLabelDataItem(),n=i&&this._value(i);void 0!==e&&null!==e||(e=""),i&&(void 0!==n&&null!==n||(n=""),i='<option value="'+n+'">'+this._text(i)+"</option>"),this._options(t,i,e),e!==s.unifyType(this._accessor(),typeof e)&&(this._customOption=null,this._custom(e))}},_listBound:function(){var t,e=this._initialIndex,i="filter"===this._state,n=this.dataSource.flatView();this._presetValue=!1,this._renderFooter(),this._renderNoData(),this._toggleNoData(!n.length),this._resizePopup(!0),this.popup.position(),this._buildOptions(n),this._makeUnselectable(),i||(this._open&&this.toggle(this._allowOpening()),this._open=!1,this._fetch||(n.length?(!this.listView.value().length&&e>-1&&null!==e&&this.select(e),this._initialIndex=null,(t=this.listView.selectedDataItems()[0])&&this.text()!==this._text(t)&&this._selectValue(t)):this._textAccessor()!==this._optionLabelText()&&(this.listView.value(""),this._selectValue(null),this._oldIndex=this.selectedIndex))),this._hideBusy(),this.trigger("dataBound")},_listChange:function(){this._selectValue(this.listView.selectedDataItems()[0]),(this._presetValue||this._old&&-1===this._oldIndex)&&(this._oldIndex=this.selectedIndex)},_filterPaste:function(){this._search()},_attachFocusHandlers:function(){this.wrapper.on("focusin"+c,g(this._focusinHandler,this)).on("focusout"+c,g(this._focusoutHandler,this))},_focusHandler:function(){this.wrapper.focus()},_focusinHandler:function(){this._inputWrapper.addClass("k-state-focused"),this._prevent=!1},_focusoutHandler:function(){var t=window.self!==window.top;this._prevent||(clearTimeout(this._typingTimeout),a.mobileOS.ios&&t?this._change():this._blur(),this._inputWrapper.removeClass("k-state-focused"),this._prevent=!0,this._open=!1,this.element.blur())},_wrapperMousedown:function(){this._prevent=!!this.filterInput},_wrapperClick:function(t){t.preventDefault(),this.popup.unbind("activate",this._focusInputHandler),this._focused=this.wrapper,this._prevent=!1,this._toggle()},_editable:function(t){var e=this.element,i=t.disable,n=t.readonly,s=this.wrapper.add(this.filterInput).off(u),r=this._inputWrapper.off(f);n||i?i?(s.removeAttr(m),r.addClass(p).removeClass(d)):r.addClass(d).removeClass(p):(e.removeAttr("disabled").removeAttr("readonly"),r.addClass(d).removeClass(p).on(f,this._toggleHover),s.attr(m,s.data(m)).attr("aria-disabled",!1).on("keydown"+u,g(this._keydown,this)).on("mousedown"+u,g(this._wrapperMousedown,this)).on("paste"+u,g(this._filterPaste,this)),this.wrapper.on("click"+u,g(this._wrapperClick,this)),this.filterInput?s.on("input"+u,g(this._search,this)):s.on("keypress"+u,g(this._keypress,this))),e.attr("disabled",i).attr("readonly",n),s.attr("aria-disabled",i)},_keydown:function(t){var e,i,n=this,s=t.keyCode,r=t.altKey,a=n.popup.visible();if(n.filterInput&&(e=n.filterInput[0]===o()),s===h.LEFT?(s=h.UP,i=!0):s===h.RIGHT&&(s=h.DOWN,i=!0),!i||!e)if(t.keyCode=s,(r&&s===h.UP||s===h.ESC)&&n._focusElement(n.wrapper),"filter"===n._state&&s===h.ESC&&(n._clearFilter(),n._open=!1,n._state="accept"),s===h.ENTER&&n._typingTimeout&&n.filterInput&&a)t.preventDefault();else if(s!==h.SPACEBAR||e||(n.toggle(!a),t.preventDefault()),!(i=n._move(t))){if(!a||!n.filterInput){var l=n._focus();if(s===h.HOME?(i=!0,n._firstItem()):s===h.END&&(i=!0,n._lastItem()),i){if(n.trigger("select",{dataItem:n._getElementDataItem(n._focus()),item:n._focus()}))return void n._focus(l);n._select(n._focus(),!0).done(function(){a||n._blur()}),t.preventDefault()}}r||i||!n.filterInput||n._search()}},_matchText:function(t,e){var i=this.options.ignoreCase;return void 0!==t&&null!==t&&(t+="",i&&(t=t.toLowerCase()),0===t.indexOf(e))},_shuffleData:function(t,e){var i=this._optionLabelDataItem();return i&&(t=[i].concat(t)),t.slice(e).concat(t.slice(0,e))},_selectNext:function(){var t,e,i=this,n=i.dataSource.flatView(),s=n.length+(i.hasOptionLabel()?1:0),r=function(t,e){for(var i=0;i<t.length;i++)if(t.charAt(i)!==e)return!1;return!0}(i._word,i._last),a=i.selectedIndex;a=-1===a?0:w(a+=r?1:0,s),n=n.toJSON?n.toJSON():n.slice(),n=i._shuffleData(n,a);for(var o=0;o<s&&(e=i._text(n[o]),!r||!i._matchText(e,i._last))&&!i._matchText(e,i._word);o++);o!==s&&(t=i._focus(),i._select(w(a+o,s)).done(function(){var e=function(){i.popup.visible()||i._change()};i.trigger("select",{dataItem:i._getElementDataItem(i._focus()),item:i._focus()})?i._select(t).done(e):e()}))},_keypress:function(t){if(0!==t.which&&t.keyCode!==i.keys.ENTER){var e=String.fromCharCode(t.charCode||t.keyCode);this.options.ignoreCase&&(e=e.toLowerCase())," "===e&&t.preventDefault(),this._word+=e,this._last=e,this._search()}},_popupOpen:function(){var t=this.popup;t.wrapper=i.wrap(t.element),t.element.closest(".km-root")[0]&&(t.wrapper.addClass("km-popup km-widget"),this.wrapper.addClass("km-widget"))},_popup:function(){r.fn._popup.call(this),this.popup.one("open",g(this._popupOpen,this))},_getElementDataItem:function(t){return t&&t[0]?t[0]===this.optionLabel[0]?this._optionLabelDataItem():this.listView.dataItemByIndex(this.listView.getElementIndex(t)):null},_click:function(e){var i=this,n=e.item||t(e.currentTarget);e.preventDefault(),i.trigger("select",{dataItem:i._getElementDataItem(n),item:n})?i.close():(i._userTriggered=!0,i._select(n).done(function(){i._focusElement(i.wrapper),i._blur()}))},_focusElement:function(t){var e=o(),i=this.wrapper,n=this.filterInput,s=t===n?i:n,r=a.mobileOS&&(a.touch||a.MSPointers||a.pointers);n&&n[0]===t[0]&&r||n&&(s[0]===e||this._focusFilter)&&(this._focusFilter=!1,this._prevent=!0,this._focused=t.focus())},_searchByWord:function(t){if(t){var e=this;e.options.ignoreCase&&(t=t.toLowerCase()),e._select(function(i){return e._matchText(e._text(i),t)})}},_inputValue:function(){return this.text()},_search:function(){var t=this,e=t.dataSource;if(clearTimeout(t._typingTimeout),t._isFilterEnabled())t._typingTimeout=setTimeout(function(){var e=t.filterInput.val();t._prev!==e&&(t._prev=e,t.search(e),t._resizeFilterInput()),t._typingTimeout=null},t.options.delay);else{if(t._typingTimeout=setTimeout(function(){t._word=""},t.options.delay),!t.listView.bound())return void e.fetch().done(function(){t._selectNext()});t._selectNext()}},_get:function(e){var i,n,s,r="function"==typeof e,a=r?t():t(e);if(this.hasOptionLabel()&&("number"==typeof e?e>-1&&(e-=1):a.hasClass("k-list-optionlabel")&&(e=-1)),r){for(i=this.dataSource.flatView(),s=0;s<i.length;s++)if(e(i[s])){e=s,n=!0;break}n||(e=-1)}return e},_firstItem:function(){this.hasOptionLabel()?this._focus(this.optionLabel):this.listView.focusFirst()},_lastItem:function(){this._resetOptionLabel(),this.listView.focusLast()},_nextItem:function(){this.optionLabel.hasClass("k-state-focused")?(this._resetOptionLabel(),this.listView.focusFirst()):this.listView.focusNext()},_prevItem:function(){this.optionLabel.hasClass("k-state-focused")||(this.listView.focusPrev(),this.listView.focus()||this._focus(this.optionLabel))},_focusItem:function(){var t=this.options,e=this.listView,i=e.focus(),n=e.select();void 0===(n=n[n.length-1])&&t.highlightFirst&&!i&&(n=0),void 0!==n?e.focus(n):!t.optionLabel||t.virtual&&"dataItem"===t.virtual.mapValueTo?e.scrollToIndex(0):(this._focus(this.optionLabel),this._select(this.optionLabel),this.listView.content.scrollTop(0))},_resetOptionLabel:function(t){this.optionLabel.removeClass("k-state-focused"+(t||"")).removeAttr("id")},_focus:function(t){var e=this.listView,i=this.optionLabel;if(void 0===t)return!(t=e.focus())&&i.hasClass("k-state-focused")&&(t=i),t;this._resetOptionLabel(),t=this._get(t),e.focus(t),-1===t&&(i.addClass("k-state-focused").attr("id",e._optionID),this._focused.add(this.filterInput).removeAttr("aria-activedescendant").attr("aria-activedescendant",e._optionID))},_select:function(t,e){var i=this;return t=i._get(t),i.listView.select(t).done(function(){e||"filter"!==i._state||(i._state="accept"),-1===t&&i._selectValue(null)})},_selectValue:function(t){var e=this.options.optionLabel,i=this.listView.select(),n="",s="";void 0===(i=i[i.length-1])&&(i=-1),this._resetOptionLabel(" k-state-selected"),t||0===t?(s=t,n=this._dataValue(t),e&&(i+=1)):e&&(this._focus(this.optionLabel.addClass("k-state-selected")),s=this._optionLabelText(),n="string"==typeof e?"":this._value(e),i=0),this.selectedIndex=i,null===n&&(n=""),this._textAccessor(s),this._accessor(n,i),this._triggerCascade()},_mobile:function(){var t=this.popup,e=a.mobileOS;t.element.parents(".km-root").eq(0).length&&e&&(t.options.animation.open.effects=e.android||e.meego?"fadeIn":e.ios||e.wp?"slideIn:up":t.options.animation.open.effects)},_filterHeader:function(){var e;this.filterInput&&(this.filterInput.off(u).parent().remove(),this.filterInput=null),this._isFilterEnabled()&&(e='<span class="k-icon k-i-zoom"></span>',this.filterInput=t('<input class="k-textbox"/>').attr({placeholder:this.element.attr("placeholder"),title:this.element.attr("title"),role:"listbox","aria-haspopup":!0,"aria-expanded":!1}),this.list.prepend(t('<span class="k-list-filter" />').append(this.filterInput.add(e))))},_span:function(){var e,i=this.wrapper;(e=i.find("span.k-input"))[0]||(i.append('<span unselectable="on" class="k-dropdown-wrap k-state-default"><span unselectable="on" class="k-input">&nbsp;</span><span unselectable="on" class="k-select" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span>').append(this.element),e=i.find("span.k-input")),this.span=e,this._inputWrapper=t(i[0].firstChild),this._arrow=i.find(".k-select"),this._arrowIcon=this._arrow.find(".k-icon")},_wrapper:function(){var t,e=this.element,i=e[0];(t=e.parent()).is("span.k-widget")||((t=e.wrap("<span />").parent())[0].style.cssText=i.style.cssText,t[0].title=i.title),this._focused=this.wrapper=t.addClass("k-widget k-dropdown k-header").addClass(i.className).css("display","").attr({accesskey:e.attr("accesskey"),unselectable:"on",role:"listbox","aria-haspopup":!0,"aria-expanded":!1}),e.hide().removeAttr("accesskey")},_clearSelection:function(t){this.select(t.value()?0:-1)},_inputTemplate:function(){var e=this.options.valueTemplate;if(e=e?i.template(e):t.proxy(i.template("#:this._text(data)#",{useWithBlock:!1}),this),this.valueTemplate=e,this.hasOptionLabel()&&!this.options.optionLabelTemplate)try{this.valueTemplate(this._optionLabelDataItem())}catch(t){throw new Error("The `optionLabel` option is not valid due to missing fields. Define a custom optionLabel as shown here http://docs.telerik.com/kendo-ui/api/javascript/ui/dropdownlist#configuration-optionLabel")}},_textAccessor:function(e){var i=null,n=this.valueTemplate,s=this._optionLabelText(),r=this.span;if(void 0===e)return r.text();t.isPlainObject(e)||e instanceof l?i=e:s&&s===e&&(i=this.options.optionLabel),i||(i=this._assignInstance(e,this._accessor())),this.hasOptionLabel()&&(i!==s&&this._text(i)!==s||(n=this.optionLabelTemplate,"string"!=typeof this.options.optionLabel||this.options.optionLabelTemplate||(i=s)));var a=function(){return{elements:r.get(),data:[{dataItem:i}]}};this.angular("cleanup",a);try{r.html(n(i))}catch(t){r.html("")}this.angular("compile",a)},_preselect:function(t,e){t||e||(e=this._optionLabelText()),this._accessor(t),this._textAccessor(e),this._old=this._accessor(),this._oldIndex=this.selectedIndex,this.listView.setValue(t),this._initialIndex=null,this._presetValue=!0},_assignInstance:function(t,e){var i=this.options.dataTextField,n={};return i?(v(n,i.split("."),t),v(n,this.options.dataValueField.split("."),e),n=new l(n)):n=t,n}});function v(t,e,i){for(var n,s=0,r=e.length-1;s<r;++s)(n=e[s])in t||(t[n]={}),t=t[n];t[e[r]]=i}function w(t,e){return t>=e&&(t-=e),t}n.plugin(_)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.colorpicker",["kendo.core","kendo.color","kendo.popup","kendo.slider","kendo.userevents","kendo.button"],function(){return function(t,e,i){var n=window.kendo,s=n.ui,r=s.Widget,a=n.parseColor,o=n.Color,l=n.keys,h="background-color",u="k-state-selected",c={apply:"Apply",cancel:"Cancel",noColor:"no color",clearColor:"Clear color",previewInput:"Color Hexadecimal Code"},d=".kendoColorTools",p="click"+d,f="keydown"+d,m=n.support.browser,g=m.msie&&m.version<9,_=r.extend({init:function(t,e){var i;r.fn.init.call(this,t,e),t=this.element,e=this.options,this._value=e.value=a(e.value),this._tabIndex=t.attr("tabIndex")||0,(i=this._ariaId=e.ariaId)&&t.attr("aria-labelledby",i),e._standalone&&(this._triggerSelect=this._triggerChange)},options:{name:"ColorSelector",value:null,_standalone:!0},events:["change","select","cancel"],color:function(t){return void 0!==t&&(this._value=a(t),this._updateUI(this._value)),this._value},value:function(t){return(t=this.color(t))&&(t=this.options.opacity?t.toCssRgba():t.toCss()),t||null},enable:function(e){0===arguments.length&&(e=!0),t(".k-disabled-overlay",this.wrapper).remove(),e||this.wrapper.append("<div class='k-disabled-overlay'></div>"),this._onEnable(e)},_select:function(t,e){var i=this._value;t=this.color(t),e||(this.element.trigger("change"),t.equals(i)?this._standalone||this.trigger("cancel"):this.trigger("change",{value:this.value()}))},_triggerSelect:function(t){v(this,"select",t)},_triggerChange:function(t){v(this,"change",t)},destroy:function(){this.element&&this.element.off(d),this.wrapper&&this.wrapper.off(d).find("*").off(d),this.wrapper=null,r.fn.destroy.call(this)},_updateUI:t.noop,_selectOnHide:function(){return null},_cancel:function(){this.trigger("cancel")}});function v(t,e,i){(i=a(i))&&!i.equals(t.color())&&("change"==e&&(t._value=i),i=1!=i.a?i.toCssRgba():i.toCss(),t.trigger(e,{value:i}))}var w=_.extend({init:function(e,i){var s=this;_.fn.init.call(s,e,i),e=s.wrapper=s.element;var r=(i=s.options).palette;"websafe"==r?(r="FFFFFF,FFCCFF,FF99FF,FF66FF,FF33FF,FF00FF,CCFFFF,CCCCFF,CC99FF,CC66FF,CC33FF,CC00FF,99FFFF,99CCFF,9999FF,9966FF,9933FF,9900FF,FFFFCC,FFCCCC,FF99CC,FF66CC,FF33CC,FF00CC,CCFFCC,CCCCCC,CC99CC,CC66CC,CC33CC,CC00CC,99FFCC,99CCCC,9999CC,9966CC,9933CC,9900CC,FFFF99,FFCC99,FF9999,FF6699,FF3399,FF0099,CCFF99,CCCC99,CC9999,CC6699,CC3399,CC0099,99FF99,99CC99,999999,996699,993399,990099,FFFF66,FFCC66,FF9966,FF6666,FF3366,FF0066,CCFF66,CCCC66,CC9966,CC6666,CC3366,CC0066,99FF66,99CC66,999966,996666,993366,990066,FFFF33,FFCC33,FF9933,FF6633,FF3333,FF0033,CCFF33,CCCC33,CC9933,CC6633,CC3333,CC0033,99FF33,99CC33,999933,996633,993333,990033,FFFF00,FFCC00,FF9900,FF6600,FF3300,FF0000,CCFF00,CCCC00,CC9900,CC6600,CC3300,CC0000,99FF00,99CC00,999900,996600,993300,990000,66FFFF,66CCFF,6699FF,6666FF,6633FF,6600FF,33FFFF,33CCFF,3399FF,3366FF,3333FF,3300FF,00FFFF,00CCFF,0099FF,0066FF,0033FF,0000FF,66FFCC,66CCCC,6699CC,6666CC,6633CC,6600CC,33FFCC,33CCCC,3399CC,3366CC,3333CC,3300CC,00FFCC,00CCCC,0099CC,0066CC,0033CC,0000CC,66FF99,66CC99,669999,666699,663399,660099,33FF99,33CC99,339999,336699,333399,330099,00FF99,00CC99,009999,006699,003399,000099,66FF66,66CC66,669966,666666,663366,660066,33FF66,33CC66,339966,336666,333366,330066,00FF66,00CC66,009966,006666,003366,000066,66FF33,66CC33,669933,666633,663333,660033,33FF33,33CC33,339933,336633,333333,330033,00FF33,00CC33,009933,006633,003333,000033,66FF00,66CC00,669900,666600,663300,660000,33FF00,33CC00,339900,336600,333300,330000,00FF00,00CC00,009900,006600,003300,000000",i.columns=18):"basic"==r&&(r="000000,7f7f7f,880015,ed1c24,ff7f27,fff200,22b14c,00a2e8,3f48cc,a349a4,ffffff,c3c3c3,b97a57,ffaec9,ffc90e,efe4b0,b5e61d,99d9ea,7092be,c8bfe7"),"string"==typeof r&&(r=r.split(",")),t.isArray(r)&&(r=t.map(r,function(t){return a(t)})),s._selectedID=(i.ariaId||n.guid())+"_selected",e.addClass("k-widget k-colorpalette").attr("role","grid").attr("aria-readonly","true").append(t(s._template({colors:r,columns:i.columns,tileSize:i.tileSize,value:s._value,id:i.ariaId}))).on(p,".k-item",function(e){s._select(t(e.currentTarget).css(h))}).attr("tabIndex",s._tabIndex).on(f,C(s._keydown,s));var o,l,u=i.tileSize;if(u){if(/number|string/.test(typeof u))o=l=parseFloat(u);else{if("object"!=typeof u)throw new Error("Unsupported value for the 'tileSize' argument");o=parseFloat(u.width),l=parseFloat(u.height)}e.find(".k-item").css({width:o,height:l})}},focus:function(){this.wrapper&&!this.wrapper.is("[unselectable='on']")&&this.wrapper.focus()},options:{name:"ColorPalette",columns:10,tileSize:null,palette:"basic"},_onEnable:function(t){t?this.wrapper.attr("tabIndex",this._tabIndex):this.wrapper.removeAttr("tabIndex")},_keydown:function(e){var i,n=this.wrapper.find(".k-item"),s=n.filter("."+u).get(0),r=e.keyCode;if(r==l.LEFT?i=y(n,s,-1):r==l.RIGHT?i=y(n,s,1):r==l.DOWN?i=y(n,s,this.options.columns):r==l.UP?i=y(n,s,-this.options.columns):r==l.ENTER?(x(e),s&&this._select(t(s).css(h))):r==l.ESC&&this._cancel(),i){x(e),this._current(i);try{var o=a(i.css(h));this._triggerSelect(o)}catch(t){}}},_current:function(e){this.wrapper.find("."+u).removeClass(u).attr("aria-selected",!1).removeAttr("id"),t(e).addClass(u).attr("aria-selected",!0).attr("id",this._selectedID),this.element.removeAttr("aria-activedescendant").attr("aria-activedescendant",this._selectedID)},_updateUI:function(e){var i=null;this.wrapper.find(".k-item").each(function(){var n=a(t(this).css(h));if(n&&n.equals(e))return i=this,!1}),this._current(i)},_template:n.template('<table class="k-palette k-reset" role="presentation"><tr role="row"># for (var i = 0; i < colors.length; ++i) { ## var selected = colors[i].equals(value); ## if (i && i % columns == 0) { # </tr><tr role="row"> # } #<td role="gridcell" unselectable="on" style="background-color:#= colors[i].toCss() #"#= selected ? " aria-selected=true" : "" # #=(id && i === 0) ? "id=\\""+id+"\\" " : "" # class="k-item#= selected ? " '+u+'" : "" #" aria-label="#= colors[i].toCss() #"></td># } #</tr></table>')}),b=_.extend({init:function(e,i){var n=this;_.fn.init.call(n,e,i),(i=n.options).messages=i.options?t.extend(n.options.messages,i.options.messages):n.options.messages,e=n.element,n.wrapper=e.addClass("k-widget k-flatcolorpicker").append(n._template(i)),n._hueElements=t(".k-hsv-rectangle, .k-transparency-slider .k-slider-track",e),n._selectedColor=t(".k-selected-color-display",e),n._colorAsText=t("input.k-color-value",e),n._sliders(),n._hsvArea(),n._updateUI(n._value||a("#f00")),e.find("input.k-color-value").on(f,function(e){var i=this;if(e.keyCode==l.ENTER)try{var s=a(i.value),r=n.color();n._select(s,s.equals(r))}catch(e){t(i).addClass("k-state-error")}else n.options.autoupdate&&setTimeout(function(){var t=a(i.value,!0);t&&n._updateUI(t,!0)},10)}).end().on(p,".k-controls button.apply",function(){n.options._clearedColor?n.trigger("change"):n._select(n._getHSV())}).on(p,".k-controls button.cancel",function(){n._updateUI(n.color()),n._cancel()}),g&&n._applyIEFilter()},destroy:function(){this._hueSlider.destroy(),this._opacitySlider&&this._opacitySlider.destroy(),this._hueSlider=this._opacitySlider=this._hsvRect=this._hsvHandle=this._hueElements=this._selectedColor=this._colorAsText=null,_.fn.destroy.call(this)},options:{name:"FlatColorPicker",opacity:!1,buttons:!1,input:!0,preview:!0,clearButton:!1,autoupdate:!0,messages:c},_applyIEFilter:function(){var t=this.element.find(".k-hue-slider .k-slider-track")[0],e=t.currentStyle.backgroundImage;e=e.replace(/^url\([\'\"]?|[\'\"]?\)$/g,""),t.style.filter="progid:DXImageTransform.Microsoft.AlphaImageLoader(src='"+e+"', sizingMethod='scale')"},_sliders:function(){var t=this,e=t.element,i=e.find(".k-hue-slider"),n=e.find(".k-transparency-slider");function s(e){t._updateUI(t._getHSV(e.value,null,null,null))}function r(e){t._updateUI(t._getHSV(null,null,null,e.value/100))}i.attr("aria-label","hue saturation"),t._hueSlider=i.kendoSlider({min:0,max:360,tickPlacement:"none",showButtons:!1,slide:s,change:s}).data("kendoSlider"),n.attr("aria-label","opacity"),t._opacitySlider=n.kendoSlider({min:0,max:100,tickPlacement:"none",showButtons:!1,slide:r,change:r}).data("kendoSlider")},_hsvArea:function(){var t=this,e=t.element.find(".k-hsv-rectangle"),i=e.find(".k-draghandle").attr("tabIndex",0).on(f,C(t._keydown,t));function s(e,i){var n=this.offset,s=e-n.left,r=i-n.top,a=this.width,o=this.height;s=s<0?0:s>a?a:s,r=r<0?0:r>o?o:r,t._svChange(s/a,1-r/o)}t._hsvEvents=new n.UserEvents(e,{global:!0,press:function(t){this.offset=n.getOffset(e),this.width=e.width(),this.height=e.height(),i.focus(),s.call(this,t.x.location,t.y.location)},start:function(){e.addClass("k-dragging"),i.focus()},move:function(t){t.preventDefault(),s.call(this,t.x.location,t.y.location)},end:function(){e.removeClass("k-dragging")}}),t._hsvRect=e,t._hsvHandle=i},_onEnable:function(t){this._hueSlider.enable(t),this._opacitySlider&&this._opacitySlider.enable(t),this.wrapper.find("input").attr("disabled",!t);var e=this._hsvRect.find(".k-draghandle");t?e.attr("tabIndex",this._tabIndex):e.removeAttr("tabIndex")},_keydown:function(t){var e=this;function i(i,n){var s=e._getHSV();s[i]+=n*(t.shiftKey?.01:.05),s[i]<0&&(s[i]=0),s[i]>1&&(s[i]=1),e._updateUI(s),x(t)}function n(i){var n=e._getHSV();n.h+=i*(t.shiftKey?1:5),n.h<0&&(n.h=0),n.h>359&&(n.h=359),e._updateUI(n),x(t)}switch(t.keyCode){case l.LEFT:t.ctrlKey?n(-1):i("s",-1);break;case l.RIGHT:t.ctrlKey?n(1):i("s",1);break;case l.UP:i(t.ctrlKey&&e._opacitySlider?"a":"v",1);break;case l.DOWN:i(t.ctrlKey&&e._opacitySlider?"a":"v",-1);break;case l.ENTER:e._select(e._getHSV());break;case l.F2:e.wrapper.find("input.k-color-value").focus().select();break;case l.ESC:e._cancel()}},focus:function(){this._hsvHandle.focus()},_getHSV:function(t,e,i,n){var s=this._hsvRect,r=s.width(),a=s.height(),l=this._hsvHandle.position();return null==t&&(t=this._hueSlider.value()),null==e&&(e=l.left/r),null==i&&(i=1-l.top/a),null==n&&(n=this._opacitySlider?this._opacitySlider.value()/100:1),o.fromHSV(t,e,i,n)},_svChange:function(t,e){var i=this._getHSV(null,t,e,null);this._updateUI(i)},_updateUI:function(t,e){var i=this._hsvRect;t&&(this._colorAsText.attr("title",this.options.messages.previewInput),this._colorAsText.removeClass("k-state-error"),this._selectedColor.css(h,t.toDisplay()),e||this._colorAsText.val(this._opacitySlider?t.toCssRgba():t.toCss()),this._triggerSelect(t),t=t.toHSV(),this._hsvHandle.css({left:t.s*i.width()+"px",top:(1-t.v)*i.height()+"px"}),this._hueElements.css(h,o.fromHSV(t.h,1,1,1).toCss()),this._hueSlider.value(t.h),this._opacitySlider&&this._opacitySlider.value(100*t.a))},_selectOnHide:function(){return this.options.buttons?null:this._getHSV()},_template:n.template('# if (preview) { #<div class="k-selected-color"><div class="k-selected-color-display"><div class="k-color-input"><input class="k-color-value" # if (clearButton && !_standalone) { #placeholder="#: messages.noColor #" # } ##= !data.input ? \'style="visibility: hidden;"\' : "" #># if (clearButton && !_standalone) { #<span class="k-clear-color k-button k-bare" title="#: messages.clearColor #"></span># } #</div></div></div># } ## if (clearButton && !_standalone && !preview) { #<div class="k-clear-color-container"><span class="k-clear-color k-button k-bare">#: messages.clearColor #</span></div># } #<div class="k-hsv-rectangle"><div class="k-hsv-gradient"></div><div class="k-draghandle"></div></div><input class="k-hue-slider" /># if (opacity) { #<input class="k-transparency-slider" /># } ## if (buttons) { #<div unselectable="on" class="k-controls"><button class="k-button k-primary apply">#: messages.apply #</button> <button class="k-button cancel">#: messages.cancel #</button></div># } #')});function y(t,e,i){var n=(t=Array.prototype.slice.call(t)).length,s=t.indexOf(e);return s<0?i<0?t[n-1]:t[0]:((s+=i)<0?s+=n:s%=n,t[s])}var k=r.extend({init:function(e,i){var n=this;r.fn.init.call(n,e,i),i=n.options;var s=(e=n.element).attr("value")||e.val();s=a(s||i.value,!0),n._value=i.value=s;var o=n.wrapper=t(n._template(i));if(e.hide().after(o),e.is("input")){e.appendTo(o);var l=e.closest("label"),h=e.attr("id");h&&(l=l.add('label[for="'+h+'"]')),l.click(function(t){n.open(),t.preventDefault()})}n._tabIndex=e.attr("tabIndex")||0,n.enable(!e.attr("disabled"));var u=e.attr("accesskey");u&&(e.attr("accesskey",null),o.attr("accesskey",u)),n.bind("activate",function(t){t.isDefaultPrevented()||n.toggle()}),n._updateUI(s)},destroy:function(){this.wrapper.off(d).find("*").off(d),this._popup&&(this._selector.destroy(),this._popup.destroy()),this._selector=this._popup=this.wrapper=null,r.fn.destroy.call(this)},enable:function(t){var e=this,i=e.wrapper,n=i.children(".k-picker-wrap"),s=n.find(".k-select");0===arguments.length&&(t=!0),e.element.attr("disabled",!t),i.attr("aria-disabled",!t),s.off(d).on("mousedown"+d,x),i.addClass("k-state-disabled").removeAttr("tabIndex").add("*",i).off(d),t?i.removeClass("k-state-disabled").attr("tabIndex",e._tabIndex).on("mouseenter"+d,function(){n.addClass("k-state-hover")}).on("mouseleave"+d,function(){n.removeClass("k-state-hover")}).on("focus"+d,function(){n.addClass("k-state-focused")}).on("blur"+d,function(){n.removeClass("k-state-focused")}).on(f,C(e._keydown,e)).on(p,".k-select",C(e.toggle,e)).on(p,e.options.toolIcon?".k-tool-icon":".k-selected-color",function(){e.trigger("activate")}):e.close()},_template:n.template('<span role="textbox" aria-haspopup="true" class="k-widget k-colorpicker k-header"><span class="k-picker-wrap k-state-default"># if (toolIcon) { #<span class="k-icon k-tool-icon #= toolIcon #"><span class="k-selected-color"></span></span># } else { #<span class="k-selected-color"><span class="k-icon k-i-line" style="display: none;"></span></span># } #<span class="k-select" unselectable="on" aria-label="select"><span class="k-icon k-i-arrow-60-down"></span></span></span></span>'),options:{name:"ColorPicker",palette:null,columns:10,toolIcon:null,value:null,messages:c,opacity:!1,buttons:!0,preview:!0,clearButton:!1,ARIATemplate:'Current selected color is #=data || ""#'},events:["activate","change","select","open","close"],open:function(){this.element.prop("disabled")||this._getPopup().open()},close:function(){var t=this._selector&&this._selector.options||{};t._closing=!0,this._getPopup().close(),delete t._closing},toggle:function(){this.element.prop("disabled")||this._getPopup().toggle()},_noColorIcon:function(){return this.wrapper.find(".k-picker-wrap > .k-selected-color > .k-icon.k-i-line")},color:_.fn.color,value:_.fn.value,_select:_.fn._select,_triggerSelect:_.fn._triggerSelect,_isInputTypeColor:function(){var t=this.element[0];return/^input$/i.test(t.tagName)&&/^color$/i.test(t.type)},_updateUI:function(t){var e="";t&&(e=this._isInputTypeColor()||1==t.a?t.toCss():t.toCssRgba(),this.element.val(e)),this._ariaTemplate||(this._ariaTemplate=n.template(this.options.ARIATemplate)),this.wrapper.attr("aria-label",this._ariaTemplate(e)),this._triggerSelect(t),this.wrapper.find(".k-selected-color").css(h,t?t.toDisplay():"#ffffff"),this._noColorIcon()[e?"hide":"show"]()},_keydown:function(t){var e=t.keyCode;this._getPopup().visible()?(e==l.ESC?this._selector._cancel():this._selector._keydown(t),x(t)):e!=l.ENTER&&e!=l.DOWN||(this.open(),x(t))},_getPopup:function(){var e=this,i=e._popup;if(!i){var s,r=e.options;s=r.palette?w:b,r._standalone=!1,delete r.select,delete r.change,delete r.cancel;var o=n.guid(),l=e._selector=new s(t('<div id="'+o+'"/>').appendTo(document.body),r);e.wrapper.attr("aria-owns",o),e._popup=i=l.wrapper.kendoPopup({anchor:e.wrapper,adjustSize:{width:5,height:0}}).data("kendoPopup"),l.element.find(".k-clear-color").kendoButton({icon:"reset-color",click:function(t){l.options._clearedColor=!0,e.value(null),e.element.val(null),e._updateUI(null),l._colorAsText.val(""),l._hsvHandle.css({top:"0px",left:"0px"}),l._selectedColor.css(h,"#ffffff"),e.trigger("change",{value:e.value()}),t.preventDefault()}}),l.bind({select:function(t){e._updateUI(a(t.value)),delete l.options._clearedColor},change:function(){l.options._clearedColor||e._select(l.color()),e.close()},cancel:function(){l.options._clearedColor&&!e.value()&&l.value()&&e._select(l.color(),!0),e.close()}}),i.bind({close:function(t){if(e.trigger("close"))t.preventDefault();else{e.wrapper.children(".k-picker-wrap").removeClass("k-state-focused");var i=l._selectOnHide(),n=l.value(),s=e.value(),r=l.options;i?r._clearedColor&&!s||e._select(i):(setTimeout(function(){e.wrapper&&!e.wrapper.is("[unselectable='on']")&&e.wrapper.focus()}),!r._closing&&r._clearedColor&&!s&&n?e._select(n,!0):e._updateUI(e.color()))}},open:function(t){e.trigger("open")?t.preventDefault():e.wrapper.children(".k-picker-wrap").addClass("k-state-focused")},activate:function(){l._select(e.color(),!0),l.focus(),e.wrapper.children(".k-picker-wrap").addClass("k-state-focused")}})}return i}});function x(t){t.preventDefault()}function C(t,e){return function(){return t.apply(e,arguments)}}s.plugin(w),s.plugin(b),s.plugin(k)}(jQuery,parseInt),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.maskedtextbox",["kendo.core"],function(){return function(t,e){var i=window,n=i.Math.min,s=i.kendo,r=s.caret,a=s.keys,o=s.ui,l=o.Widget,h=".kendoMaskedTextBox",u=t.proxy,c=window.setTimeout;function d(t){return t+h}var p=d(s.support.propertyChangeEvent?"propertychange":"input"),f=l.extend({init:function(e,i){var n,a=this;l.fn.init.call(a,e,i),a._rules=t.extend({},a.rules,a.options.rules),e=a.element,n=e[0],a._wrapper(),a._tokenize(),a._form(),a.element.addClass("k-textbox").attr("autocomplete","off").on("focus"+h,function(){var t=n.value;t?a._togglePrompt(!0):n.value=a._old=a._emptyMask,a._oldValue=t,a._timeoutId=c(function(){r(e,0,t?a._maskLength:0)})}).on("focusout"+h,function(){var t=e.val();clearTimeout(a._timeoutId),n.value=a._old="",t!==a._emptyMask&&(n.value=a._old=t),a._change(),a._togglePrompt()}),e.is("[disabled]")||t(a.element).parents("fieldset").is(":disabled")?a.enable(!1):a.readonly(e.is("[readonly]")),a.value(a.options.value||e.val()),a._validationIcon=t("<span class='k-icon k-i-warning'></span>").insertAfter(e),s.notify(a)},options:{name:"MaskedTextBox",clearPromptChar:!1,unmaskOnPost:!1,promptChar:"_",culture:"",rules:{},value:"",mask:""},events:["change"],rules:{0:/\d/,9:/\d|\s/,"#":/\d|\s|\+|\-/,L:/[a-zA-Z]/,"?":/[a-zA-Z]|\s/,"&":/\S/,C:/./,A:/[a-zA-Z0-9]/,a:/[a-zA-Z0-9]|\s/},setOptions:function(e){l.fn.setOptions.call(this,e),this._rules=t.extend({},this.rules,this.options.rules),this._tokenize(),this._unbindInput(),this._bindInput(),this.value(this.element.val())},destroy:function(){this.element.off(h),this._formElement&&(this._formElement.off("reset",this._resetHandler),this._formElement.off("submit",this._submitHandler)),l.fn.destroy.call(this)},raw:function(){return this._unmask(this.element.val(),0).replace(new RegExp(this.options.promptChar,"g"),"")},value:function(t){var e=this.element,i=this._emptyMask;return void 0===t?this.element.val():(null===t&&(t=""),i?(t=this._unmask(t+""),e.val(t?i:""),this._mask(0,this._maskLength,t),this._unmaskedValue=null,t=e.val(),this._oldValue=t,void(s._activeElement()!==e&&(t===i?e.val(""):this._togglePrompt()))):(this._oldValue=t,void e.val(t)))},_togglePrompt:function(t){var e=this.element[0],i=e.value;this.options.clearPromptChar&&(i=t?this._oldValue:i.replace(new RegExp(this.options.promptChar,"g")," "),e.value=this._old=i)},readonly:function(t){this._editable({readonly:void 0===t||t,disable:!1})},enable:function(t){this._editable({readonly:!1,disable:!(t=void 0===t||t)})},_bindInput:function(){if(this._maskLength&&(this.options.$angular&&this.element.off("input"),this.element.on(d("keydown"),u(this._keydown,this)).on(d("drop"),u(this._drop,this)).on(d("change"),u(this._trackChange,this)).on(p,u(this._inputHandler,this)),s.support.browser.msie)){var t=s.support.browser.version;if(t>8&&t<11){var e=[d("mouseup"),d("drop"),d("keydown"),d("paste")].join(" ");this.element.on(e,u(this._legacyIEInputHandler,this))}}},_unbindInput:function(){var t=[p,d("keydown"),d("mouseup"),d("drop"),d("paste")].join(" ");this.element.off(t)},_editable:function(t){var e=this.element,i=this.wrapper,n=t.disable,s=t.readonly;this._unbindInput(),s||n?(e.attr("disabled",n).attr("readonly",s),i.toggleClass("k-state-disabled",n)):(e.removeAttr("disabled").removeAttr("readonly"),i.removeClass("k-state-disabled"),this._bindInput())},_change:function(){var t=this.value();t!==this._oldValue?(this._oldValue=t,this.trigger("change"),this.element.trigger("change")):""===t&&this.__changing&&this.element.trigger("change")},inputChange:function(t){var e=this._old,i=this.element[0],a=i.value,o=r(i)[1],l=a.length-e.length,h=s.support.mobileOS;if(!(this.__dropping&&l<0)){-1===l&&h.android&&"chrome"===h.browser&&(t=!0);var u=n(o,function(t,e){for(var i=0;i<e.length&&t[i]===e[i];)i++;return i}(a,e)),c=a.substring(u,o);i.value=a.substring(0,u)+this._emptyMask.substring(u);var d=this._mask(u,o,c),p=this._trimStartPromptChars(a.substring(o),n(l,d-u)),f=this._unmask(p,e.length-p.length);this._mask(d,d,f),t&&(d=this._findCaretPosBackwards(u)),r(i,d),this.__dropping=!1}},_trimStartPromptChars:function(t,e){for(var i=this.options.promptChar;e-- >0&&0===t.indexOf(i);)t=t.substring(1);return t},_findCaretPosBackwards:function(t){var e=this._find(t,!0);return e<t&&(e+=1),e},_inputHandler:function(){s._activeElement()===this.element[0]&&this.inputChange(this.__backward)},_legacyIEInputHandler:function(t){var e=this,i=e.element[0],n=i.value,s=t.type;e.__pasting="paste"===s,c(function(){"mouseup"===s&&e.__pasting||i.value&&i.value!==n&&e.inputChange(e.__backward)})},_trackChange:function(){var t=this;t.__changing=!0,c(function(){t.__changing=!1})},_form:function(){var e=this,i=e.element,n=i.attr("form"),s=n?t("#"+n):i.closest("form");s[0]&&(e._resetHandler=function(){c(function(){e.value(i[0].value)})},e._submitHandler=function(){e.element[0].value=e._old=e.raw()},e.options.unmaskOnPost&&s.on("submit",e._submitHandler),e._formElement=s.on("reset",e._resetHandler))},_keydown:function(t){var e=t.keyCode;this.__backward=e===a.BACKSPACE,e===a.ENTER&&this._change()},_drop:function(){this.__dropping=!0},_find:function(t,e){var i=this.element.val()||this._emptyMask,n=1;for(!0===e&&(n=-1);t>-1||t<=this._maskLength;){if(i.charAt(t)!==this.tokens[t])return t;t+=n}return-1},_mask:function(t,e,i,n){var a,o,l,h,u=this.element[0],c=u.value||this._emptyMask,d=this.options.promptChar,p=0;for((t=this._find(t,n))>e&&(e=t),o=this._unmask(c.substring(e),e),a=(i=this._unmask(i,t)).length,i&&(o=o.replace(new RegExp("^_{0,"+a+"}"),"")),i+=o,c=c.split(""),l=i.charAt(p);t<this._maskLength;)c[t]=l||d,l=i.charAt(++p),void 0===h&&p>a&&(h=t),t=this._find(t+1);return u.value=this._old=c.join(""),s._activeElement()===u&&(void 0===h&&(h=this._maskLength),r(u,h)),h},_unmask:function(e,i){if(!e)return"";if(this._unmaskedValue===e)return this._unmaskedValue;var n,s;e=(e+"").split("");for(var r=0,a=i||0,o=this.options.promptChar,l=e.length,h=this.tokens.length,u="";a<h&&((n=e[r])===(s=this.tokens[a])||n===o?(u+=n===o?o:"",r+=1,a+=1):"string"!=typeof s?(s&&s.test&&s.test(n)||t.isFunction(s)&&s(n)?(u+=n,a+=1):1===l&&this._blinkInvalidState(),r+=1):a+=1,!(r>=l)););return this._unmaskedValue=u,u},_wrapper:function(){var t=this.element,e=t[0],i=t.wrap("<span class='k-widget k-maskedtextbox'></span>").parent();i[0].style.cssText=e.style.cssText,e.style.width="100%",this.wrapper=i.addClass(e.className)},_blinkInvalidState:function(){this.wrapper.addClass("k-state-invalid"),clearTimeout(this._invalidStateTimeout),this._invalidStateTimeout=c(u(this._removeInvalidState,this),100)},_removeInvalidState:function(){this.wrapper.removeClass("k-state-invalid"),this._invalidStateTimeout=null},_tokenize:function(){for(var t,e,i=[],n=0,r=(this.options.mask||"").split(""),a=r.length,o=0,l="",h=this.options.promptChar,u=s.getCulture(this.options.culture).numberFormat,c=this._rules;o<a;o++)if(e=c[t=r[o]])i[n]=e,l+=h,n+=1;else{"."===t||","===t?t=u[t]:"$"===t?t=u.currency.symbol:"\\"===t&&(t=r[o+=1]);for(var d=0,p=(t=t.split("")).length;d<p;d++)i[n]=t[d],l+=t[d],n+=1}this.tokens=i,this._emptyMask=l,this._maskLength=l.length}});o.plugin(f)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.multiselect",["kendo.list","kendo.mobile.scroller","kendo.virtuallist"],function(){return t=window.kendo.jQuery,e=window.kendo,i=e.ui,n=i.List,s=t.extend({A:65},e.keys),r=e._activeElement,a=e.data.ObservableArray,o=t.proxy,l="k-state-focused",h="k-state-disabled",c="mouseenter"+(u=".kendoMultiSelect"),d="mouseleave"+u,p=/"/g,f=t.isArray,m=["font-family","font-size","font-stretch","font-style","font-weight","letter-spacing","text-transform","line-height"],g=n.extend({init:function(i,s){var r;this.ns=u,n.fn.init.call(this,i,s),this._optionsMap={},this._customOptions={},this._wrapper(),this._tagList(),this._input(),this._textContainer(),this._loader(),this._clearButton(),this._tabindex(this.input),i=this.element.attr("multiple","multiple").hide(),(s=this.options).placeholder||(s.placeholder=i.data("placeholder")),(r=i.attr("id"))&&(this._tagID=r+"_tag_active",r+="_taglist",this.tagList.attr("id",r),this.input.attr("aria-describedby",r)),this._initialOpen=!0,this._aria(r),this._dataSource(),this._ignoreCase(),this._popup(),this._tagTemplate(),this.requireValueMapper(this.options),this._initList(),this._reset(),this._enable(),this._placeholder(),s.autoBind?this.dataSource.fetch():s.value&&this._preselect(s.value),t(this.element).parents("fieldset").is(":disabled")&&this.enable(!1),e.notify(this),this._toggleCloseVisibility()},options:{name:"MultiSelect",tagMode:"multiple",enabled:!0,autoBind:!0,autoClose:!0,highlightFirst:!0,dataTextField:"",dataValueField:"",filter:"startswith",ignoreCase:!0,minLength:1,enforceMinLength:!1,delay:100,value:null,maxSelectedItems:null,placeholder:"",height:200,animation:{},virtual:!1,itemTemplate:"",tagTemplate:"",groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",clearButton:!0,autoWidth:!1},events:["open","close","change","select","deselect","filtering","dataBinding","dataBound"],setDataSource:function(t){this.options.dataSource=t,this._state="",this._dataSource(),this.persistTagList=!1,this.listView.setDataSource(this.dataSource),this.options.autoBind&&this.dataSource.fetch()},setOptions:function(t){var e=this._listOptions(t);n.fn.setOptions.call(this,t),this.listView.setOptions(e),this._accessors(),this._aria(this.tagList.attr("id")),this._tagTemplate(),this._placeholder(),this._clearButton()},currentTag:function(t){if(void 0===t)return this._currentTag;this._currentTag&&(this._currentTag.removeClass(l).removeAttr("id"),this.input.removeAttr("aria-activedescendant")),t&&(t.addClass(l).attr("id",this._tagID),this.input.attr("aria-activedescendant",this._tagID)),this._currentTag=t},dataItems:function(){return this.listView.selectedDataItems()},destroy:function(){var t=this.ns;clearTimeout(this._busy),clearTimeout(this._typingTimeout),this.wrapper.off(t),this.tagList.off(t),this.input.off(t),this._clear.off(t),n.fn.destroy.call(this)},_activateItem:function(){this.popup.visible()&&n.fn._activateItem.call(this),this.currentTag(null)},_listOptions:function(i){var s=n.fn._listOptions.call(this,t.extend(i,{selectedItemChange:o(this._selectedItemChange,this),selectable:"multiple"})),r=this.options.itemTemplate||this.options.template,a=s.itemTemplate||r||s.template;return a||(a="#:"+e.expr(s.dataTextField,"data")+"#"),s.template=a,s},_setListValue:function(){n.fn._setListValue.call(this,this._initialValues.slice(0))},_listChange:function(t){var e=this.dataSource.flatView(),i=this._optionsMap,n=this._value;"rebind"===this._state&&(this._state="");for(var s=0;s<t.added.length;s++)if(void 0===i[n(t.added[s].dataItem)]){this._render(e);break}this._selectValue(t.added,t.removed)},_selectedItemChange:function(t){var e,i,n=t.items;for(i=0;i<n.length;i++)e=n[i],this.tagList.children().eq(e.index).children("span:first").html(this.tagTextTemplate(e.item))},_wrapperMousedown:function(i){var n="input"!==i.target.nodeName.toLowerCase(),s=t(i.target),a=s.hasClass("k-select")||s.hasClass("k-icon");a&&(a=!s.closest(".k-select").children(".k-i-arrow-60-down").length),!n||a&&e.support.mobileOS||i.preventDefault(),a||(this.input[0]!==r()&&n&&this.input.focus(),1===this.options.minLength&&this.open())},_inputFocus:function(){this._placeholder(!1),this.wrapper.addClass(l)},_inputFocusout:function(){clearTimeout(this._typingTimeout),this.wrapper.removeClass(l),this._placeholder(!this.listView.selectedDataItems()[0],!0),this.close(),"filter"===this._state&&(this._state="accept",this.listView.skipUpdate(!0)),this.listView.bound()&&this.listView.isFiltered()&&(this.persistTagList=!0,this._clearFilter()),this.element.blur()},_removeTag:function(t,e){var i=this,n=i._state,s=t.index(),r=i.listView,a=r.value()[s],o=i.listView.selectedDataItems()[s],l=i._customOptions[a];if(i.trigger("deselect",{dataItem:o,item:t}))i._close();else{void 0!==l||"accept"!==n&&"filter"!==n||(l=i._optionsMap[a]);var h=function(){i.currentTag(null),e&&i._change(),i._close()};void 0===l?(i.persistTagList=!1,r.select(r.select()[s]).done(h)):(i.element[0].children[l].selected=!1,r.removeAt(s),t.remove(),h())}},_tagListClick:function(e){var i=t(e.currentTarget);i.children(".k-i-arrow-60-down").length||this._removeTag(i.closest("li"),!0)},_clearClick:function(){var e=this;"single"===e.options.tagMode?e.value([]):e.tagList.children().each(function(i,n){e._removeTag(t(n),!1)}),e.input.val(""),e._search(),e.trigger("change"),e.focus(),e._hideClear(),"filter"===e._state&&(e._state="accept")},_editable:function(e){var i=e.disable,n=e.readonly,s=this.wrapper.off(u),r=this.tagList.off(u),a=this.element.add(this.input.off(u));n||i?(i?s.addClass(h):s.removeClass(h),a.attr("disabled",i).attr("readonly",n).attr("aria-disabled",i)):(s.removeClass(h).on("mouseenter.kendoMultiSelect mouseleave.kendoMultiSelect",this._toggleHover).on("mousedown"+u+" touchend"+u,o(this._wrapperMousedown,this)),this.input.on("keydown.kendoMultiSelect",o(this._keydown,this)).on("paste"+u,o(this._search,this)).on("input"+u,o(this._search,this)).on("focus"+u,o(this._inputFocus,this)).on("focusout"+u,o(this._inputFocusout,this)),this._clear.on("click"+u,o(this._clearClick,this)),a.removeAttr("disabled").removeAttr("readonly").attr("aria-disabled",!1),r.on(c,"li",function(){t(this).addClass("k-state-hover")}).on(d,"li",function(){t(this).removeClass("k-state-hover")}).on("click.kendoMultiSelect","li.k-button .k-select",o(this._tagListClick,this)))},_close:function(){this.options.autoClose?this.close():this.popup.position()},_filterSource:function(t,e){e||(e=this._retrieveData),this._retrieveData=!1,n.fn._filterSource.call(this,t,e)},close:function(){this._activeItem=null,this.input.removeAttr("aria-activedescendant"),this.popup.close()},open:function(){this._request&&(this._retrieveData=!1),this._retrieveData||!this.listView.bound()||"accept"===this._state?(this._open=!0,this._state="rebind",this.listView.skipUpdate(!0),this.persistTagList=!(this._initialOpen&&!this.listView.bound()),this._filterSource(),this._focusItem()):this._allowOpening()&&(!this._initialOpen||this.options.autoBind||this.options.virtual||!this.options.value||t.isPlainObject(this.options.value[0])||this.value(this._initialValues),this.popup._hovered=!0,this._initialOpen=!1,this.popup.open(),this._focusItem())},toggle:function(t){this[(t=void 0!==t?t:!this.popup.visible())?"open":"close"]()},refresh:function(){this.listView.refresh()},_listBound:function(){var t=this.dataSource.flatView(),e=this.listView.skip();this._render(t),this._renderFooter(),this._renderNoData(),this._toggleNoData(!t.length),this._resizePopup(),this._open&&(this._open=!1,this.toggle(this._allowOpening())),this.popup.position(),!this.options.highlightFirst||void 0!==e&&0!==e||this.listView.focusFirst(),this._touchScroller&&this._touchScroller.reset(),this._hideBusy(),this._makeUnselectable(),this.trigger("dataBound")},_inputValue:function(){var t=this.input.val();return this.options.placeholder===t&&(t=""),t},value:function(t){var e=this.listView,i=e.value().slice(),n=this.options.maxSelectedItems,s=e.bound()&&e.isFiltered();if(void 0===t)return i;this.persistTagList=!1,this.requireValueMapper(this.options,t),t=this._normalizeValues(t),null!==n&&t.length>n&&(t=t.slice(0,n)),s&&this._clearFilter(),e.value(t),this._old=e.value(),s||this._fetchData(),this._toggleCloseVisibility()},_preselect:function(i,n){f(i)||i instanceof e.data.ObservableArray||(i=[i]),(t.isPlainObject(i[0])||i[0]instanceof e.data.ObservableObject||!this.options.dataValueField)&&(this.dataSource.data(i),this.value(n||this._initialValues),this._retrieveData=!0)},_setOption:function(t,e){var i=this.element[0].children[this._optionsMap[t]];i&&(i.selected=e)},_fetchData:function(){var t=this,e=!!t.dataSource.view().length;0===t.listView.value().length||t._request||(t._retrieveData||!t._fetch&&!e)&&(t._fetch=!0,t._retrieveData=!1,t.dataSource.read().done(function(){t._fetch=!1}))},_isBound:function(){return this.listView.bound()&&!this._retrieveData},_dataSource:function(){var t=this.element,i=this.options,n=i.dataSource||{};(n=f(n)?{data:n}:n).select=t,n.fields=[{field:i.dataTextField},{field:i.dataValueField}],this.dataSource&&this._refreshHandler?this._unbindDataSource():(this._progressHandler=o(this._showBusy,this),this._errorHandler=o(this._hideBusy,this)),this.dataSource=e.data.DataSource.create(n).bind("progress",this._progressHandler).bind("error",this._errorHandler)},_reset:function(){var e=this,i=e.element,n=i.attr("form"),s=n?t("#"+n):i.closest("form");s[0]&&(e._resetHandler=function(){setTimeout(function(){e.value(e._initialValues),e._placeholder()})},e._form=s.on("reset",e._resetHandler))},_initValue:function(){var t=this.options.value||this.element.val();this._old=this._initialValues=this._normalizeValues(t)},_normalizeValues:function(e){var i=this;return null===e?e=[]:e&&t.isPlainObject(e)?e=[i._value(e)]:e&&t.isPlainObject(e[0])?e=t.map(e,function(t){return i._value(t)}):f(e)||e instanceof a?f(e)&&(e=e.slice()):e=[e],e},_change:function(){var t=this.value();(function(t,e){var i;if(null===t&&null!==e||null!==t&&null===e)return!1;if((i=t.length)!==e.length)return!1;for(;i--;)if(t[i]!==e[i])return!1;return!0})(t,this._old)||(this._old=t.slice(),this.trigger("change"),this.element.trigger("change")),this._toggleCloseVisibility()},_click:function(t){var e=this,i=t.item;t.preventDefault(),e._select(i).done(function(){e._activeItem=i,e._change(),e._close()})},_getActiveItem:function(){return this._activeItem||t(this.listView.items()[this._getSelectedIndices().length-1])||this.listView.focus()},_getSelectedIndices:function(){return this.listView._selectedIndices||this.listView._selectedIndexes},_keydown:function(i){var n,r=this,a=i.keyCode,o=r._currentTag,l=r.listView,h=r.input.val(),u=e.support.isRtl(r.wrapper),c=r.popup.visible(),d=0;if(a===s.DOWN){if(i.preventDefault(),!c)return r.open(),void(l.focus()||l.focusFirst());l.focus()?(!r._activeItem&&i.shiftKey&&(r._activeItem=l.focus(),d=-1),n=l.getElementIndex(r._getActiveItem()[0]),l.focusNext(),l.focus()?i.shiftKey&&r._selectRange(n,l.getElementIndex(l.focus()[0])+d):l.focusLast()):l.focusFirst()}else if(a===s.UP)c&&(!r._activeItem&&i.shiftKey&&(r._activeItem=l.focus(),d=1),n=l.getElementIndex(r._getActiveItem()[0]),l.focusPrev(),l.focus()?i.shiftKey&&r._selectRange(n,l.getElementIndex(l.focus()[0])+d):r.close()),i.preventDefault();else if(a===s.LEFT&&!u||a===s.RIGHT&&u)h||(o=o?o.prev():t(r.tagList[0].lastChild))[0]&&r.currentTag(o);else if(a===s.RIGHT&&!u||a===s.LEFT&&u)!h&&o&&(o=o.next(),r.currentTag(o[0]?o:null));else if(i.ctrlKey&&!i.altKey&&a===s.A&&c)this._getSelectedIndices().length===l.items().length&&(r._activeItem=null),l.items().length&&r._selectRange(0,l.items().length-1);else if(a===s.ENTER&&c){if(!l.focus())return;if(i.preventDefault(),l.focus().hasClass("k-state-selected"))return void r._close();r._select(l.focus()).done(function(){r._change(),r._close()})}else if(a===s.SPACEBAR&&i.ctrlKey&&c)r._activeItem&&l.focus()&&l.focus()[0]===r._activeItem[0]&&(r._activeItem=null),t(l.focus()).hasClass("k-state-selected")||(r._activeItem=l.focus()),r._select(l.focus()).done(function(){r._change()}),i.preventDefault();else if(a===s.SPACEBAR&&i.shiftKey&&c){var p=l.getElementIndex(r._getActiveItem()),f=l.getElementIndex(l.focus());void 0!==p&&void 0!==f&&r._selectRange(p,f),i.preventDefault()}else if(a===s.ESC)c?i.preventDefault():(r.tagList.children().each(function(e,i){r._removeTag(t(i),!1)}),r.trigger("change")),r.close();else if(a===s.HOME)c?l.focus()?(i.ctrlKey&&i.shiftKey&&r._selectRange(l.getElementIndex(l.focus()[0]),0),l.focusFirst()):r.close():h||(o=r.tagList[0].firstChild)&&r.currentTag(t(o));else if(a===s.END)c?l.focus()?(i.ctrlKey&&i.shiftKey&&r._selectRange(l.getElementIndex(l.focus()[0]),l.element.children().length-1),l.focusLast()):r.close():h||(o=r.tagList[0].lastChild)&&r.currentTag(t(o));else if(a!==s.DELETE&&a!==s.BACKSPACE||h)if(!r.popup.visible()||a!==s.PAGEDOWN&&a!==s.PAGEUP)clearTimeout(r._typingTimeout),setTimeout(function(){r._scale()}),r._search();else{i.preventDefault();var m=a===s.PAGEDOWN?1:-1;l.scrollWith(m*l.screenHeight())}else{if(r._state="accept","single"===r.options.tagMode)return l.value([]),r._change(),void r._close();a!==s.BACKSPACE||o||(o=t(r.tagList[0].lastChild)),o&&o[0]&&r._removeTag(o,!0)}},_hideBusy:function(){clearTimeout(this._busy),this.input.attr("aria-busy",!1),this._loading.addClass("k-hidden"),this._request=!1,this._busy=null,this._toggleCloseVisibility()},_showBusyHandler:function(){this.input.attr("aria-busy",!0),this._loading.removeClass("k-hidden"),this._hideClear()},_showBusy:function(){this._request=!0,this._busy||(this._busy=setTimeout(o(this._showBusyHandler,this),100))},_placeholder:function(t,i){var n=this.input,s=r(),a=this.options.placeholder,o=n.val(),l=n[0]===s,h=o.length;l&&!this.options.autoClose&&o!==a||(h=0,o=""),void 0===t&&(t=!1,n[0]!==s&&(t=!this.listView.selectedDataItems()[0])),this._prev=o,n.toggleClass("k-readonly",t).val(t?a:o),l&&!i&&e.caret(n[0],h,h),this._scale()},_scale:function(){var t,e=this.wrapper.find(".k-multiselect-wrap"),i=e.width(),n=this._span.text(this.input.val());e.is(":visible")?t=n.width()+25:(n.appendTo(document.documentElement),i=t=n.width()+25,n.appendTo(e)),this.input.width(t>i?i:t)},_option:function(t,i,n){var s="<option";return void 0!==t&&(-1!==(t+="").indexOf('"')&&(t=t.replace(p,"&quot;")),s+=' value="'+t+'"'),n&&(s+=" selected"),s+=">",void 0!==i&&(s+=e.htmlEncode(i)),s+"</option>"},_render:function(t){var e,i,n,s,r=this.listView.selectedDataItems(),a=this.listView.value(),o=t.length,l="";a.length!==r.length&&(r=this._buildSelectedItems(a));var h={},u={};for(s=0;s<o;s++)i=t[s],n=this._value(i),-1!==(e=this._selectedItemIndex(n,r))&&r.splice(e,1),u[n]=s,l+=this._option(n,this._text(i),-1!==e);if(r.length)for(s=0;s<r.length;s++)i=r[s],h[n=this._value(i)]=o,u[n]=o,o+=1,l+=this._option(n,this._text(i),!0);this._customOptions=h,this._optionsMap=u,this.element.html(l)},_buildSelectedItems:function(t){for(var e,i=this.options.dataValueField,n=this.options.dataTextField,s=[],r=0;r<t.length;r++)(e={})[i]=t[r],e[n]=t[r],s.push(e);return s},_selectedItemIndex:function(t,e){for(var i=this._value,n=0;n<e.length;n++)if(t===i(e[n]))return n;return-1},_search:function(){var t=this;t._typingTimeout=setTimeout(function(){var e=t._inputValue();t._prev!==e&&(t._prev=e,t.search(e),t._toggleCloseVisibility())},t.options.delay)},_toggleCloseVisibility:function(){this.value().length||this.input.val()&&this.input.val()!==this.options.placeholder?this._showClear():this._hideClear()},_allowOpening:function(){return this._allowSelection()&&n.fn._allowOpening.call(this)},_allowSelection:function(){var t=this.options.maxSelectedItems;return null===t||t>this.listView.value().length},_angularTagItems:function(e){var i=this;i.angular(e,function(){return{elements:i.tagList[0].children,data:t.map(i.dataItems(),function(t){return{dataItem:t}})}})},updatePersistTagList:function(t,e){this.persistTagList.added&&this.persistTagList.added.length===e.length&&this.persistTagList.removed&&this.persistTagList.removed.length===t.length?this.persistTagList=!1:this.persistTagList={added:t,removed:e}},_selectValue:function(t,e){var i,n,s,r=this.value(),a=this.dataSource.total(),o=this.tagList,l=this._value;if(this.persistTagList)this.updatePersistTagList(t,e);else{if(this._angularTagItems("cleanup"),"multiple"===this.options.tagMode){for(s=e.length-1;s>-1;s--)i=e[s],o.children().length&&(o[0].removeChild(o[0].children[i.position]),this._setOption(l(i.dataItem),!1));for(s=0;s<t.length;s++)n=t[s],o.append(this.tagTemplate(n.dataItem)),this._setOption(l(n.dataItem),!0)}else{for((!this._maxTotal||this._maxTotal<a)&&(this._maxTotal=a),o.html(""),r.length&&o.append(this.tagTemplate({values:r,dataItems:this.dataItems(),maxTotal:this._maxTotal,currentTotal:a})),s=e.length-1;s>-1;s--)this._setOption(l(e[s].dataItem),!1);for(s=0;s<t.length;s++)this._setOption(l(t[s].dataItem),!0)}this._angularTagItems("compile"),this._placeholder()}},_select:function(e){var i=t.Deferred().resolve();if(!e)return i;var n=this,s=n.listView,r=s.dataItemByIndex(s.getElementIndex(e)),a=e.hasClass("k-state-selected");return"rebind"===n._state&&(n._state=""),n._allowSelection()||a?n.trigger(a?"deselect":"select",{dataItem:r,item:e})?(n._close(),i):(n.persistTagList=!1,s.select(e).done(function(){n._placeholder(),"filter"===n._state&&(n._state="accept",s.skipUpdate(!0))})):i},_selectRange:function(e,i){var n,s=this,r=this.listView,a=this.options.maxSelectedItems,o=this._getSelectedIndices().slice(),l=[],h=function(e){r.select(e).done(function(){e.forEach(function(e){var i=r.dataItemByIndex(e),n=r.element.children()[e],a=t(n).hasClass("k-state-selected");s.trigger(a?"select":"deselect",{dataItem:i,item:t(n)})}),s._change()})};if(o.length-1==i-e)return h(o);if(e<i)for(n=e;n<=i;n++)l.push(n);else for(n=e;n>=i;n--)l.push(n);for(null!==a&&l.length>a&&(l=l.slice(0,a)),n=0;n<l.length;n++){var u=l[n];-1==this._getSelectedIndices().indexOf(u)?o.push(u):o.splice(o.indexOf(u),1)}return o.length?(s.persistTagList=!1,h(o)):void 0},_input:function(){var e=this.element,i=e[0].accessKey,n=this._innerWrapper.children("input.k-input");n[0]||(n=t('<input class="k-input" style="width: 25px" />').appendTo(this._innerWrapper)),e.removeAttr("accesskey"),this._focused=this.input=n.attr({accesskey:i,autocomplete:"off",role:"listbox",title:e[0].title,"aria-expanded":!1})},_tagList:function(){var e=this._innerWrapper.children("ul");e[0]||(e=t('<ul role="listbox" unselectable="on" class="k-reset"/>').appendTo(this._innerWrapper)),this.tagList=e},_tagTemplate:function(){var t,i=this.options,n=i.tagTemplate,s=i.dataSource,r="multiple"===i.tagMode;this.element[0].length&&!s&&(i.dataTextField=i.dataTextField||"text",i.dataValueField=i.dataValueField||"value"),t=r?e.template("#:"+e.expr(i.dataTextField,"data")+"#",{useWithBlock:!1}):e.template("#:values.length# item(s) selected"),this.tagTextTemplate=n=n?e.template(n):t,this.tagTemplate=function(t){return'<li class="k-button" unselectable="on"><span unselectable="on">'+n(t)+'</span><span unselectable="on" aria-label="'+(r?"delete":"open")+'" class="k-select"><span class="k-icon '+(r?"k-i-close":"k-i-arrow-60-down")+'"></span></span></li>'}},_loader:function(){this._loading=t('<span class="k-icon k-i-loading k-hidden"></span>').insertAfter(this.input)},_clearButton:function(){n.fn._clearButton.call(this),this.options.clearButton&&(this._clear.insertAfter(this.input),this.wrapper.addClass("k-multiselect-clearable"))},_textContainer:function(){var i=e.getComputedStyles(this.input[0],m);i.position="absolute",i.visibility="hidden",i.top=-3333,i.left=-3333,this._span=t("<span/>").css(i).appendTo(this.wrapper)},_wrapper:function(){var e=this.element,i=e.parent("span.k-multiselect");i[0]||((i=e.wrap('<div class="k-widget k-multiselect" unselectable="on" />').parent())[0].style.cssText=e[0].style.cssText,i[0].title=e[0].title,t('<div class="k-multiselect-wrap k-floatwrap" unselectable="on" />').insertBefore(e)),this.wrapper=i.addClass(e[0].className).css("display",""),this._innerWrapper=t(i[0].firstChild)}}),i.plugin(g),window.kendo;var t,e,i,n,s,r,a,o,l,h,u,c,d,p,f,m,g}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.numerictextbox",["kendo.core","kendo.userevents"],function(){return function(t,e){var i=window.kendo,n=i.caret,s=i.keys,r=i.ui,a=r.Widget,o=i._activeElement,l=i._extractFormat,h=i.parseFloat,u=i.support.placeholder,c=i.getCulture,d=".kendoNumericTextBox",p="mouseenter"+d+" mouseleave.kendoNumericTextBox",f="k-state-default",m=".",g="k-icon",_="k-state-selected",v="k-state-disabled",w=/^(-)?(\d*)$/,b=t.proxy,y=t.extend,k=a.extend({init:function(e,n){var s,r,o,h,u=this,c=n&&void 0!==n.step;a.fn.init.call(u,e,n),n=u.options,e=u.element.on("focusout"+d,b(u._focusout,u)).attr("role","spinbutton"),n.placeholder=n.placeholder||e.attr("placeholder"),s=u.min(e.attr("min")),r=u.max(e.attr("max")),o=u._parse(e.attr("step")),null===n.min&&null!==s&&(n.min=s),null===n.max&&null!==r&&(n.max=r),c||null===o||(n.step=o),u._initialOptions=y({},n),u._reset(),u._wrapper(),u._arrows(),u._validation(),u._input(),i.support.mobileOS?u._text.on("touchend"+d+" focus"+d,function(){i.support.browser.edge?u._text.one("focus"+d,function(){u._toggleText(!1),e.focus()}):(u._toggleText(!1),e.focus())}):u._text.on("focus"+d,b(u._click,u)),e.attr("aria-valuemin",null!==n.min?n.min*n.factor:n.min).attr("aria-valuemax",null!==n.max?n.max*n.factor:n.max),n.format=l(n.format),h=n.value,u.value(null!==h?h:e.val()),e.is("[disabled]")||t(u.element).parents("fieldset").is(":disabled")?u.enable(!1):u.readonly(e.is("[readonly]")),i.notify(u)},options:{name:"NumericTextBox",decimals:null,restrictDecimals:!1,min:null,max:null,value:null,step:1,round:!0,culture:"",format:"n",spinners:!0,placeholder:"",factor:1,upArrowText:"Increase value",downArrowText:"Decrease value"},events:["change","spin"],_editable:function(t){var e=this,i=e.element,n=t.disable,s=t.readonly,r=e._text.add(i),a=e._inputWrapper.off(p);e._toggleText(!0),e._upArrowEventHandler.unbind("press"),e._downArrowEventHandler.unbind("press"),i.off("keydown"+d).off("keypress"+d).off("keyup"+d).off("paste"+d),s||n?(a.addClass(n?v:f).removeClass(n?f:v),r.attr("disabled",n).attr("readonly",s).attr("aria-disabled",n)):(a.addClass(f).removeClass(v).on(p,e._toggleHover),r.removeAttr("disabled").removeAttr("readonly").attr("aria-disabled",!1),e._upArrowEventHandler.bind("press",function(t){t.preventDefault(),e._spin(1),e._upArrow.addClass(_)}),e._downArrowEventHandler.bind("press",function(t){t.preventDefault(),e._spin(-1),e._downArrow.addClass(_)}),e.element.on("keydown"+d,b(e._keydown,e)).on("keypress"+d,b(e._keypress,e)).on("keyup"+d,b(e._keyup,e)).on("paste"+d,b(e._paste,e)))},readonly:function(t){this._editable({readonly:void 0===t||t,disable:!1})},enable:function(t){this._editable({readonly:!1,disable:!(t=void 0===t||t)})},setOptions:function(t){a.fn.setOptions.call(this,t),this._arrowsWrap.toggle(this.options.spinners),this._inputWrapper.toggleClass("k-expand-padding",!this.options.spinners),this._text.prop("placeholder",this.options.placeholder),this._placeholder(this.options.placeholder),this.element.attr({"aria-valuemin":null!==this.options.min?this.options.min*this.options.factor:this.options.min,"aria-valuemax":null!==this.options.max?this.options.max*this.options.factor:this.options.max}),this.options.format=l(this.options.format),void 0!==t.value&&this.value(t.value)},destroy:function(){this.element.add(this._text).add(this._upArrow).add(this._downArrow).add(this._inputWrapper).off(d),this._upArrowEventHandler.destroy(),this._downArrowEventHandler.destroy(),this._form&&this._form.off("reset",this._resetHandler),a.fn.destroy.call(this)},min:function(t){return this._option("min",t)},max:function(t){return this._option("max",t)},step:function(t){return this._option("step",t)},value:function(t){if(void 0===t)return this._value;(t=this._parse(t))===this._adjust(t)&&(this._update(t),this._old=this._value)},focus:function(){this._focusin()},_adjust:function(t){var e=this.options,i=e.min,n=e.max;return null===t?t:(null!==i&&t<i?t=i:null!==n&&t>n&&(t=n),t)},_arrows:function(){var e,n=this,s=function(){clearTimeout(n._spinning),e.removeClass(_)},r=n.options,a=r.spinners,o=n.element;(e=o.siblings("."+g))[0]||(e=t(x("increase",r.upArrowText)+x("decrease",r.downArrowText)).insertAfter(o),n._arrowsWrap=e.wrapAll('<span class="k-select"/>').parent()),a||(e.parent().toggle(a),n._inputWrapper.addClass("k-expand-padding")),n._upArrow=e.eq(0),n._upArrowEventHandler=new i.UserEvents(n._upArrow,{release:s}),n._downArrow=e.eq(1),n._downArrowEventHandler=new i.UserEvents(n._downArrow,{release:s})},_validation:function(){var e=this.element;this._validationIcon=t("<span class='"+g+" k-i-warning'></span>").hide().insertAfter(e)},_blur:function(){this._toggleText(!0),this._change(this.element.val())},_click:function(t){var e=this;clearTimeout(e._focusing),e._focusing=setTimeout(function(){var i,s,r,a=t.target,o=n(a)[0],l=a.value.substring(0,o),h=e._format(e.options.format),u=h[","],c=0;u&&(s=new RegExp("\\"+u,"g"),r=new RegExp("([\\d\\"+u+"]+)(\\"+h[m]+")?(\\d+)?")),r&&(i=r.exec(l)),i&&(c=i[0].replace(s,"").length,-1!=l.indexOf("(")&&e._value<0&&c++),e._focusin(),n(e.element[0],c)})},_change:function(t){var e=this.options.factor;e&&1!==e&&null!==(t=i.parseFloat(t))&&(t/=e),this._update(t),t=this._value,this._old!=t&&(this._old=t,this._typing||this.element.trigger("change"),this.trigger("change")),this._typing=!1},_culture:function(t){return t||c(this.options.culture)},_focusin:function(){this._inputWrapper.addClass("k-state-focused"),this._toggleText(!1),this.element[0].focus()},_focusout:function(){clearTimeout(this._focusing),this._inputWrapper.removeClass("k-state-focused").removeClass("k-state-hover"),this._blur(),this._removeInvalidState()},_format:function(t,e){var i=this._culture(e).numberFormat;return(t=t.toLowerCase()).indexOf("c")>-1?i=i.currency:t.indexOf("p")>-1&&(i=i.percent),i},_input:function(){var e,i=this.options,n=this.element.addClass("k-input").show()[0],s=n.accessKey;(e=this.wrapper.find(m+"k-formatted-value"))[0]||(e=t('<input type="text"/>').insertBefore(n).addClass("k-formatted-value"));try{n.setAttribute("type","text")}catch(t){n.type="text"}this._initialTitle=n.title,e[0].title=n.title,e[0].tabIndex=n.tabIndex,e[0].style.cssText=n.style.cssText,e.prop("placeholder",i.placeholder),s&&(e.attr("accesskey",s),n.accessKey=""),this._text=e.addClass(n.className).attr({role:"spinbutton","aria-valuemin":null!==i.min?i.min*i.factor:i.min,"aria-valuemax":null!==i.max?i.max*i.factor:i.max})},_keydown:function(t){var e=t.keyCode;this._key=e,e==s.DOWN?this._step(-1):e==s.UP?this._step(1):e==s.ENTER?this._change(this.element.val()):e!=s.TAB&&(this._typing=!0)},_keypress:function(t){if(0!==t.which&&!t.metaKey&&!t.ctrlKey&&t.keyCode!==s.BACKSPACE&&t.keyCode!==s.ENTER){var e,i=this.options.min,r=this.element,a=n(r),o=a[0],l=a[1],h=String.fromCharCode(t.which),u=this._format(this.options.format),c=this._key===s.NUMPAD_DOT,d=r.val();c&&(h=u[m]),d=d.substring(0,o)+h+d.substring(l),(e=this._numericRegex(u).test(d))&&c?(r.val(d),n(r,o+h.length),t.preventDefault()):(null!==i&&i>=0&&"-"===d.charAt(0)||!e)&&(this._addInvalidState(),t.preventDefault()),this._key=0}},_keyup:function(){this._removeInvalidState()},_addInvalidState:function(){this._inputWrapper.addClass("k-state-invalid"),this._validationIcon.show()},_removeInvalidState:function(){this._inputWrapper.removeClass("k-state-invalid"),this._validationIcon.hide()},_numericRegex:function(t){var e=t[m],i=this.options.decimals,n="*";return e===m&&(e="\\"+e),null===i&&(i=t.decimals),0===i&&this.options.restrictDecimals?w:(this.options.restrictDecimals&&(n="{0,"+i+"}"),this._separator!==e&&(this._separator=e,this._floatRegExp=new RegExp("^(-)?(((\\d+("+e+"\\d"+n+")?)|("+e+"\\d"+n+")))?$")),this._floatRegExp)},_paste:function(t){var e=this,i=t.target,n=i.value,s=e._format(e.options.format);setTimeout(function(){var t=e._parse(i.value);null===t?e._update(n):(i.value=t.toString().replace(m,s[m]),e._adjust(t)===t&&e._numericRegex(s).test(i.value)||e._update(n))})},_option:function(t,e){var i=this.element,n=this.options;if(void 0===e)return n[t];((e=this._parse(e))||"step"!==t)&&(n[t]=e,i.add(this._text).attr("aria-value"+t,e),i.attr(t,e))},_spin:function(t,e){var i=this;e=e||500,clearTimeout(i._spinning),i._spinning=setTimeout(function(){i._spin(t,50)},e),i._step(t)},_step:function(t){var e=this.element,i=this._value,n=this._parse(e.val())||0,s=this.options.decimals||2;o()!=e[0]&&this._focusin(),this.options.factor&&n&&(n/=this.options.factor),n=+(n+this.options.step*t).toFixed(s),n=this._adjust(n),this._update(n),this._typing=!1,i!==n&&this.trigger("spin")},_toggleHover:function(e){t(e.currentTarget).toggleClass("k-state-hover","mouseenter"===e.type)},_toggleText:function(t){this._text.toggle(t),this.element.toggle(!t)},_parse:function(t,e){return h(t,this._culture(e),this.options.format)},_round:function(t,e){return(this.options.round?i._round:C)(t,e)},_update:function(t){var e,n=this.options,s=n.factor,r=n.format,a=n.decimals,o=this._culture(),l=this._format(r,o);null===a&&(a=l.decimals),(e=null!==(t=this._parse(t,o)))&&(t=parseFloat(this._round(t,a),10)),this._value=t=this._adjust(t),this._placeholder(i.toString(t,r,o)),e?(s&&(t=parseFloat(this._round(t*s,a),10)),-1!==(t=t.toString()).indexOf("e")&&(t=this._round(+t,a)),t=t.replace(m,l[m])):t=null,this.element.val(t),this.element.add(this._text).attr("aria-valuenow",t)},_placeholder:function(t){var e=this._text;e.val(t),u||t||e.val(this.options.placeholder),e.attr("title",this._initialTitle||e.val())},_wrapper:function(){var e,i=this.element,n=i[0];(e=i.parents(".k-numerictextbox")).is("span.k-numerictextbox")||(e=(e=i.hide().wrap('<span class="k-numeric-wrap k-state-default" />').parent()).wrap("<span/>").parent()),e[0].style.cssText=n.style.cssText,n.style.width="",this.wrapper=e.addClass("k-widget k-numerictextbox").addClass(n.className).css("display",""),this._inputWrapper=t(e[0].firstChild)},_reset:function(){var e=this,i=e.element,n=i.attr("form"),s=n?t("#"+n):i.closest("form");s[0]&&(e._resetHandler=function(){setTimeout(function(){e.value(i[0].value),e.max(e._initialOptions.max),e.min(e._initialOptions.min)})},e._form=s.on("reset",e._resetHandler))}});function x(t,e){return'<span unselectable="on" class="k-link k-link-'+t+'" aria-label="'+e+'" title="'+e+'"><span unselectable="on" class="'+g+" k-i-arrow-"+("increase"===t?"60-up":"60-down")+'"></span></span>'}function C(t,e){var i=parseFloat(t,10).toString().split(m);return i[1]&&(i[1]=i[1].substring(0,e)),i.join(m)}r.plugin(k)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.toolbar",["kendo.core","kendo.userevents","kendo.popup"],function(){return function(t,e){var i=window.kendo,n=i.Class,s=i.ui.Widget,r=t.proxy,a=i.isFunction,o=i.keys,l=i._outerWidth,h="k-button",u="k-overflow-button",c="k-button-group",d="k-split-button",p="k-state-active",f="k-state-disabled",m="k-state-hidden",g="k-icon",_="k-i-",v="k-split-button-arrow",w="k-overflow-anchor",b="k-overflow-container",y="k-toolbar-first-visible",k="k-toolbar-last-visible",x="k-overflow-hidden",C=i.attr("uid");i.toolbar={};var T={overflowAnchor:'<div tabindex="0" class="k-overflow-anchor"></div>',overflowContainer:'<ul class="k-overflow-container k-list-container"></ul>'};i.toolbar.registerComponent=function(t,e,i){T[t]={toolbar:e,overflow:i}};var S=i.Class.extend({addOverflowAttr:function(){this.element.attr(i.attr("overflow"),this.options.overflow||"auto")},addUidAttr:function(){this.element.attr(C,this.options.uid)},addIdAttr:function(){this.options.id&&this.element.attr("id",this.options.id)},addOverflowIdAttr:function(){this.options.id&&this.element.attr("id",this.options.id+"_overflow")},attributes:function(){this.options.attributes&&this.element.attr(this.options.attributes)},show:function(){this.element.removeClass(m).show(),this.options.hidden=!1},hide:function(){this.element.addClass(m).hide(),this.overflow&&this.overflowHidden&&this.overflowHidden(),this.options.hidden=!0},remove:function(){this.element.remove()},enable:function(t){void 0===t&&(t=!0),this.element.toggleClass(f,!t),this.options.enable=t},twin:function(){var e=this.element.attr(C);return this.overflow&&this.options.splitContainerId?t("#"+this.options.splitContainerId).find("["+C+"='"+e+"']").data(this.options.type):this.overflow?this.toolbar.element.find("["+C+"='"+e+"']").data(this.options.type):this.toolbar.options.resizable?this.toolbar.popup.element.find("["+C+"='"+e+"']").data(this.options.type):void 0}});i.toolbar.Item=S;var I=S.extend({init:function(e,n){var s=e.useButtonTag?t('<button tabindex="0"></button>'):t('<a href tabindex="0"></a>');this.element=s,this.options=e,this.toolbar=n,this.attributes(),e.primary&&s.addClass("k-primary"),e.togglable&&(s.addClass("k-toggle-button"),this.toggle(e.selected)),void 0===e.url||e.useButtonTag||(s.attr("href",e.url),e.mobile&&s.attr(i.attr("role"),"button")),e.group&&(s.attr(i.attr("group"),e.group),this.group=this.toolbar.addToGroup(this,e.group)),!e.togglable&&e.click&&a(e.click)&&(this.clickHandler=e.click),e.togglable&&e.toggle&&a(e.toggle)&&(this.toggleHandler=e.toggle)},toggle:function(t,e){t=!!t,this.group&&t?this.group.select(this):this.group||this.select(t),e&&this.twin()&&this.twin().toggle(t)},getParentGroup:function(){if(this.options.isChild)return this.element.closest("."+c).data("buttonGroup")},_addGraphics:function(){var e,i,n,s=this.element,r=this.options.icon,a=this.options.spriteCssClass,o=this.options.imageUrl;(a||o||r)&&(e=!0,s.contents().filter(function(){return!t(this).hasClass("k-sprite")&&!t(this).hasClass(g)&&!t(this).hasClass("k-image")}).each(function(i,n){(1==n.nodeType||3==n.nodeType&&t.trim(n.nodeValue).length>0)&&(e=!1)}),e?s.addClass("k-button-icon"):s.addClass("k-button-icontext")),r?((i=s.children("span."+g).first())[0]||(i=t('<span class="'+g+'"></span>').prependTo(s)),i.addClass(_+r)):a?((i=s.children("span.k-sprite").first())[0]||(i=t('<span class="k-sprite '+g+'"></span>').prependTo(s)),i.addClass(a)):o&&((n=s.children("img.k-image").first())[0]||(n=t('<img alt="icon" class="k-image" />').prependTo(s)),n.attr("src",o))}});i.toolbar.Button=I;var D=I.extend({init:function(t,e){I.fn.init.call(this,t,e);var i=this.element;i.addClass(h),this.addIdAttr(),t.align&&i.addClass("k-align-"+t.align),"overflow"!=t.showText&&t.text&&(t.mobile?i.html('<span class="km-text">'+t.text+"</span>"):i.html(t.text)),t.hasIcon="overflow"!=t.showIcon&&(t.icon||t.spriteCssClass||t.imageUrl),t.hasIcon&&this._addGraphics(),this.addUidAttr(),this.addOverflowAttr(),this.enable(t.enable),t.hidden&&this.hide(),this.element.data({type:"button",button:this})},select:function(t){void 0===t&&(t=!1),this.element.toggleClass(p,t),this.options.selected=t}});i.toolbar.ToolBarButton=D;var F=I.extend({init:function(e,i){this.overflow=!0,I.fn.init.call(this,t.extend({},e),i);var n=this.element;"toolbar"!=e.showText&&e.text&&(e.mobile?n.html('<span class="km-text">'+e.text+"</span>"):n.html('<span class="k-text">'+e.text+"</span>")),e.hasIcon="toolbar"!=e.showIcon&&(e.icon||e.spriteCssClass||e.imageUrl),e.hasIcon&&this._addGraphics(),e.isChild||this._wrap(),this.addOverflowIdAttr(),this.attributes(),this.addUidAttr(),this.addOverflowAttr(),this.enable(e.enable),n.addClass(u+" "+h),e.hidden&&this.hide(),e.togglable&&this.toggle(e.selected),this.element.data({type:"button",button:this})},_wrap:function(){this.element=this.element.wrap("<li></li>").parent()},overflowHidden:function(){this.element.addClass(x)},select:function(t){void 0===t&&(t=!1),this.options.isChild?this.element.toggleClass(p,t):this.element.find(".k-button").toggleClass(p,t),this.options.selected=t}});i.toolbar.OverflowButton=F,i.toolbar.registerComponent("button",D,F);var E=S.extend({createButtons:function(e){for(var n=this.options,s=n.buttons||[],r=0;r<s.length;r++)s[r].uid||(s[r].uid=i.guid()),new e(t.extend({mobile:n.mobile,isChild:!0,type:"button"},s[r]),this.toolbar).element.appendTo(this.element)},refresh:function(){this.element.children().filter(":not('."+m+"'):first").addClass("k-group-start"),this.element.children().filter(":not('."+m+"'):last").addClass("k-group-end")}});i.toolbar.ButtonGroup=E;var O=E.extend({init:function(e,i){var n=this.element=t("<div></div>");this.options=e,this.toolbar=i,this.addIdAttr(),e.align&&n.addClass("k-align-"+e.align),this.createButtons(D),this.attributes(),this.addUidAttr(),this.addOverflowAttr(),this.refresh(),n.addClass(c),this.element.data({type:"buttonGroup",buttonGroup:this})}});i.toolbar.ToolBarButtonGroup=O;var A=E.extend({init:function(e,i){var n=this.element=t("<li></li>");this.options=e,this.toolbar=i,this.overflow=!0,this.addOverflowIdAttr(),this.createButtons(F),this.attributes(),this.addUidAttr(),this.addOverflowAttr(),this.refresh(),n.addClass((e.mobile?"":c)+" k-overflow-group"),this.element.data({type:"buttonGroup",buttonGroup:this})},overflowHidden:function(){this.element.addClass(x)}});i.toolbar.OverflowButtonGroup=A,i.toolbar.registerComponent("buttonGroup",O,A);var M=S.extend({init:function(e,i){var n=this.element=t('<div class="'+d+'" tabindex="0"></div>');this.options=e,this.toolbar=i,this.mainButton=new D(t.extend({},e,{hidden:!1}),i),this.arrowButton=t('<a class="'+h+" "+v+'"><span class="'+(e.mobile?"km-icon km-arrowdown":"k-icon k-i-arrow-60-down")+'"></span></a>'),this.popupElement=t('<ul class="k-list-container k-split-container"></ul>'),this.mainButton.element.removeAttr("href tabindex").appendTo(n),this.arrowButton.appendTo(n),this.popupElement.appendTo(n),e.align&&n.addClass("k-align-"+e.align),e.id||(e.id=e.uid),n.attr("id",e.id+"_wrapper"),this.addOverflowAttr(),this.addUidAttr(),this.createMenuButtons(),this.createPopup(),this._navigatable(),this.mainButton.main=!0,this.enable(e.enable),e.hidden&&this.hide(),n.data({type:"splitButton",splitButton:this,kendoPopup:this.popup})},_navigatable:function(){var e=this;e.popupElement.on("keydown","."+h,function(i){var n=t(i.target).parent();i.preventDefault(),i.keyCode===o.ESC||i.keyCode===o.TAB||i.altKey&&i.keyCode===o.UP?(e.toggle(),e.focus()):i.keyCode===o.DOWN?W(n,"next").focus():i.keyCode===o.UP?W(n,"prev").focus():i.keyCode===o.SPACEBAR||i.keyCode===o.ENTER?e.toolbar.userEvents.trigger("tap",{target:t(i.target)}):i.keyCode===o.HOME?n.parent().find(":kendoFocusable").first().focus():i.keyCode===o.END&&n.parent().find(":kendoFocusable").last().focus()})},createMenuButtons:function(){for(var e=this.options,i=e.menuButtons,n=0;n<i.length;n++)new D(t.extend({mobile:e.mobile,type:"button",click:e.click},i[n]),this.toolbar).element.wrap("<li></li>").parent().appendTo(this.popupElement)},createPopup:function(){var e=this,i=this.options,n=this.element;this.popupElement.attr("id",i.id+"_optionlist").attr(C,i.rootUid),i.mobile&&(this.popupElement=L(this.popupElement)),this.popup=this.popupElement.kendoPopup({appendTo:i.mobile?t(i.mobile).children(".km-pane"):null,anchor:n,isRtl:this.toolbar._isRtl,copyAnchorStyles:!1,animation:i.animation,open:function(t){e.toolbar.trigger("open",{target:n})?t.preventDefault():e.adjustPopupWidth(t.sender)},activate:function(){this.element.find(":kendoFocusable").first().focus()},close:function(t){e.toolbar.trigger("close",{target:n})&&t.preventDefault(),n.focus()}}).data("kendoPopup"),this.popup.element.on("click","a.k-button",N)},adjustPopupWidth:function(t){var e,n=t.options.anchor,s=l(n);i.wrap(t.element).addClass("k-split-wrapper"),e="border-box"!==t.element.css("box-sizing")?s-(l(t.element)-t.element.width()):s,t.element.css({fontFamily:n.css("font-family"),"min-width":e})},remove:function(){this.popup.element.off("click","a.k-button"),this.popup.destroy(),this.element.remove()},toggle:function(){(this.options.enable||this.popup.visible())&&this.popup.toggle()},enable:function(t){void 0===t&&(t=!0),this.mainButton.enable(t),this.element.toggleClass(f,!t),this.options.enable=t},focus:function(){this.element.focus()},hide:function(){this.popup&&this.popup.close(),this.element.addClass(m).hide(),this.options.hidden=!0},show:function(){this.element.removeClass(m).hide(),this.options.hidden=!1}});i.toolbar.ToolBarSplitButton=M;var H=S.extend({init:function(e,i){var n,s=this.element=t('<li class="'+d+'"></li>'),r=e.menuButtons;this.options=e,this.toolbar=i,this.overflow=!0,n=(e.id||e.uid)+"_optionlist",this.mainButton=new F(t.extend({},e)),this.mainButton.element.appendTo(s);for(var a=0;a<r.length;a++)new F(t.extend({mobile:e.mobile,type:"button",splitContainerId:n},r[a]),this.toolbar).element.appendTo(s);this.addUidAttr(),this.addOverflowAttr(),this.mainButton.main=!0,s.data({type:"splitButton",splitButton:this})},overflowHidden:function(){this.element.addClass(x)}});i.toolbar.OverflowSplitButton=H,i.toolbar.registerComponent("splitButton",M,H);var P=S.extend({init:function(e,i){var n=this.element=t("<div>&nbsp;</div>");this.element=n,this.options=e,this.toolbar=i,this.attributes(),this.addIdAttr(),this.addUidAttr(),this.addOverflowAttr(),n.addClass("k-separator"),n.data({type:"separator",separator:this})}}),z=S.extend({init:function(e,i){var n=this.element=t("<li>&nbsp;</li>");this.element=n,this.options=e,this.toolbar=i,this.overflow=!0,this.attributes(),this.addUidAttr(),this.addOverflowIdAttr(),n.addClass("k-separator"),n.data({type:"separator",separator:this})},overflowHidden:function(){this.element.addClass(x)}});i.toolbar.registerComponent("separator",P,z);var V=S.extend({init:function(e,i,n){var s=a(e)?e(i):e;s=s instanceof jQuery?s.wrap("<div></div>").parent():t("<div></div>").html(s),this.element=s,this.options=i,this.options.type="template",this.toolbar=n,this.attributes(),this.addUidAttr(),this.addIdAttr(),this.addOverflowAttr(),s.data({type:"template",template:this})}});i.toolbar.TemplateItem=V;var R=S.extend({init:function(e,i,n){var s=a(e)?t(e(i)):t(e);s=s instanceof jQuery?s.wrap("<li></li>").parent():t("<li></li>").html(s),this.element=s,this.options=i,this.options.type="template",this.toolbar=n,this.overflow=!0,this.attributes(),this.addUidAttr(),this.addOverflowIdAttr(),this.addOverflowAttr(),s.data({type:"template",template:this})},overflowHidden:function(){this.element.addClass(x)}});function B(t){t.target.is(".k-toggle-button")||t.target.toggleClass(p,"press"==t.type)}function L(e){return(e=t(e)).hasClass("km-actionsheet")?e.closest(".km-popup-wrapper"):e.addClass("km-widget km-actionsheet").wrap('<div class="km-actionsheet-wrapper km-actionsheet-tablet km-widget km-popup"></div>').parent().wrap('<div class="km-popup-wrapper k-popup"></div>').parent()}function N(e){t(e.target).closest("a.k-button").length&&e.preventDefault()}function W(e,i){var n="next"===i?t.fn.next:t.fn.prev,s="next"===i?t.fn.first:t.fn.last,r=n.call(e);return r.is(":kendoFocusable")||!r.length?r:r.find(":kendoFocusable").length?s.call(r.find(":kendoFocusable")):W(r,i)}i.toolbar.OverflowTemplateItem=R;var U=n.extend({init:function(t){this.name=t,this.buttons=[]},add:function(t){this.buttons[this.buttons.length]=t},remove:function(e){var i=t.inArray(e,this.buttons);this.buttons.splice(i,1)},select:function(t){for(var e=0;e<this.buttons.length;e++)this.buttons[e].select(!1);t.select(!0),t.twin()&&t.twin().select(!0)}}),q=s.extend({init:function(e,n){var a=this;if(s.fn.init.call(a,e,n),n=a.options,(e=a.wrapper=a.element).addClass("k-toolbar k-widget"),this.uid=i.guid(),this._isRtl=i.support.isRtl(e),this._groups={},e.attr(C,this.uid),a.isMobile="boolean"==typeof n.mobile?n.mobile:a.element.closest(".km-root")[0],a.animation=a.isMobile?{open:{effects:"fade"}}:{},a.isMobile&&(e.addClass("km-widget"),g="km-icon",_="km-",h="km-button",c="km-buttongroup km-widget",p="km-state-active",f="km-state-disabled"),n.resizable?(a._renderOverflow(),e.addClass("k-toolbar-resizable"),a.overflowUserEvents=new i.UserEvents(a.element,{threshold:5,allowSelection:!0,filter:"."+w,tap:r(a._toggleOverflow,a)}),a._resizeHandler=i.onResize(function(){a.resize()})):a.popup={element:t([])},n.items&&n.items.length){for(var o=0;o<n.items.length;o++)a.add(n.items[o]);n.resizable&&a._shrink(a.element.innerWidth())}a.userEvents=new i.UserEvents(document,{threshold:5,allowSelection:!0,filter:"["+C+"="+this.uid+"] a."+h+", ["+C+"="+this.uid+"] ."+u,tap:r(a._buttonClick,a),press:B,release:B}),a.element.on("click","a.k-button",N),a._navigatable(),n.resizable&&a.popup.element.on("click",NaN,N),n.resizable&&this._toggleOverflowAnchor(),i.notify(a)},events:["click","toggle","open","close","overflowOpen","overflowClose"],options:{name:"ToolBar",items:[],resizable:!0,mobile:null},addToGroup:function(t,e){var i;return(i=this._groups[e]?this._groups[e]:this._groups[e]=new U).add(t),i},destroy:function(){this.element.find("."+d).each(function(e,i){t(i).data("kendoPopup").destroy()}),this.element.off("click","a.k-button"),this.userEvents.destroy(),this.options.resizable&&(i.unbindResize(this._resizeHandler),this.overflowUserEvents.destroy(),this.popup.element.off("click","a.k-button"),this.popup.destroy()),s.fn.destroy.call(this)},add:function(e){var n,s,r=T[e.type],a=e.template,o=this.isMobile?"":"k-item k-state-default",l=e.overflowTemplate;if(t.extend(e,{uid:i.guid(),animation:this.animation,mobile:this.isMobile,rootUid:this.uid}),e.menuButtons)for(var h=0;h<e.menuButtons.length;h++)t.extend(e.menuButtons[h],{uid:i.guid()});a&&!l?e.overflow="never":e.overflow||(e.overflow="auto"),"never"!==e.overflow&&this.options.resizable&&(l?s=new R(l,e,this):r&&(s=new r.overflow(e,this)).element.addClass(o),s&&("auto"===e.overflow&&s.overflowHidden(),s.element.appendTo(this.popup.container),this.angular("compile",function(){return{elements:s.element.get()}}))),"always"!==e.overflow&&(a?n=new V(a,e,this):r&&(n=new r.toolbar(e,this)),n&&(n.element.appendTo(this.element),this.angular("compile",function(){return{elements:n.element.get()}})))},_getItem:function(e){var i,n,s,r,a=this.options.resizable;return(i=this.element.find(e)).length||(i=t(".k-split-container[data-uid="+this.uid+"]").find(e)),r=i.length?i.data("type"):"",(n=i.data(r))?(n.main&&(r="splitButton",n=(i=i.parent("."+d)).data(r)),a&&(s=n.twin())):a&&(r=(i=this.popup.element.find(e)).length?i.data("type"):"",(s=i.data(r))&&s.main&&(r="splitButton",s=(i=i.parent("."+d)).data(r))),{type:r,toolbar:n,overflow:s}},remove:function(t){var e=this._getItem(t);e.toolbar&&e.toolbar.remove(),e.overflow&&e.overflow.remove(),this.resize(!0)},hide:function(t){var e=this._getItem(t);e.toolbar&&("button"===e.toolbar.options.type&&e.toolbar.options.isChild?(e.toolbar.hide(),e.toolbar.getParentGroup().refresh()):e.toolbar.options.hidden||e.toolbar.hide()),e.overflow&&("button"===e.overflow.options.type&&e.overflow.options.isChild?(e.overflow.hide(),e.overflow.getParentGroup().refresh()):e.overflow.options.hidden||e.overflow.hide()),this.resize(!0)},show:function(t){var e=this._getItem(t);e.toolbar&&("button"===e.toolbar.options.type&&e.toolbar.options.isChild?(e.toolbar.show(),e.toolbar.getParentGroup().refresh()):e.toolbar.options.hidden&&e.toolbar.show()),e.overflow&&("button"===e.overflow.options.type&&e.overflow.options.isChild?(e.toolbar.show(),e.overflow.getParentGroup().refresh()):e.overflow.options.hidden&&e.overflow.show()),this.resize(!0)},enable:function(t,e){var i=this._getItem(t);void 0===e&&(e=!0),i.toolbar&&i.toolbar.enable(e),i.overflow&&i.overflow.enable(e)},getSelectedFromGroup:function(t){return this.element.find(".k-toggle-button[data-group='"+t+"']").filter("."+p)},toggle:function(e,i){var n=t(e).data("button");n.options.togglable&&(void 0===i&&(i=!0),n.toggle(i,!0))},_renderOverflow:function(){var e=this,n=T.overflowContainer,s=e._isRtl,r=s?"left":"right";e.overflowAnchor=t(T.overflowAnchor).addClass(h),e.element.append(e.overflowAnchor),e.isMobile?(e.overflowAnchor.append('<span class="km-icon km-more"></span>'),n=L(n)):e.overflowAnchor.append('<span class="k-icon k-i-more-vertical"></span>'),e.popup=new i.ui.Popup(n,{origin:"bottom "+r,position:"top "+r,anchor:e.overflowAnchor,isRtl:s,animation:e.animation,appendTo:e.isMobile?t(e.isMobile).children(".km-pane"):null,copyAnchorStyles:!1,open:function(n){var r=i.wrap(e.popup.element).addClass("k-overflow-wrapper");e.isMobile?e.popup.container.css("max-height",parseFloat(t(".km-content:visible").innerHeight())-15+"px"):r.css("margin-left",(s?-1:1)*((l(r)-r.width())/2+1)),e.trigger("overflowOpen")&&n.preventDefault()},activate:function(){this.element.find(":kendoFocusable").first().focus()},close:function(t){e.trigger("overflowClose")&&t.preventDefault(),this.element.focus()}}),e.popup.element.on("keydown","."+h,function(i){var n=t(i.target),s=n.parent(),r=s.is("."+c)||s.is("."+d);i.preventDefault(),i.keyCode===o.ESC||i.keyCode===o.TAB||i.altKey&&i.keyCode===o.UP?(e._toggleOverflow(),e.overflowAnchor.focus()):i.keyCode===o.DOWN?W(!r||r&&n.is(":last-child")?s:n,"next").focus():i.keyCode===o.UP?W(!r||r&&n.is(":first-child")?s:n,"prev").focus():i.keyCode===o.SPACEBAR||i.keyCode===o.ENTER?(e.userEvents.trigger("tap",{target:t(i.target)}),e.overflowAnchor.focus()):i.keyCode===o.HOME?s.parent().find(":kendoFocusable").first().focus():i.keyCode===o.END&&s.parent().find(":kendoFocusable").last().focus()}),e.isMobile?e.popup.container=e.popup.element.find("."+b):e.popup.container=e.popup.element,e.popup.container.attr(C,this.uid)},_toggleOverflowAnchor:function(){(this.options.mobile?this.popup.element.find("."+b).children(":not(."+x+", .k-popup)").length>0:this.popup.element.children(":not(."+x+", .k-popup)").length>0)?this.overflowAnchor.css({visibility:"visible",width:""}):this.overflowAnchor.css({visibility:"hidden",width:"1px"})},_buttonClick:function(e){var i,n,s,r,o,l,c=e.target.closest("."+v).length;e.preventDefault(),c?this._toggle(e):(i=t(e.target).closest("."+h,this.element)).hasClass(w)||(!(n=i.data("button"))&&this.popup&&(n=(i=t(e.target).closest("."+u,this.popup.container)).parent("li").data("button")),n&&n.options.enable&&(n.options.togglable?(r=a(n.toggleHandler)?n.toggleHandler:null,n.toggle(!n.options.selected,!0),o={target:i,group:n.options.group,checked:n.options.selected,id:n.options.id},r&&r.call(this,o),this.trigger("toggle",o)):(r=a(n.clickHandler)?n.clickHandler:null,o={sender:this,target:i,id:n.options.id},r&&r.call(this,o),this.trigger("click",o)),n.options.url&&(n.options.attributes&&n.options.attributes.target&&(l=n.options.attributes.target),window.open(n.options.url,l||"_self")),i.hasClass(u)&&this.popup.close(),(s=i.closest(".k-split-container"))[0]&&(s.data("kendoPopup")||s.parents(".km-popup-wrapper").data("kendoPopup")).close()))},_navigatable:function(){this.element.attr("tabindex",0).on("focusin",function(e){var i=t(e.target),n=t(this).find(":kendoFocusable:first");i.is(".k-toolbar")&&0!==n.length&&(n.is("."+w)&&(n=W(n,"next")),n[0].focus())}).on("keydown",r(this._keydown,this))},_keydown:function(e){var i=t(e.target),n=e.keyCode,s=this.element.children(":not(.k-separator):visible"),r=this._isRtl?-1:1;if(n===o.TAB){var a=i.parentsUntil(this.element).last(),l=!1,h=!1;a.length||(a=i),a.is("."+w)&&(e.shiftKey&&e.preventDefault(),s.last().is(":kendoFocusable")?s.last().focus():s.last().find(":kendoFocusable").last().focus()),e.shiftKey||s.index(a)!==s.length-1||(l=!a.is("."+c)||i.is(":last-child"));var u=s.index(a)===s.not(".k-overflow-anchor").first().index();if(e.shiftKey&&u&&(h=!a.is("."+c)||i.is(":first-child")),l&&this.overflowAnchor&&"hidden"!==this.overflowAnchor.css("visibility")&&(e.preventDefault(),this.overflowAnchor.focus()),h){e.preventDefault();var p=this._getPrevFocusable(this.wrapper);p&&p.focus()}this._preventNextFocus=!1}if(e.altKey&&n===o.DOWN){var f=t(document.activeElement).data("splitButton"),m=t(document.activeElement).is("."+w);f?f.toggle():m&&this._toggleOverflow()}else{if((n===o.SPACEBAR||n===o.ENTER)&&!i.is("input, checkbox"))return n===o.SPACEBAR&&e.preventDefault(),void(i.is("."+d)?(i=i.children().first(),this.userEvents.trigger("tap",{target:i})):n===o.SPACEBAR&&this.userEvents.trigger("tap",{target:i}));if(n===o.HOME){if(i.is(".k-dropdown")||i.is("input"))return;this.overflowAnchor?s.eq(1).focus():s.first().focus(),e.preventDefault()}else if(n===o.END){if(i.is(".k-dropdown")||i.is("input"))return;this.overflowAnchor&&"hidden"!=t(this.overflowAnchor).css("visibility")?this.overflowAnchor.focus():s.last().focus(),e.preventDefault()}else n!==o.RIGHT||this._preventNextFocus||i.is("input, select, .k-dropdown, .k-colorpicker")||!this._getNextElement(e.target,1*r)?n!==o.LEFT||this._preventNextFocus||i.is("input, select, .k-dropdown, .k-colorpicker")||!this._getNextElement(e.target,-1*r)||(this._getNextElement(e.target,-1*r).focus(),e.preventDefault()):(this._getNextElement(e.target,1*r).focus(),e.preventDefault())}},_getNextElement:function(e,i){var n=this.element.children(":not(.k-separator):visible"),s=-1===n.index(e)?n.index(e.parentElement):n.index(e),r=this.overflowAnchor?1:0,a=i,o=1===i?n.length-1:r,l=1===i?r:n.length-1,h=n[s+i];if(this._preventNextFocus=!1,t(e).closest("."+c).length&&!t(e).is(1===i?":last-child":":first-child"))return t(e).closest("."+c).children()[t(e).closest("."+c).children().index(e)+i];for(this.overflowAnchor&&e===this.overflowAnchor[0]&&-1===i&&(h=n[n.length-1]),s===o&&(h=!this.overflowAnchor||this.overflowAnchor&&"hidden"===t(this.overflowAnchor).css("visibility")?n[l]:this.overflowAnchor);!t(h).is(":kendoFocusable");){if(!(h=-1===i&&t(h).closest("."+c).length?t(h).children(":not(label, div)").last():t(h).children(":not(label, div)").first()).length&&!(h=n[s+(a+=i)]))return this.overflowAnchor;this._preventNextFocus=!t(h).closest("."+c).length}return h},_getPrevFocusable:function(e){return e.is("html")?e:(e.prevAll().each(function(){return(n=t(this)).is(":kendoFocusable")?(i=n,!1):n.find(":kendoFocusable").length>0?(i=n.find(":kendoFocusable").last(),!1):void 0}),i||this._getPrevFocusable(e.parent()));var i,n},_toggle:function(e){var i=t(e.target).closest("."+d).data("splitButton");e.preventDefault(),i.options.enable&&i.toggle()},_toggleOverflow:function(){this.popup.toggle()},_resize:function(t){var e=t.width;this.options.resizable&&(this.popup.close(),this._shrink(e),this._stretch(e),this._markVisibles(),this._toggleOverflowAnchor())},_childrenWidth:function(){var e=0;return this.element.children(":visible:not('."+m+"')").each(function(){e+=l(t(this),!0)}),Math.ceil(e)},_shrink:function(t){var e,i;if(t<this._childrenWidth())for(var n=(i=this.element.children(":visible:not([data-overflow='never'], ."+w+")")).length-1;n>=0&&(e=i.eq(n),!(t>this._childrenWidth()));n--)this._hideItem(e)},_stretch:function(t){var e,i;if(t>this._childrenWidth()){i=this.element.children(":hidden:not('."+m+"')");for(var n=0;n<i.length&&(e=i.eq(n),!(t<this._childrenWidth())&&this._showItem(e,t));n++);}},_hideItem:function(t){t.hide(),this.popup&&this.popup.container.find(">li[data-uid='"+t.data("uid")+"']").removeClass(x)},_showItem:function(t,e){return!!(t.length&&e>this._childrenWidth()+l(t,!0))&&(t.show(),this.popup&&this.popup.container.find(">li[data-uid='"+t.data("uid")+"']").addClass(x),!0)},_markVisibles:function(){var t=this.popup.container.children(),e=this.element.children(":not(.k-overflow-anchor)"),i=t.filter(":not(.k-overflow-hidden)"),n=e.filter(":visible");t.add(e).removeClass(y+" "+k),i.first().add(n.first()).addClass(y),i.last().add(n.last()).addClass(k)}});i.ui.plugin(q)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.panelbar",["kendo.data"],function(){return function(t,e){var i,n=window.kendo,s=n.ui,r=n.keys,a=t.extend,o=t.proxy,l=t.each,h=t.isArray,u=n.template,c=s.Widget,d=n.data.HierarchicalDataSource,p=/^(ul|a|div)$/i,f=".kendoPanelBar",m="k-last",g=".k-link",_=".k-item",v=".k-group:visible",w="k-first",b="k-content",y="k-state-active",k="> .k-panel",x="> .k-content",C="k-state-disabled",T="k-state-selected",S="."+T,I="k-state-highlight",D=".k-item:not(.k-state-disabled)",F="> "+D+" > "+g+", .k-panel > "+D+" > "+g,E="> li > "+S+", .k-panel > li > "+S,O="aria-disabled",A="aria-expanded",M="aria-hidden",H="aria-selected",P=":visible",z={text:"dataTextField",url:"dataUrlField",spriteCssClass:"dataSpriteCssClassField",imageUrl:"dataImageUrlField"},V={aria:function(t){var e="";return(t.items||t.content||t.contentUrl||t.expanded)&&(e+=A+"='"+(t.expanded?"true":"false")+"' "),!1===t.enabled&&(e+=O+"='true'"),e},wrapperCssClass:function(t,e){var i="k-item",n=e.index;return!1===e.enabled?i+=" "+C:!0===e.expanded?i+=" "+y:i+=" k-state-default",0===n&&(i+=" k-first"),n==t.length-1&&(i+=" k-last"),e.cssClass&&(i+=" "+e.cssClass),i},textClass:function(t,e){var i="k-link";return e.firstLevel&&(i+=" k-header"),t.selected&&(i+=" "+T),i},textAttributes:function(t){return t?" href='"+t+"'":""},arrowClass:function(t){var e="k-icon";return e+=t.expanded?" k-panelbar-collapse k-i-arrow-60-up":" k-panelbar-expand k-i-arrow-60-down"},text:function(t){return!1===t.encoded?t.text:n.htmlEncode(t.text)},groupAttributes:function(t){return!0!==t.expanded?" style='display:none'":""},ariaHidden:function(t){return!0!==t.expanded},groupCssClass:function(){return"k-group k-panel"},contentAttributes:function(t){return!0!==t.item.expanded?" style='display:none'":""},content:function(t){return t.content?t.content:t.contentUrl?"":"&nbsp;"},contentUrl:function(t){return t.contentUrl?'href="'+t.contentUrl+'"':""}};function R(e){(e=t(e)).filter(".k-first:not(:first-child)").removeClass(w),e.filter(".k-last:not(:last-child)").removeClass(m),e.filter(":first-child").addClass(w),e.filter(":last-child").addClass(m)}i=function(t){return t.children("span").children(".k-icon")};var B=n.ui.DataBoundWidget.extend({init:function(e,i){var s,r,a=this;h(i)&&(i={dataSource:i}),r=i&&!!i.dataSource,c.fn.init.call(a,e,i),e=a.wrapper=a.element.addClass("k-widget k-reset k-header k-panelbar"),i=a.options,e[0].id&&(a._itemId=e[0].id+"_pb_active"),a._tabindex(),a._accessors(),a._dataSource(),a._templates(),a._initData(r),a._updateClasses(),a._animations(i),e.on("click"+f,F,function(e){a._click(t(e.currentTarget))&&e.preventDefault()}).on("mouseenter"+f+" mouseleave"+f,F,a._toggleHover).on("click"+f,".k-item.k-state-disabled > .k-link",!1).on("click"+f,".k-request-retry",o(a._retryRequest,a)).on("keydown"+f,t.proxy(a._keydown,a)).on("focus"+f,function(){var t=a.select();a._current(t[0]?t:a._first())}).on("blur"+f,function(){a._current(null)}).attr("role","menu"),(s=e.find("li."+y+" > ."+b))[0]&&a.expand(s.parent(),!1),i.dataSource||a._angularCompile(),n.notify(a)},events:["expand","collapse","select","activate","change","error","dataBound","contentLoad"],options:{name:"PanelBar",dataSource:{},animation:{expand:{effects:"expand:vertical",duration:200},collapse:{duration:200}},messages:{loading:"Loading...",requestFailed:"Request failed.",retry:"Retry"},autoBind:!0,loadOnDemand:!0,expandMode:"multiple",template:"",dataTextField:null},_angularCompile:function(){var t=this;t.angular("compile",function(){return{elements:t.element.children("li"),data:[{dataItem:t.options.$angular}]}})},_angularCompileElements:function(e,i){this.angular("compile",function(){return{elements:e,data:t.map(i,function(t){return[{dataItem:t}]})}})},_angularCleanup:function(){var t=this;t.angular("cleanup",function(){return{elements:t.element.children("li")}})},destroy:function(){c.fn.destroy.call(this),this.element.off(f),this._angularCleanup(),n.destroy(this.element)},_initData:function(t){t&&(this.element.empty(),this.options.autoBind&&(this._progress(!0),this.dataSource.fetch()))},_templates:function(){var t=this.options,e=o(this._fieldAccessor,this);t.template&&"string"==typeof t.template?t.template=u(t.template):t.template||(t.template=u("# var text = "+e("text")+"(data.item); ## if (typeof data.item.encoded != 'undefined' && data.item.encoded === false) {##= text ## } else { ##: text ## } #")),this.templates={content:u("<div role='region' class='k-content'#= contentAttributes(data) #>#= content(item) #</div>"),group:u("<ul role='group' aria-hidden='#= ariaHidden(group) #' class='#= groupCssClass(group) #'#= groupAttributes(group) #>#= renderItems(data) #</ul>"),itemWrapper:u("# var url = "+e("url")+"(item); ## var imageUrl = "+e("imageUrl")+"(item); ## var spriteCssClass = "+e("spriteCssClass")+"(item); ## var contentUrl = contentUrl(item); ## var tag = url||contentUrl ? 'a' : 'span'; #<#= tag # class='#= textClass(item, group) #' #= contentUrl ##= textAttributes(url) #># if (imageUrl) { #<img class='k-image' alt='' src='#= imageUrl #' /># } ## if (spriteCssClass) { #<span class='k-sprite #= spriteCssClass #'></span># } ##= data.panelBar.options.template(data) ##= arrow(data) #</#= tag #>"),item:u("<li role='menuitem' #=aria(item)#class='#= wrapperCssClass(group, item) #'"+n.attr("uid")+"='#= item.uid #'>#= itemWrapper(data) ## if (item.items && item.items.length > 0) { ##= subGroup({ items: item.items, panelBar: panelBar, group: { expanded: item.expanded } }) ## } else if (item.content || item.contentUrl) { ##= renderContent(data) ## } #</li>"),loading:u("<div class='k-item'><span class='k-icon k-i-loading'></span> #: data.messages.loading #</div>"),retry:u("#: data.messages.requestFailed # <button class='k-button k-request-retry'>#: data.messages.retry #</button>"),arrow:u("<span class='#= arrowClass(item) #'></span>"),empty:u("")}},setOptions:function(t){var e=this.options.animation;this._animations(t),t.animation=a(!0,e,t.animation),"dataSource"in t&&this.setDataSource(t.dataSource),c.fn.setOptions.call(this,t)},expand:function(e,i){var n=this,s={};if(e=this.element.find(e),!n._animating||!e.find("ul").is(":visible"))return n._animating=!0,i=!1!==i,e.each(function(r,a){a=t(a);var o=e.children(".k-group,.k-content");o.length||(o=n._addGroupElement(e));var l=o.add(a.find(x));if(!a.hasClass(C)&&l.length>0){if("single"==n.options.expandMode&&n._collapseAllExpanded(a))return n;e.find("."+I).removeClass(I),a.addClass(I),i||(s=n.options.animation,n.options.animation={expand:{effects:{}},collapse:{hide:!0,effects:{}}}),n._triggerEvent("expand",a)||n._toggleItem(a,!1,!1),i||(n.options.animation=s)}}),n;n.one("complete",function(){setTimeout(function(){n.expand(e)})})},collapse:function(e,i){var n=this,s={};return n._animating=!0,i=!1!==i,(e=n.element.find(e)).each(function(e,r){var a=(r=t(r)).find(k).add(r.find(x));!r.hasClass(C)&&a.is(P)&&(r.removeClass(I),i||(s=n.options.animation,n.options.animation={expand:{effects:{}},collapse:{hide:!0,effects:{}}}),n._triggerEvent("collapse",r)||n._toggleItem(r,!0),i||(n.options.animation=s))}),n},updateArrow:function(e){var i=this;(e=t(e)).children(g).children(".k-panelbar-collapse, .k-panelbar-expand").remove(),e.filter(function(){var e=i.dataItem(this);return e?e.hasChildren||e.content||e.contentUrl:t(this).find(".k-panel").length>0||t(this).find(".k-content").length>0}).children(".k-link:not(:has([class*=k-i-arrow]))").each(function(){var e=t(this),i=e.parent();e.append("<span class='k-icon "+(i.hasClass(y)?" k-panelbar-collapse k-i-arrow-60-up":" k-panelbar-expand k-i-arrow-60-down")+"'/>")})},_accessors:function(){var t,e,i,s=this.options,r=this.element;for(t in z)e=s[z[t]],i=r.attr(n.attr(t+"-field")),!e&&i&&(e=i),e||(e=t),h(e)||(e=[e]),s[z[t]]=e},_progress:function(t,e){var n=this.element,s=this.templates.loading({messages:this.options.messages});1==arguments.length?(e=t)?n.html(s):n.empty():i(t).toggleClass("k-i-loading",e).removeClass("k-i-refresh")},_refreshRoot:function(e){var i=this,n={firstLevel:!0,expanded:!0,length:i.element.children().length};this.element.empty();var s=t.map(e,function(e,s){return"string"==typeof e?t(e):(e.items=[],t(i.renderItem({group:n,item:a(e,{index:s})})))});this.element.append(s),this._angularCompileElements(s,e)},_refreshChildren:function(e,i){var n,r,a;i.children(".k-group").empty();var o,l,h,u,c=e.children.data();if(c.length)for(this.append(e.children,i),this.options.loadOnDemand&&this._toggleGroup(i.children(".k-group"),!1),r=i.children(".k-group").children("li"),n=0;n<r.length;n++)a=r.eq(n),this.trigger("itemChange",{item:a,data:this.dataItem(a),ns:s});else l=o=i,h=o.children("ul"),u=l.children(".k-link").children(".k-icon"),o.hasClass("k-panelbar")||(!u.length&&h.length?u=t("<span class='k-icon' />").appendTo(l):h.length&&h.children().length||(u.remove(),h.remove())),r=i.children(".k-group").children("li"),this._angularCompileElements(r,c)},findByUid:function(e){for(var i,s=this.element.find(".k-item"),r=n.attr("uid"),a=0;a<s.length;a++)if(s[a].getAttribute(r)==e){i=s[a];break}return t(i)},refresh:function(t){var e=this.options,i=t.node,n=t.action,s=t.items,r=this.wrapper,a=e.loadOnDemand;if(t.field){if(!s[0]||!s[0].level)return;return this._updateItems(s,t.field)}if(i&&(r=this.findByUid(i.uid),this._progress(r,!1)),"add"==n?this._appendItems(t.index,s,r):"remove"==n?this.remove(this.findByUid(s[0].uid)):"itemchange"==n?this._updateItems(s):"itemloaded"==n?this._refreshChildren(i,r):this._refreshRoot(s),"remove"!=n)for(var o=0;o<s.length;o++)if(!a||s[o].expanded){var l=s[o];this._hasChildItems(l)&&l.load()}this.trigger("dataBound",{node:i?r:void 0})},_error:function(t){var e=t.node&&this.findByUid(t.node.uid),n=this.templates.retry({messages:this.options.messages});e?(this._progress(e,!1),this._expanded(e,!1),i(e).addClass("k-i-refresh"),t.node.loaded(!1)):(this._progress(!1),this.element.html(n))},_retryRequest:function(t){t.preventDefault(),this.dataSource.fetch()},items:function(){return this.element.find(".k-item > span:first-child")},setDataSource:function(t){this.options.dataSource=t,this._dataSource(),this.options.autoBind&&(this._progress(!0),this.dataSource.fetch())},_bindDataSource:function(){this._refreshHandler=o(this.refresh,this),this._errorHandler=o(this._error,this),this.dataSource.bind("change",this._refreshHandler),this.dataSource.bind("error",this._errorHandler)},_unbindDataSource:function(){var t=this.dataSource;t&&(t.unbind("change",this._refreshHandler),t.unbind("error",this._errorHandler))},_fieldAccessor:function(e){var i=this.options[z[e]]||[],s=i.length,r="(function(item) {";return 0===s?r+="return item['"+e+"'];":(r+="var levels = ["+t.map(i,function(t){return"function(d){ return "+n.expr(t)+"}"}).join(",")+"];",r+="if(item.level){return levels[Math.min(item.level(), "+s+"-1)](item);}else",r+="{return levels["+s+"-1](item)}"),r+="})"},_dataSource:function(){var t=this.options.dataSource;t&&(t=h(t)?{data:t}:t,this._unbindDataSource(),t.fields||(t.fields=[{field:"text"},{field:"url"},{field:"spriteCssClass"},{field:"imageUrl"}]),this.dataSource=d.create(t),this._bindDataSource())},_appendItems:function(e,i,n){var s,r,o=this;n.hasClass("k-panelbar")?(s=n.children("li"),r=n):((r=n.children(".k-group")).length||(r=o._addGroupElement(n)),s=r.children("li"));var l={firstLevel:n.hasClass("k-panelbar"),expanded:!0,length:s.length},h=t.map(i,function(e,i){return t("string"==typeof e?e:o.renderItem({group:l,item:a(e,{index:i})}))});void 0===e&&(e=s.length);for(var u=0;u<h.length;u++)0===s.length||0===e?r.append(h[u]):h[u].insertAfter(s[e-1]);o._angularCompileElements(h,i),o.dataItem(n)&&(o.dataItem(n).hasChildren=!0,o.updateArrow(n))},_updateItems:function(e,i){var n,r,o,l,h=this,u={panelBar:h.options,item:l,group:{}},c="expanded"!=i;if("selected"==i)if(e[0][i]){var d=h.findByUid(e[0].uid);d.hasClass(C)||h.select(d,!0)}else h.clearSelection();else{var p=t.map(e,function(t){return h.findByUid(t.uid)});for(c&&h.angular("cleanup",function(){return{elements:p}}),n=0;n<e.length;n++)u.item=l=e[n],u.panelBar=h,r=(o=p[n]).parent(),c&&(u.group={firstLevel:r.hasClass("k-panelbar"),expanded:o.parent().hasClass(y),length:o.children().length},o.children(".k-link").remove(),o.prepend(h.templates.itemWrapper(a(u,{arrow:l.hasChildren||l.content||l.contentUrl?h.templates.arrow:h.templates.empty},V)))),"expanded"==i?h._toggleItem(o,!l[i],!l[i]||"true"):"enabled"==i&&(h.enable(o,l[i]),l[i]||l.selected&&l.set("selected",!1)),o.length&&this.trigger("itemChange",{item:o,data:l,ns:s});c&&h.angular("compile",function(){return{elements:p,data:t.map(e,function(t){return[{dataItem:t}]})}})}},_toggleDisabled:function(t,e){(t=this.element.find(t)).toggleClass("k-state-default",e).toggleClass(C,!e).attr(O,!e)},dataItem:function(e){var i=t(e).closest(_).attr(n.attr("uid")),s=this.dataSource;return s&&s.getByUid(i)},select:function(e,i){var n=this;return void 0===e?n.element.find(E).parent():((e=n.element.find(e)).length?e.each(function(){var e=t(this),s=e.children(g);if(e.hasClass(C))return n;n._updateSelected(s,i)}):this._updateSelected(e),n)},clearSelection:function(){this.select(t())},enable:function(t,e){return this._toggleDisabled(t,!1!==e),this},disable:function(t){return this._toggleDisabled(t,!1),this},append:function(t,e){e=this.element.find(e);var i=this._insert(t,e,e.length?e.find(k):null);return l(i.items,function(){i.group.append(this),R(this)}),this.updateArrow(e),R(i.group.find(".k-first, .k-last")),i.group.height("auto"),this},insertBefore:function(t,e){e=this.element.find(e);var i=this._insert(t,e,e.parent());return l(i.items,function(){e.before(this),R(this)}),R(e),i.group.height("auto"),this},insertAfter:function(t,e){e=this.element.find(e);var i=this._insert(t,e,e.parent());return l(i.items,function(){e.after(this),R(this)}),R(e),i.group.height("auto"),this},remove:function(t){var e=(t=this.element.find(t)).parentsUntil(this.element,_),i=t.parent("ul");return t.remove(),!i||i.hasClass("k-panelbar")||i.children(_).length||i.remove(),e.length&&(e=e.eq(0),this.updateArrow(e),R(e)),this},reload:function(e){var i=this;(e=i.element.find(e)).each(function(){var e=t(this);i._ajaxRequest(e,e.children("."+b),!e.is(P))})},_first:function(){return this.element.children(D).first()},_last:function(){var t=this.element.children(D).last(),e=t.children(v);return e[0]?e.children(D).last():t},_current:function(e){var i=this._focused,n=this._itemId;if(void 0===e)return i;this.element.removeAttr("aria-activedescendant"),i&&i.length&&(i[0].id===n&&i.removeAttr("id"),i.children(g).removeClass("k-state-focused")),t(e).length&&(n=e[0].id||n,e.attr("id",n).children(g).addClass("k-state-focused"),this.element.attr("aria-activedescendant",n)),this._focused=e},_keydown:function(t){var e=t.keyCode,i=this._current();t.target==t.currentTarget&&(e==r.DOWN||e==r.RIGHT?(this._current(this._nextItem(i)),t.preventDefault()):e==r.UP||e==r.LEFT?(this._current(this._prevItem(i)),t.preventDefault()):e==r.ENTER||e==r.SPACEBAR?(this._click(i.children(g)),t.preventDefault()):e==r.HOME?(this._current(this._first()),t.preventDefault()):e==r.END&&(this._current(this._last()),t.preventDefault()))},_nextItem:function(t){if(!t)return this._first();var e=t.children(v),i=t.nextAll(":visible").first();return e[0]&&(i=e.children("."+w)),i[0]||(i=t.parent(v).parent(_).next()),i[0]||(i=this._first()),i.hasClass(C)&&(i=this._nextItem(i)),i},_prevItem:function(t){if(!t)return this._last();var e,i=t.prevAll(":visible").first();if(i[0])for(e=i;e[0];)(e=e.children(v).children("."+m))[0]&&(i=e);else(i=t.parent(v).parent(_))[0]||(i=this._last());return i.hasClass(C)&&(i=this._prevItem(i)),i},_insert:function(e,i,n){var s,r,o=this,l=t.isPlainObject(e),h=i&&i[0];if(h||(n=o.element),r={firstLevel:n.hasClass("k-panelbar"),expanded:t(i).hasClass(y),length:n.children().length},h&&!n.length&&(n=t(o.renderGroup({group:r,options:o.options})).appendTo(i)),l||t.isArray(e)||e instanceof d){if(e instanceof d&&(e=e.data()),s=t.map(l?[e]:e,function(e,i){return t("string"==typeof e?e:o.renderItem({group:r,item:a(e,{index:i})}))}),h){var u=o.dataItem(i);u?(u.hasChildren=!0,i.attr(A,u.expanded).not("."+y).children("ul").attr(M,!u.expanded)):i.attr(A,!1)}}else s="string"==typeof e&&"<"!=e.charAt(0)?o.element.find(e):t(e),o._updateItemsClasses(s);return e.length||(e=[e]),o._angularCompileElements(s,e),{items:s,group:n}},_toggleHover:function(e){var i=t(e.currentTarget);i.parents("li."+C).length||i.toggleClass("k-state-hover","mouseenter"==e.type)},_updateClasses:function(){var e,i,n,s,r;s=(e=this.element.find("li > ul").not(function(){return t(this).parentsUntil(".k-panelbar","div").length}).addClass("k-group k-panel").attr("role","group")).parent(),n=(r=this.dataItem(s))&&r.expanded||!1,e.parent().attr(A,n).not("."+y).children("ul").attr(M,!n).hide(),i=this.element.add(e).children(),this._updateItemsClasses(i),this.updateArrow(i),R(i)},_updateItemsClasses:function(t){for(var e=t.length,i=0;i<e;i++)this._updateItemClasses(t[i],i)},_updateItemClasses:function(e,i){var s,r,a=this._selected,o=this.options.contentUrls,l=o&&o[i],h=this.element[0];e=t(e).addClass("k-item").attr("role","menuitem"),n.support.browser.msie&&e.css("list-style-position","inside").css("list-style-position",""),e.children("img").addClass("k-image"),(r=e.children("a").addClass("k-link"))[0]&&(r.attr("href",l),r.children("img").addClass("k-image")),e.filter(":not([disabled]):not([class*=k-state])").addClass("k-state-default"),e.filter("li[disabled]").addClass("k-state-disabled").attr(O,!0).removeAttr("disabled"),e.children("div").addClass(b).attr("role","region").attr(M,!0).hide().parent().attr(A,!1),(r=e.children(S))[0]&&(a&&a.removeAttr(H).children(S).removeClass(T),r.addClass(T),this._selected=e.attr(H,!0)),e.children(g)[0]||(s="<span class='k-link'/>",o&&o[i]&&e[0].parentNode==h&&(s='<a class="k-link k-header" href="'+o[i]+'"/>'),e.contents().filter(function(){return!(this.nodeName.match(p)||3==this.nodeType&&!t.trim(this.nodeValue))}).wrapAll(s)),e.parent(".k-panelbar")[0]&&e.children(g).addClass("k-header")},_click:function(t){var e,i,n,s=this.element;if(!t.parents("li."+C).length&&t.closest(".k-widget")[0]==s[0]){var r=t.closest(g),a=r.closest(_);this._updateSelected(r);var o=a.children(".k-group,.k-content"),l=this.dataItem(a);if(!o.length&&(this.options.loadOnDemand&&l&&l.hasChildren||this._hasChildItems(a)||a.content||a.contentUrl)&&(o=this._addGroupElement(a)),i=a.find(k).add(a.find(x)),e=!((!(n=r.attr("href"))||"#"!=n.charAt(n.length-1)&&-1==n.indexOf("#"+this.element[0].id+"-"))&&!i.length),i.data("animating"))return e;if(this._triggerEvent("select",a)&&(e=!0),!1!==e){if("single"==this.options.expandMode&&this._collapseAllExpanded(a))return e;if(i.length){var h=i.is(P);this._triggerEvent(h?"collapse":"expand",a)||(e=this._toggleItem(a,h))}return e}}},_hasChildItems:function(t){return t.items&&t.items.length>0||t.hasChildren},_toggleItem:function(t,e,i){var n,s,r=t.find(k),a=t.find(g).attr("href"),o=this.dataItem(t),l=!e,h=o&&o.loaded();return o&&!i&&o.expanded!==l?(o.set("expanded",l),n=o.hasChildren||!!o.content||!!o.contentUrl):(!o||i&&"true"!==i||h||o.content||o.contentUrl?r.length?(this._toggleGroup(r,e),n=!0):(s=t.children("."+b)).length&&(n=!0,s.is(":empty")&&void 0!==a?this._ajaxRequest(t,s,e):this._toggleGroup(s,e)):(this.options.loadOnDemand&&this._progress(t,!0),t.children(".k-group,.k-content").remove(),n=o.hasChildren,o.load()),n)},_toggleGroup:function(t,e){var i=this,n=i.options.animation,s=n.expand,r=n.collapse&&"effects"in n.collapse,o=a({},n.expand,n.collapse);r||(o=a(o,{reverse:!0})),t.is(P)==e?(t.attr(M,!!e),t.parent().attr(A,!e).toggleClass(y,!e).find("> .k-link > .k-panelbar-collapse,> .k-link > .k-panelbar-expand").toggleClass("k-i-arrow-60-up",!e).toggleClass("k-panelbar-collapse",!e).toggleClass("k-i-arrow-60-down",e).toggleClass("k-panelbar-expand",e),e?(s=a(o,{hide:!0})).complete=function(){i._animationCallback()}:s=a({complete:function(t){i._triggerEvent("activate",t.closest(_)),i._animationCallback()}},s),t.kendoStop(!0,!0).kendoAnimate(s)):i._animating=!1},_animationCallback:function(){this.trigger("complete"),this._animating=!1},_addGroupElement:function(e){var i=t('<ul role="group" aria-hidden="true" class="k-group k-panel" style="display:none"></ul>');return e.append(i),i},_collapseAllExpanded:function(e){var i,n=this,s=!1,r=e.find(k).add(e.find(x));return r.is(P)&&(s=!0),r.is(P)||0===r.length||((i=e.siblings()).find(k).add(i.find(x)).filter(function(){return t(this).is(P)}).each(function(e,i){i=t(i),(s=n._triggerEvent("collapse",i.closest(_)))||n._toggleGroup(i,!0)}),n.one("complete",function(){setTimeout(function(){i.each(function(t,e){var i=n.dataItem(e);i&&i.set("expanded",!1)})})})),s},_ajaxRequest:function(e,i,n){var s=this,r=e.find(".k-panelbar-collapse, .k-panelbar-expand"),a=e.find(g),o=setTimeout(function(){r.addClass("k-i-loading")},100),l=a.attr("href");t.ajax({type:"GET",cache:!1,url:l,dataType:"html",data:{},error:function(t,e){r.removeClass("k-i-loading"),s.trigger("error",{xhr:t,status:e})&&this.complete()},complete:function(){clearTimeout(o),r.removeClass("k-i-loading")},success:function(t){function r(){return{elements:i.get()}}try{s.angular("cleanup",r),i.html(t),s.angular("compile",r)}catch(t){var a=window.console;a&&a.error&&a.error(t.name+": "+t.message+" in "+l),this.error(this.xhr,"error")}s._toggleGroup(i,n),s.trigger("contentLoad",{item:e[0],contentElement:i[0]})}})},_triggerEvent:function(t,e){return this.trigger(t,{item:e[0]})},_updateSelected:function(t,e){var i=this.element,n=t.parent(_),s=this._selected,r=this.dataItem(n);s&&s.removeAttr(H),this._selected=n.attr(H,!0),i.find(E).removeClass(T),i.find("> ."+I+", .k-panel > ."+I).removeClass(I),t.addClass(T),t.parentsUntil(i,_).filter(":has(.k-header)").addClass(I),this._current(n[0]?n:null),r&&r.set("selected",!0),e||this.trigger("change")},_animations:function(t){t&&"animation"in t&&!t.animation&&(t.animation={expand:{effects:{}},collapse:{hide:!0,effects:{}}})},renderItem:function(t){t=a({panelBar:this,group:{}},t);var e=this.templates.empty,i=t.item;return this.templates.item(a(t,{itemWrapper:this.templates.itemWrapper,renderContent:this.renderContent,arrow:this._hasChildItems(i)||i.content||i.contentUrl?this.templates.arrow:e,subGroup:!t.loadOnDemand||i.expanded?this.renderGroup:e},V))},renderGroup:function(t){return(this.templates||t.panelBar.templates).group(a({renderItems:function(t){for(var e="",i=0,n=t.items,s=n?n.length:0,r=a({length:s},t.group);i<s;i++)e+=t.panelBar.renderItem(a(t,{group:r,item:a({index:i},n[i])}));return e}},t,V))},renderContent:function(t){return t.panelBar.templates.content(a(t,V))}});n.ui.plugin(B)}(window.kendo.jQuery),window.kendo}),("function"==typeof define&&define.amd?define:function(t,e,i){(i||e)()})("kendo.window",["kendo.draganddrop","kendo.popup"],function(){return function(t,e){var i,n=window.kendo,s=n.ui.Widget,r=n.ui.Popup.TabKeyTrap,a=n.ui.Draggable,o=t.isPlainObject,l=n._activeElement,h=n._outerWidth,u=n._outerHeight,c=t.proxy,d=t.extend,p=t.each,f=n.template,m=".kendoWindow",g=".k-window",_=".k-window-title",v=_+"bar",w=".k-window-content",b=".k-resize-handle",y=".k-overlay",k="k-content-frame",x=":visible",C="overflow",T="original-overflow-rule",S=".k-window-actions .k-i-window-minimize,.k-window-actions .k-i-window-maximize",I=".k-i-pin,.k-i-unpin",D=".k-window-titlebar .k-window-action",F=".k-window-titlebar .k-i-refresh",E="WindowEventsHandled",O=/^0[a-z]*$/i,A=n.isLocalUrl;function M(t){return void 0!==t}function H(t,e,i){return Math.max(Math.min(parseInt(t,10),i===1/0?i:parseInt(i,10)),parseInt(e,10))}function P(){return!this.type||this.type.toLowerCase().indexOf("script")>=0}var z=s.extend({init:function(e,i){var a,l,h,u,d,p,f,b,y={},C=i&&i.actions&&!i.actions.length;s.fn.init.call(this,e,i),u=(i=this.options).position,e=this.element,d=i.content,f=t(window),C&&(i.actions=[]),this.appendTo=t(i.appendTo),d&&!o(d)&&(d=i.content={url:d}),e.find("script").filter(P).remove(),e.parent().is(this.appendTo)||void 0!==u.top&&void 0!==u.left||(e.is(x)?y=e.offset():(l=e.css("visibility"),h=e.css("display"),e.css({visibility:"hidden",display:""}),y=e.offset(),e.css({visibility:l,display:h})),void 0===u.top&&(u.top=y.top),void 0===u.left&&(u.left=y.left)),M(i.visible)&&null!==i.visible||(i.visible=e.is(x)),a=this.wrapper=e.closest(g),e.is(".k-content")&&a[0]||(e.addClass("k-window-content k-content"),this._createWindow(e,i),a=this.wrapper=e.closest(g),this._dimensions()),this._position(),d&&this.refresh(d),i.visible&&this.toFront(),p=a.children(w),this._tabindex(p),i.visible&&i.modal&&this._overlay(a.is(x)).css({opacity:.5}),a.on("mouseenter"+m,D,c(this._buttonEnter,this)).on("mouseleave"+m,D,c(this._buttonLeave,this)).on("click"+m,"> "+D,c(this._windowActionHandler,this)).on("keydown"+m,c(this._keydown,this)).on("focus"+m,c(this._focus,this)).on("blur"+m,c(this._blur,this)),p.on("keydown"+m,c(this._keydown,this)).on("focus"+m,c(this._focus,this)).on("blur"+m,c(this._blur,this)),p.find("."+k)[0]&&!f.data(E)&&(f.on("blur"+m,function(){var e=t(document.activeElement).parent(w);e.length&&n.widgetInstance(e)._focus()}),f.on("focus"+m,function(){t(w).not(".k-dialog-content").each(function(e,i){n.widgetInstance(t(i))._blur()})}),f.data(E,!0)),this._resizable(),this._draggable(),i.pinned&&this.wrapper.is(":visible")&&this.pin(),(b=e.attr("id"))&&(b+="_wnd_title",a.children(v).children(_).attr("id",b),p.attr({role:"dialog","aria-labelledby":b})),a.add(a.children(".k-resize-handle,"+v)).on("mousedown"+m,c(this.toFront,this)),this.touchScroller=n.touchScroller(e),this._resizeHandler=c(this._onDocumentResize,this),this._marker=n.guid().substring(0,8),t(window).on("resize"+m+this._marker,this._resizeHandler),i.visible&&(this.trigger("open"),this.trigger("activate")),n.notify(this),this.options.modal&&(this._tabKeyTrap=new r(a),this._tabKeyTrap.trap(),this._tabKeyTrap.shouldTrap=function(){return p.data("isFront")})},_buttonEnter:function(e){t(e.currentTarget).addClass("k-state-hover")},_buttonLeave:function(e){t(e.currentTarget).removeClass("k-state-hover")},_focus:function(){this.wrapper.addClass("k-state-focused")},_blur:function(){this.wrapper.removeClass("k-state-focused")},_dimensions:function(){var t=this.wrapper,e=this.options,i=e.width,n=e.height,s=e.maxHeight,r=["minWidth","minHeight","maxWidth","maxHeight"];this.title(e.title);for(var a=0;a<r.length;a++){var o=e[r[a]]||"";o!=1/0&&t.css(r[a],o)}s!=1/0&&this.element.css("maxHeight",s),i?isNaN(i)&&i.toString().indexOf("px")<0?t.width(i):t.width(H(i,e.minWidth,e.maxWidth)):t.width(""),n?isNaN(n)&&n.toString().indexOf("px")<0?t.height(n):t.height(H(n,e.minHeight,e.maxHeight)):t.height(""),e.visible||t.hide()},_position:function(){var t=this.wrapper,e=this.options.position;0===e.top&&(e.top=e.top.toString()),0===e.left&&(e.left=e.left.toString()),t.css({top:e.top||"",left:e.left||""})},_animationOptions:function(t){var e=this.options.animation;return e&&e[t]||{open:{effects:{}},close:{hide:!0,effects:{}}}[t]},_resize:function(){n.resize(this.element.children())},_resizable:function(){var e=this.options.resizable,n=this.wrapper;this.resizing&&(n.off("dblclick"+m).children(b).remove(),this.resizing.destroy(),this.resizing=null),e&&(n.on("dblclick"+m,v,c(function(e){t(e.target).closest(".k-window-action").length||this.toggleMaximization()},this)),p("n e s w se sw ne nw".split(" "),function(t,e){n.append(i.resizeHandle(e))}),this.resizing=new V(this)),n=null},_draggable:function(){var t=this.options.draggable;this.dragging&&(this.dragging.destroy(),this.dragging=null),t&&(this.dragging=new R(this,t.dragHandle||v))},_actions:function(){var e=this.options,s=e.actions,r=e.pinned,a=this.wrapper.children(v).find(".k-window-actions"),o=["maximize","minimize"];s=t.map(s,function(t){return t=r&&"pin"===t.toLowerCase()?"unpin":t,{name:o.indexOf(t.toLowerCase())>-1?"window-"+t:t}}),a.html(n.render(i.action,s))},setOptions:function(t){var e=JSON.parse(JSON.stringify(t));d(t.position,this.options.position),d(t.position,e.position),s.fn.setOptions.call(this,t);var i=!1!==this.options.scrollable;if(this.restore(),this._dimensions(),this._position(),this._resizable(),this._draggable(),this._actions(),void 0!==t.modal){var n=!1!==this.options.visible;this._overlay(t.modal&&n)}this.element.css(C,i?"":"hidden")},events:["open","activate","deactivate","close","minimize","maximize","refresh","resizeStart","resize","resizeEnd","dragstart","dragend","error"],options:{name:"Window",animation:{open:{effects:{zoom:{direction:"in"},fade:{direction:"in"}},duration:350},close:{effects:{zoom:{direction:"out",properties:{scale:.7}},fade:{direction:"out"}},duration:350,hide:!0}},title:"",actions:["Close"],autoFocus:!0,modal:!1,resizable:!0,draggable:!0,minWidth:90,minHeight:50,maxWidth:1/0,maxHeight:1/0,pinned:!1,scrollable:!0,position:{},content:null,visible:null,height:null,width:null,appendTo:"body",isMaximized:!1,isMinimized:!1},_closable:function(){return t.inArray("close",t.map(this.options.actions,function(t){return t.toLowerCase()}))>-1},_keydown:function(t){var e,i,s,r,a,o,l=this.options,h=n.keys,u=t.keyCode,c=this.wrapper,d=this.options.isMaximized,p=this.options.isMinimized;u==h.ESC&&this._closable()&&(t.stopPropagation(),this._close(!1)),t.target!=t.currentTarget||this._closing||(t.altKey&&82==u&&this.refresh(),t.altKey&&80==u&&(this.options.pinned?this.unpin():this.pin()),t.altKey&&u==h.UP?p?(this.restore(),this.element.focus()):d||(this.maximize(),this.element.focus()):t.altKey&&u==h.DOWN&&(p||d?d&&(this.restore(),this.element.focus()):(this.minimize(),this.wrapper.focus())),!l.draggable||t.ctrlKey||t.altKey||d||(e=n.getOffset(c),u==h.UP?i=c.css("top",e.top-10):u==h.DOWN?i=c.css("top",e.top+10):u==h.LEFT?i=c.css("left",e.left-10):u==h.RIGHT&&(i=c.css("left",e.left+10))),l.resizable&&t.ctrlKey&&!d&&!p&&(u==h.UP?(i=!0,r=c.height()-10):u==h.DOWN&&(i=!0,r=c.height()+10),u==h.LEFT?(i=!0,s=c.width()-10):u==h.RIGHT&&(i=!0,s=c.width()+10),i&&(a=H(s,l.minWidth,l.maxWidth),o=H(r,l.minHeight,l.maxHeight),isNaN(a)||(c.width(a),this.options.width=a+"px"),isNaN(o)||(c.height(o),this.options.height=o+"px"),this.resize())),i&&t.preventDefault())},_overlay:function(e){var i=this.appendTo.children(y),n=this.wrapper;return i.length||(i=t("<div class='k-overlay' />")),i.insertBefore(n[0]).toggle(e).css("zIndex",parseInt(n.css("zIndex"),10)-1),i},_actionForIcon:function(t){return{"k-i-close":"_close","k-i-window-maximize":"maximize","k-i-window-minimize":"minimize","k-i-window-restore":"restore","k-i-refresh":"refresh","k-i-pin":"pin","k-i-unpin":"unpin"}[/\bk-i(-\w+)+\b/.exec(t[0].className)[0]]},_windowActionHandler:function(e){if(!this._closing){var i=t(e.target).closest(".k-window-action").find(".k-icon"),n=this._actionForIcon(i);return n?(e.preventDefault(),this[n](),!1):void 0}},_modals:function(){var e=this,i=t(g).filter(function(){var i=t(this),n=e._object(i),s=n&&n.options;return s&&s.modal&&s.visible&&s.appendTo===e.options.appendTo&&i.is(x)}).sort(function(e,i){return+t(e).css("zIndex")-+t(i).css("zIndex")});return e=null,i},_object:function(t){var e=t.children(w),i=n.widgetInstance(e);if(i)return i},center:function(){var e,i,n=this.options.position,s=this.wrapper,r=t(window),a=0,o=0;return this.options.isMaximized?this:(this.options.pinned&&!this._isPinned&&this.pin(),this.options.pinned||(a=r.scrollTop(),o=r.scrollLeft()),i=o+Math.max(0,(r.width()-s.width())/2),e=a+Math.max(0,(r.height()-s.height()-parseInt(s.css("paddingTop"),10))/2),s.css({left:i,top:e}),n.top=e,n.left=i,this)},title:function(t){var e,s=this.wrapper,r=this.options,a=s.children(v),o=a.children(_);return arguments.length?(!1===t?(s.addClass("k-window-titleless"),a.remove()):(a.length?o.html(n.htmlEncode(t)):(s.prepend(i.titlebar(r)),this._actions(),a=s.children(v)),e=parseInt(u(a),10),s.css("padding-top",e),a.css("margin-top",-e)),this.options.title=t,this):o.html()},content:function(t,e){var i=this.wrapper.children(w),s=i.children(".km-scroll-container");return i=s[0]?s:i,M(t)?(this.angular("cleanup",function(){return{elements:i.children()}}),n.destroy(this.element.children()),i.empty().html(t),this.angular("compile",function(){for(var t=[],n=i.length;--n>=0;)t.push({dataItem:e});return{elements:i.children(),data:t}}),this):i.html()},open:function(){var e,i,s=this,r=s.wrapper,a=s.options,o=this._animationOptions("open"),l=r.children(w),h=t(document);if(!s.trigger("open")){if(s._closing&&r.kendoStop(!0,!0),s._closing=!1,s.toFront(),a.autoFocus&&s.element.focus(),a.visible=!0,a.modal){if(i=!!s._modals().length,(e=s._overlay(i)).kendoStop(!0,!0),o.duration&&n.effects.Fade&&!i){var u=n.fx(e).fadeIn();u.duration(o.duration||0),u.endValue(.5),u.play()}else e.css("opacity",.5);e.show(),t(window).on("focus",function(){l.data("isFront")&&!t(document.activeElement).closest(l).length&&s.element.focus()})}r.is(x)||(l.css(C,"hidden"),r.show().kendoStop().kendoAnimate({effects:o.effects,duration:o.duration,complete:c(this._activate,this)}))}return a.isMaximized&&(s._documentScrollTop=h.scrollTop(),s._documentScrollLeft=h.scrollLeft(),s._stopDocumentScrolling()),a.pinned&&!s._isPinned&&s.pin(),s},_activate:function(){var t=!1!==this.options.scrollable;this.options.autoFocus&&this.element.focus(),this.element.css(C,t?"":"hidden"),n.resize(this.element.children()),this.trigger("activate")},_removeOverlay:function(e){var i=this._modals(),s=this.options,r=s.modal&&!i.length,a=s.modal?this._overlay(!0):t(void 0),o=this._animationOptions("close");if(r)if(!e&&o.duration&&n.effects.Fade){var l=n.fx(a).fadeOut();l.duration(o.duration||0),l.startValue(.5),l.play()}else this._overlay(!1).remove();else i.length&&this._object(i.last())._overlay(!0)},_close:function(e){var i,n=this.wrapper,s=this.options,r=this._animationOptions("open"),a=this._animationOptions("close"),o=t(document);this._closing||(i=this.trigger("close",{userTriggered:!e}),this._closing=!i,n.is(x)&&!i&&(s.visible=!1,t(g).each(function(e,i){var s=t(i).children(w);i!=n&&s.find("> ."+k).length>0&&s.children(y).remove()}),this._removeOverlay(),n.kendoStop().kendoAnimate({effects:a.effects||r.effects,reverse:!0===a.reverse,duration:a.duration,complete:c(this._deactivate,this)})),this.options.isMaximized&&(this._enableDocumentScrolling(),this._documentScrollTop&&this._documentScrollTop>0&&o.scrollTop(this._documentScrollTop),this._documentScrollLeft&&this._documentScrollLeft>0&&o.scrollLeft(this._documentScrollLeft)))},_deactivate:function(){if(this.wrapper.hide().css("opacity",""),this.trigger("deactivate"),this.options.modal){var t=this._object(this._modals().last());t&&t.toFront()}},close:function(){return this._close(!0),this},_actionable:function(e){return t(e).is(D+","+D+" .k-icon,:input,a")},_shouldFocus:function(e){var i=l(),n=this.element;return this.options.autoFocus&&!t(i).is(n)&&!this._actionable(e)&&(!n.find(i).length||!n.find(e).length)},toFront:function(e){var n=this,s=n.wrapper,r=s[0],a=+s.css("zIndex"),o=a,l=e&&e.target||null;if(t(g).each(function(e,n){var s=t(n),o=s.css("zIndex"),l=s.children(w);isNaN(o)||(a=Math.max(+o,a)),l.data("isFront",n==r),n!=r&&l.find("> ."+k).length>0&&l.append(i.overlay)}),(!s[0].style.zIndex||o<a)&&s.css("zIndex",a+2),n.element.find("> .k-overlay").remove(),n._shouldFocus(l)){n.isMinimized()?n.wrapper.focus():t(l).is(y)?setTimeout(function(){n.element.focus()}):n.element.focus();var h=t(window).scrollTop(),u=parseInt(s.position().top,10);!n.options.pinned&&u>0&&u<h&&(h>0?t(window).scrollTop(u):s.css("top",h))}return s=null,n},toggleMaximization:function(){return this._closing?this:this[this.options.isMaximized?"restore":"maximize"]()},restore:function(){var e=this.options,i=e.minHeight,n=this.restoreOptions,s=t(document);return e.isMaximized||e.isMinimized?(i&&i!=1/0&&this.wrapper.css("min-height",i),this.wrapper.css({position:e.pinned?"fixed":"absolute",left:n.left,top:n.top,width:n.width,height:n.height}).removeClass("k-window-maximized").find(".k-window-content,.k-resize-handle").show().end().find(".k-window-titlebar .k-i-window-restore").parent().remove().end().end().find(S).parent().show().end().end().find(I).parent().show(),e.isMaximized?this.wrapper.find(".k-i-window-maximize").parent().focus():e.isMinimized&&this.wrapper.find(".k-i-window-minimize").parent().focus(),this.options.width=n.width,this.options.height=n.height,this._enableDocumentScrolling(),this._documentScrollTop&&this._documentScrollTop>0&&s.scrollTop(this._documentScrollTop),this._documentScrollLeft&&this._documentScrollLeft>0&&s.scrollLeft(this._documentScrollLeft),e.isMaximized=e.isMinimized=!1,this.wrapper.removeAttr("tabindex"),this.wrapper.removeAttr("aria-labelled-by"),this.resize(),this):this},_sizingAction:function(t,e){var n=this.wrapper,s=n[0].style,r=this.options;return r.isMaximized||r.isMinimized?this:(this.restoreOptions={width:s.width,height:s.height},n.children(b).hide().end().children(v).find(S).parent().hide().eq(0).before(i.action({name:"window-restore"})),e.call(this),this.wrapper.children(v).find(I).parent().toggle("maximize"!==t),this.trigger(t),n.find(".k-i-window-restore").parent().focus(),this)},maximize:function(){return this._sizingAction("maximize",function(){var e=this.wrapper,i=e.position(),n=t(document);d(this.restoreOptions,{left:i.left,top:i.top}),e.css({left:0,top:0,position:"fixed"}).addClass("k-window-maximized"),this._documentScrollTop=n.scrollTop(),this._documentScrollLeft=n.scrollLeft(),this._stopDocumentScrolling(),this.options.isMaximized=!0,this._onDocumentResize()}),this},_stopDocumentScrolling:function(){var e=t("body");this._storeOverflowRule(e),e.css(C,"hidden");var i=t("html");this._storeOverflowRule(i),i.css(C,"hidden")},_enableDocumentScrolling:function(){this._restoreOverflowRule(t(document.body)),this._restoreOverflowRule(t("html"))},_storeOverflowRule:function(t){if(!this._isOverflowStored(t)){var e=t.get(0).style.overflow;"string"==typeof e&&t.data(T,e)}},_isOverflowStored:function(t){return"string"==typeof t.data(T)},_restoreOverflowRule:function(t){var e=t.data(T);null!==e&&void 0!==e?(t.css(C,e),t.removeData(T)):t.css(C,"")},isMaximized:function(){return this.options.isMaximized},minimize:function(){return this._sizingAction("minimize",function(){this.wrapper.css({height:"",minHeight:""}),this.element.hide(),this.options.isMinimized=!0}),this.wrapper.attr("tabindex",0),this.wrapper.attr("aria-labelled-by",this.element.attr("aria-labelled-by")),this},isMinimized:function(){return this.options.isMinimized},pin:function(){var e=t(window),i=this.wrapper,n=parseInt(i.css("top"),10),s=parseInt(i.css("left"),10);this.options.isMaximized||(i.css({position:"fixed",top:n-e.scrollTop(),left:s-e.scrollLeft()}),i.children(v).find(".k-i-pin").addClass("k-i-unpin").removeClass("k-i-pin"),this._isPinned=!0,this.options.pinned=!0)},unpin:function(){var e=t(window),i=this.wrapper,n=parseInt(i.css("top"),10),s=parseInt(i.css("left"),10);this.options.isMaximized||(i.css({position:"",top:n+e.scrollTop(),left:s+e.scrollLeft()}),i.children(v).find(".k-i-unpin").addClass("k-i-pin").removeClass("k-i-unpin"),this._isPinned=!1,this.options.pinned=!1)},_onDocumentResize:function(){var e,i,s=this.wrapper,r=t(window),a=n.support.zoomLevel();if(this.options.isMaximized){var o=parseInt(s.css("border-left-width"),10)+parseInt(s.css("border-right-width"),10),l=parseInt(s.css("border-top-width"),10)+parseInt(s.css("border-bottom-width"),10);e=r.width()/a-o,i=r.height()/a-parseInt(s.css("padding-top"),10)-l,s.css({width:e,height:i}),this.options.width=e,this.options.height=i,this.resize()}},refresh:function(e){var n,s,r,a=this.options,l=t(this.element);return o(e)||(e={url:e}),e=d({},a.content,e),s=M(a.iframe)?a.iframe:e.iframe,(r=e.url)?(M(s)||(s=!A(r)),s?((n=l.find("."+k)[0])?n.src=r||n.src:l.html(i.contentFrame(d({},a,{content:e}))),l.find("."+k).unbind("load"+m).on("load"+m,c(this._triggerRefresh,this))):this._ajaxRequest(e)):(e.template&&this.content(f(e.template)({})),this.trigger("refresh")),l.toggleClass("k-window-iframecontent",!!s),this},_triggerRefresh:function(){this.trigger("refresh")},_ajaxComplete:function(){clearTimeout(this._loadingIconTimeout),this.wrapper.find(F).removeClass("k-i-loading")},_ajaxError:function(t,e){this.trigger("error",{status:e,xhr:t})},_ajaxSuccess:function(t){return function(e){var i=e;t&&(i=f(t)(e||{})),this.content(i,e),this.element.prop("scrollTop",0),this.trigger("refresh")}},_showLoading:function(){this.wrapper.find(F).addClass("k-i-loading")},_ajaxRequest:function(e){this._loadingIconTimeout=setTimeout(c(this._showLoading,this),100),t.ajax(d({type:"GET",dataType:"html",cache:!1,error:c(this._ajaxError,this),complete:c(this._ajaxComplete,this),success:c(this._ajaxSuccess(e.template),this)},e))},_destroy:function(){this.resizing&&this.resizing.destroy(),this.dragging&&this.dragging.destroy(),this.wrapper.off(m).children(w).off(m).end().find(".k-resize-handle,.k-window-titlebar").off(m),t(window).off("resize"+m+this._marker),t(window).off(m),clearTimeout(this._loadingIconTimeout),s.fn.destroy.call(this),this.unbind(void 0),n.destroy(this.wrapper),this._removeOverlay(!0)},destroy:function(){this._destroy(),this.wrapper.empty().remove(),this.wrapper=this.appendTo=this.element=t()},_createWindow:function(){var e,s,r=this.element,a=this.options,o=n.support.isRtl(r);!1===a.scrollable&&r.css("overflow","hidden"),s=t(i.wrapper(a)),e=r.find("iframe:not(.k-content)").map(function(){var t=this.getAttribute("src");return this.src="",t}),s.toggleClass("k-rtl",o).appendTo(this.appendTo).append(r).find("iframe:not(.k-content)").each(function(t){this.src=e[t]}),s.find(".k-window-title").css(o?"left":"right",h(s.find(".k-window-actions"))+10),r.css("visibility","").show(),r.find("[data-role=editor]").each(function(){var e=t(this).data("kendoEditor");e&&e.refresh()}),s=r=null}});function V(t){this.owner=t,this._preventDragging=!1,this._draggable=new a(t.wrapper,{filter:">"+b,group:t.wrapper.id+"-resizing",dragstart:c(this.dragstart,this),drag:c(this.drag,this),dragend:c(this.dragend,this)}),this._draggable.userEvents.bind("press",c(this.addOverlay,this)),this._draggable.userEvents.bind("release",c(this.removeOverlay,this))}function R(t,e){this.owner=t,this._preventDragging=!1,this._draggable=new a(t.wrapper,{filter:e,group:t.wrapper.id+"-moving",dragstart:c(this.dragstart,this),drag:c(this.drag,this),dragend:c(this.dragend,this),dragcancel:c(this.dragcancel,this)}),this._draggable.userEvents.stopPropagation=!1}i={wrapper:f("<div class='k-widget k-window' />"),action:f("<a role='button' href='\\#' class='k-button k-bare k-button-icon k-window-action' aria-label='#= name #'><span class='k-icon k-i-#= name.toLowerCase() #'></span></a>"),titlebar:f("<div class='k-window-titlebar k-header'><span class='k-window-title'>#: title #</span><div class='k-window-actions' /></div>"),overlay:"<div class='k-overlay' />",contentFrame:f("<iframe frameborder='0' title='#= title #' class='"+k+"' src='#= content.url #'>This page requires frames in order to show content</iframe>"),resizeHandle:f("<div class='k-resize-handle k-resize-#= data #'></div>")},V.prototype={addOverlay:function(){this.owner.wrapper.append(i.overlay)},removeOverlay:function(){this.owner.wrapper.find(y).remove()},dragstart:function(e){var i=this.owner,s=i.wrapper;if(this._preventDragging=i.trigger("resizeStart"),!this._preventDragging){this.elementPadding=parseInt(s.css("padding-top"),10),this.initialPosition=n.getOffset(s,"position"),this.resizeDirection=e.currentTarget.prop("className").replace("k-resize-handle k-resize-",""),this.initialSize={width:s.width(),height:s.height()},this.containerOffset=n.getOffset(i.appendTo,"position");var r=s.offsetParent();if(r.is("html"))this.containerOffset.top=this.containerOffset.left=0;else{var a=r.css("margin-top"),o=r.css("margin-left");if(!O.test(a)||!O.test(o)){var l=function(e){for(var i={top:e.offsetTop,left:e.offsetLeft},n=e.offsetParent;n;){i.top+=n.offsetTop,i.left+=n.offsetLeft;var s=t(n).css("overflowX"),r=t(n).css("overflowY");"auto"!==r&&"scroll"!==r||(i.top-=n.scrollTop),"auto"!==s&&"scroll"!==s||(i.left-=n.scrollLeft),n=n.offsetParent}return i}(s[0]),h=l.left-this.containerOffset.left-this.initialPosition.left,u=l.top-this.containerOffset.top-this.initialPosition.top;this._relativeElMarginLeft=h>1?h:0,this._relativeElMarginTop=u>1?u:0,this.initialPosition.left+=this._relativeElMarginLeft,this.initialPosition.top+=this._relativeElMarginTop}}s.children(b).not(e.currentTarget).hide(),t("body").css("cursor",e.currentTarget.css("cursor"))}},drag:function(e){if(!this._preventDragging){var i,n,s,r,a=this.owner,o=a.wrapper,l=a.options,h=this.resizeDirection,u=this.containerOffset,c=this.initialPosition,d=this.initialSize,p=Math.max(e.x.location,0),f=Math.max(e.y.location,0);h.indexOf("e")>=0?(i=p-c.left-u.left,o.width(H(i,l.minWidth,l.maxWidth))):h.indexOf("w")>=0&&(i=H((r=c.left+d.width+u.left)-p,l.minWidth,l.maxWidth),o.css({left:r-i-u.left-(this._relativeElMarginLeft||0),width:i}));var m=f;a.options.pinned&&(m-=t(window).scrollTop()),h.indexOf("s")>=0?(n=m-c.top-this.elementPadding-u.top,o.height(H(n,l.minHeight,l.maxHeight))):h.indexOf("n")>=0&&(n=H((s=c.top+d.height+u.top)-m,l.minHeight,l.maxHeight),o.css({top:s-n-u.top-(this._relativeElMarginTop||0),height:n})),i&&(a.options.width=i+"px"),n&&(a.options.height=n+"px"),a.resize()}},dragend:function(e){if(!this._preventDragging){var i=this.owner,n=i.wrapper;return n.children(b).not(e.currentTarget).show(),t("body").css("cursor",""),i.touchScroller&&i.touchScroller.reset(),27==e.keyCode&&n.css(this.initialPosition).css(this.initialSize),i.trigger("resizeEnd"),!1}},destroy:function(){this._draggable&&this._draggable.destroy(),this._draggable=this.owner=null}},R.prototype={dragstart:function(e){var s=this.owner,r=s.element,a=r.find(".k-window-actions"),o=n.getOffset(s.appendTo);this._preventDragging=s.trigger("dragstart")||!s.options.draggable,this._preventDragging||(s.initialWindowPosition=n.getOffset(s.wrapper,"position"),s.initialPointerPosition={left:e.x.client,top:e.y.client},s.startPosition={left:e.x.client-s.initialWindowPosition.left,top:e.y.client-s.initialWindowPosition.top},a.length>0?s.minLeftPosition=h(a)+parseInt(a.css("right"),10)-h(r):s.minLeftPosition=20-h(r),s.minLeftPosition-=o.left,s.minTopPosition=-o.top,s.wrapper.append(i.overlay).children(b).hide(),t("body").css("cursor",e.currentTarget.css("cursor")))},drag:function(e){if(!this._preventDragging){var i=this.owner,s=i.options.position;s.top=Math.max(e.y.client-i.startPosition.top,i.minTopPosition),s.left=Math.max(e.x.client-i.startPosition.left,i.minLeftPosition),n.support.transforms?t(i.wrapper).css("transform","translate("+(e.x.client-i.initialPointerPosition.left)+"px, "+(e.y.client-i.initialPointerPosition.top)+"px)"):t(i.wrapper).css(s)}},_finishDrag:function(){var e=this.owner;e.wrapper.children(b).toggle(!e.options.isMinimized).end().find(y).remove(),t("body").css("cursor","")},dragcancel:function(t){this._preventDragging||(this._finishDrag(),t.currentTarget.closest(g).css(this.owner.initialWindowPosition))},dragend:function(){if(!this._preventDragging)return t(this.owner.wrapper).css(this.owner.options.position).css("transform",""),this._finishDrag(),this.owner.trigger("dragend"),!1},destroy:function(){this._draggable&&this._draggable.destroy(),this._draggable=this.owner=null}},n.ui.plugin(z)}(window.kendo.jQuery),window.kendo});