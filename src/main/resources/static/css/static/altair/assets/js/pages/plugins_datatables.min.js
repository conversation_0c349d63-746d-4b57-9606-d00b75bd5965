$(function(){altair_datatables.dt_default(),altair_datatables.dt_scroll(),altair_datatables.dt_individual_search(),altair_datatables.dt_colVis(),altair_datatables.dt_tableExport()}),altair_datatables={dt_default:function(){var t=$("#dt_default");t.length&&t.DataTable()},dt_scroll:function(){var t=$("#dt_scroll");t.length&&t.DataTable({scrollY:"200px",scrollCollapse:!1,paging:!1})},dt_individual_search:function(){var t=$("#dt_individual_search");t.length&&(t.find("tfoot th").each(function(){var a=t.find("tfoot th").eq($(this).index()).text();$(this).html('<input type="text" class="md-input" placeholder="'+a+'" />')}),altair_md.inputs(),t.DataTable().columns().every(function(){var t=this;$("input",this.footer()).on("keyup change",function(){t.search(this.value).draw()})}))},dt_colVis:function(){var t=$("#dt_colVis"),a=t.prev(".dt_colVis_buttons");t.length&&t.DataTable({buttons:[{extend:"colvis",fade:0}]}).buttons().container().appendTo(a)},dt_tableExport:function(){var t=$("#dt_tableExport"),a=t.prev(".dt_colVis_buttons");t.length&&t.DataTable({buttons:[{extend:"copyHtml5",text:'<i class="uk-icon-files-o"></i> Copy',titleAttr:"Copy"},{extend:"print",text:'<i class="uk-icon-print"></i> Print',titleAttr:"Print"},{extend:"excelHtml5",text:'<i class="uk-icon-file-excel-o"></i> XLSX',titleAttr:""},{extend:"csvHtml5",text:'<i class="uk-icon-file-text-o"></i> CSV',titleAttr:"CSV"},{extend:"pdfHtml5",text:'<i class="uk-icon-file-pdf-o"></i> PDF',titleAttr:"PDF"}]}).buttons().container().appendTo(a)}};