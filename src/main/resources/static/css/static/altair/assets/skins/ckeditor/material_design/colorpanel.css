/*
Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
colorpanel.css (part of editor.css)
=====================================

The color panel is related to the contents part of the panels that are
displayed when clicking the color buttons of the toolbar. See panels.css for
styles related to the outer part of panels.

The following is the visual representation of the color panel contents:

+-- .cke_panel_block.cke_colorblock --+
| +-- a.cke_colorauto --------------+ |
| |                                 | |
| |         AUTOMATIC COLOR         | |
| |                                 | |
| +---------------------------------+ |
| +-- table ------------------------+ |
| |                                 | |
| |          COLOR PALETTE          | |
| |                                 | |
| |---------------------------------| |
| |          "More Colors"          | |
| +---------------------------------+ |
+-------------------------------------+

The AUTOMATIC COLOR section is an <a> containing a table with two cells with
the following contents:

+-- TD -----------------+ +-- TD -----------------+
| +-- .cke_colorbox --+ | |                       |
| |                   | | |      "Automatic"      |
| +-------------------+ | |                       |
+-----------------------+ +-----------------------+

The COLOR PALETTE section instead is a table with a variable number of cells
(by default 8). Each cell represents a color box, with the following structure:

+-- A.cke_colorbox ---------+
| +-- SPAN.cke_colorbox --+ |
| |                       | |
| +-----------------------+ |
+---------------------------+
*/

/* The container of the color palette. */
.cke_colorblock
{
	padding: 3px;
	font-size: 11px;
	font-family: 'Microsoft Sans Serif', Tahoma, Arial, Verdana, Sans-Serif;
}

.cke_colorblock,
.cke_colorblock a
{
	text-decoration: none;
	color: #000;
}

/* The box which is to represent a single color on the color palette.
   It is a small, square-shaped element which can be selected from the palette. */
span.cke_colorbox
{
	width: 10px;
	height: 10px;
	border: #808080 1px solid;
	float: left;
}

.cke_rtl span.cke_colorbox
{
	float: right;
}

/* The wrapper of the span.cke_colorbox. It provides an extra border and padding. */
a.cke_colorbox
{
	border: #fff 1px solid;
	padding: 2px;
	float: left;
	width: 12px;
	height: 12px;
}

.cke_rtl a.cke_colorbox
{
	float: right;
}

/* Different states of the a.cke_colorbox wrapper. */
a:hover.cke_colorbox,
a:focus.cke_colorbox,
a:active.cke_colorbox
{
	border: #b6b6b6 1px solid;
	background-color: #e5e5e5;
}

/* Buttons which are visible at the top/bottom of the color palette:
   - cke_colorauto (TOP) applies the automatic color.
   - cke_colormore (BOTTOM) executes the color dialog.
*/
a.cke_colorauto,
a.cke_colormore
{
	border: #fff 1px solid;
	padding: 2px;
	display: block;
	cursor: pointer;
}

/* Different states of cke_colorauto/cke_colormore buttons. */
a:hover.cke_colorauto,
a:hover.cke_colormore,
a:focus.cke_colorauto,
a:focus.cke_colormore,
a:active.cke_colorauto,
a:active.cke_colormore
{
	border: #b6b6b6 1px solid;
	background-color: #e5e5e5;
}
