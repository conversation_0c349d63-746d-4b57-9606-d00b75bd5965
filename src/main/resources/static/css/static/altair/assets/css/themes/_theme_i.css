.app_theme_i .uk-alert {
  background: #fbc02d;
}
.app_theme_i .md-btn-primary,
.app_theme_i .md-btn-primary:hover,
.app_theme_i .md-btn-primary:focus,
.app_theme_i .md-btn-primary:active {
  background: #fbc02d;
  color: #fff;
}
.app_theme_i .md-btn-flat-primary,
.app_theme_i .md-btn-flat-primary:hover,
.app_theme_i .md-btn-flat-primary:focus,
.app_theme_i .md-btn-flat-primary:active {
  color: #f57f17;
}
.app_theme_i .md-btn-flat-primary:hover,
.app_theme_i .md-btn-flat-primary:focus,
.app_theme_i .md-btn-flat-primary:active {
  background: #ffffff;
}
.app_theme_i .uk-badge-primary {
  background: #fbc02d;
}
.app_theme_i .md-card-list-wrapper .md-card-list > ul > li.item-shown.md-card-list-item-selected:before {
  background: #fffde7;
}
.app_theme_i .md-card-list-wrapper .md-card-list .md-card-list-item-selected {
  background: #fffde7;
}
.app_theme_i .md-card-list-wrapper .md-card-list .md-card-list-item-selected.item-shown {
  background: #fff;
}
.app_theme_i .md-card-list-wrapper .md-card-list .md-card-list-item-selected.item-shown:before {
  background: #fffde7;
}
.app_theme_i .md-fab.md-fab-accent {
  background: #00acc1;
}
.app_theme_i .md-list .uk-nestable-list > li.md-list-item-active,
.app_theme_i .md-list > li.md-list-item-active {
  color: #00acc1;
}
.app_theme_i .md-list-addon > li.md-list-item-active .md-list-addon-element,
.app_theme_i .md-list-addon > li.md-list-item-active .md-list-addon-element .material-icons {
  color: #00acc1;
}
.app_theme_i .uk-pagination > li > a:hover {
  background: #8ef3ff;
}
.app_theme_i .uk-pagination > li.uk-active > a,
.app_theme_i .uk-pagination > li.uk-active > span {
  background: #00acc1;
  color: #fff;
}
.app_theme_i .uk-subnav-pill > .uk-active > * {
  background: #00acc1;
}
.app_theme_i .uk-tab > li > a:hover,
.app_theme_i .uk-tab > li > a:focus {
  border-bottom-color: #feedc3;
}
.app_theme_i .uk-tab > li.uk-active > a {
  border-bottom-color: #fbc02d;
}
.app_theme_i .uk-tab-bottom li > a:hover,
.app_theme_i .uk-tab-bottom li > a:focus {
  border-top-color: #feedc3;
}
.app_theme_i .uk-tab-bottom li.uk-active > a {
  border-top-color: #fbc02d;
}
.app_theme_i .uk-tab-left li > a:hover,
.app_theme_i .uk-tab-left li > a:focus {
  border-right-color: #feedc3;
}
.app_theme_i .uk-tab-left li.uk-active > a {
  border-right-color: #fbc02d;
}
.app_theme_i .uk-tab-right li > a:hover,
.app_theme_i .uk-tab-right li > a:focus {
  border-left-color: #feedc3;
}
.app_theme_i .uk-tab-right li.uk-active > a {
  border-left-color: #fbc02d;
}
.app_theme_i .uk-tab-double-header li a:hover,
.app_theme_i .uk-tab-double-header li a:focus {
  border-bottom-color: #fccf5f;
}
.app_theme_i .uk-tab-double-header li.uk-active > a {
  border-bottom-color: #00acc1;
}
.app_theme_i #header_main {
  background: #fbc02d;
}
.app_theme_i .header_double_height:after {
  background: #fbc02d;
}
.app_theme_i #top_bar .top_bar_nav > li > a:hover {
  -webkit-box-shadow: inset 0 -3px 0 #fbc02d;
  box-shadow: inset 0 -3px 0 #fbc02d;
}
.app_theme_i #top_bar .top_bar_nav > li.uk-active a {
  -webkit-box-shadow: inset 0 -3px 0 #fbc02d;
  box-shadow: inset 0 -3px 0 #fbc02d;
}
.app_theme_i .user_heading {
  background: #f57f17;
}
.app_theme_i #sidebar_main .menu_section > ul > li ul li.act_item a {
  color: #00acc1;
}
.app_theme_i #sidebar_main .menu_section > ul > li.current_section > a > .menu_icon .material-icons {
  color: #00acc1;
}
.app_theme_i #sidebar_main .menu_section > ul > li.current_section > a .menu_title {
  color: #00acc1;
}
.app_theme_i .sidebar_mini #sidebar_main .menu_section > ul > li > a > .menu_title {
  background: #00acc1;
  color: #fff;
}
.app_theme_i .sidebar_mini #sidebar_main .menu_section > ul > li:hover.sidebar_submenu {
  background: #00acc1;
}
.app_theme_i .sidebar_mini #sidebar_main .menu_section > ul > li:hover.sidebar_submenu > a .menu_icon .material-icons {
  color: #fff;
}
.app_theme_i .sidebar_mini #sidebar_main .menu_section > ul > li:hover.sidebar_submenu ul {
  border-left-color: #00acc1;
}
.app_theme_i .sidebar_mini #sidebar_main .menu_section > ul > li.current_section > a > .menu_icon .material-icons {
  color: #00acc1;
}
.app_theme_i .clndr .clndr_days .clndr_days_grid .day.today > span {
  background: #00acc1;
}
.app_theme_i .DTTT_print_info h6 {
  color: #00acc1;
}
.app_theme_i .fc-unthemed .fc-button.fc-state-active:after {
  color: #00acc1;
}
.app_theme_i .fc-unthemed .fc-highlight {
  -webkit-box-shadow: inset 0 0 0 2px #fbc02d, inset 0 -1px 0 2px #fbc02d;
  box-shadow: inset 0 0 0 2px #fbc02d, inset 0 -1px 0 2px #fbc02d;
}
.app_theme_i .tablesorter-altair tbody > tr.row_highlighted > td {
  background: #fffde7;
}
.app_theme_i .tablesorter-altair .headerSortUp,
.app_theme_i .tablesorter-altair .tablesorter-headerSortUp,
.app_theme_i .tablesorter-altair .tablesorter-headerAsc,
.app_theme_i .tablesorter-altair .headerSortDown,
.app_theme_i .tablesorter-altair .tablesorter-headerSortDown,
.app_theme_i .tablesorter-altair .tablesorter-headerDesc {
  color: #00acc1;
}
.app_theme_i .wizard > .steps {
  background: #fbc02d;
}
.app_theme_i .wizard > .steps > ul > li.current a,
.app_theme_i .wizard > .steps > ul > li.current a:hover,
.app_theme_i .wizard > .steps > ul > li.current a:active {
  background: #fccf5f;
}
.app_theme_i .wizard > .steps > ul > li.done a,
.app_theme_i .wizard > .steps > ul > li.done a:hover,
.app_theme_i .wizard > .steps > ul > li.done a:active {
  background: #f57f17;
}
.app_theme_i .md-card-primary {
  border-left-color: #fbc02d;
}
.app_theme_i .waves-effect.md-btn-flat-primary {
  background: none;
}
.app_theme_i .waves-effect.md-btn-flat-primary .waves-ripple {
  background: rgba(251, 192, 45, 0.4);
}
.app_theme_i .search_list_link {
  color: #00acc1;
}
.app_theme_i .chatbox.cb_active .chatbox_header {
  background: #f57f17;
}
.app_theme_i .chatbox_content .chatbox_message.own .chatbox_message_content li > span {
  background: #fbc02d;
}
.app_theme_i .uk-table tr.row_checked td,
.app_theme_i .uk-table-hover tbody tr:hover {
  background: #fffde7;
}
