.app_theme_a .uk-alert {
  background: #3f51b5;
}
.app_theme_a .md-btn-primary,
.app_theme_a .md-btn-primary:hover,
.app_theme_a .md-btn-primary:focus,
.app_theme_a .md-btn-primary:active {
  background: #3f51b5;
  color: #fff;
}
.app_theme_a .md-btn-flat-primary,
.app_theme_a .md-btn-flat-primary:hover,
.app_theme_a .md-btn-flat-primary:focus,
.app_theme_a .md-btn-flat-primary:active {
  color: #303f9f;
}
.app_theme_a .md-btn-flat-primary:hover,
.app_theme_a .md-btn-flat-primary:focus,
.app_theme_a .md-btn-flat-primary:active {
  background: #d1d6ef;
}
.app_theme_a .uk-badge-primary {
  background: #3f51b5;
}
.app_theme_a .md-card-list-wrapper .md-card-list > ul > li.item-shown.md-card-list-item-selected:before {
  background: #e8eaf6;
}
.app_theme_a .md-card-list-wrapper .md-card-list .md-card-list-item-selected {
  background: #e8eaf6;
}
.app_theme_a .md-card-list-wrapper .md-card-list .md-card-list-item-selected.item-shown {
  background: #fff;
}
.app_theme_a .md-card-list-wrapper .md-card-list .md-card-list-item-selected.item-shown:before {
  background: #e8eaf6;
}
.app_theme_a .md-fab.md-fab-accent {
  background: #ff4081;
}
.app_theme_a .md-list .uk-nestable-list > li.md-list-item-active,
.app_theme_a .md-list > li.md-list-item-active {
  color: #ff4081;
}
.app_theme_a .md-list-addon > li.md-list-item-active .md-list-addon-element,
.app_theme_a .md-list-addon > li.md-list-item-active .md-list-addon-element .material-icons {
  color: #ff4081;
}
.app_theme_a .uk-pagination > li > a:hover {
  background: #ffd9e6;
}
.app_theme_a .uk-pagination > li.uk-active > a,
.app_theme_a .uk-pagination > li.uk-active > span {
  background: #ff4081;
  color: #fff;
}
.app_theme_a .uk-subnav-pill > .uk-active > * {
  background: #ff4081;
}
.app_theme_a .uk-tab > li > a:hover,
.app_theme_a .uk-tab > li > a:focus {
  border-bottom-color: #abb4e2;
}
.app_theme_a .uk-tab > li.uk-active > a {
  border-bottom-color: #3f51b5;
}
.app_theme_a .uk-tab-bottom li > a:hover,
.app_theme_a .uk-tab-bottom li > a:focus {
  border-top-color: #abb4e2;
}
.app_theme_a .uk-tab-bottom li.uk-active > a {
  border-top-color: #3f51b5;
}
.app_theme_a .uk-tab-left li > a:hover,
.app_theme_a .uk-tab-left li > a:focus {
  border-right-color: #abb4e2;
}
.app_theme_a .uk-tab-left li.uk-active > a {
  border-right-color: #3f51b5;
}
.app_theme_a .uk-tab-right li > a:hover,
.app_theme_a .uk-tab-right li > a:focus {
  border-left-color: #abb4e2;
}
.app_theme_a .uk-tab-right li.uk-active > a {
  border-left-color: #3f51b5;
}
.app_theme_a .uk-tab-double-header li a:hover,
.app_theme_a .uk-tab-double-header li a:focus {
  border-bottom-color: #606fc7;
}
.app_theme_a .uk-tab-double-header li.uk-active > a {
  border-bottom-color: #ff4081;
}
.app_theme_a #header_main {
  background: #3f51b5;
}
.app_theme_a .header_double_height:after {
  background: #3f51b5;
}
.app_theme_a #top_bar .top_bar_nav > li > a:hover {
  -webkit-box-shadow: inset 0 -3px 0 #3f51b5;
  box-shadow: inset 0 -3px 0 #3f51b5;
}
.app_theme_a #top_bar .top_bar_nav > li.uk-active a {
  -webkit-box-shadow: inset 0 -3px 0 #3f51b5;
  box-shadow: inset 0 -3px 0 #3f51b5;
}
.app_theme_a .user_heading {
  background: #303f9f;
}
.app_theme_a #sidebar_main .menu_section > ul > li ul li.act_item a {
  color: #ff4081;
}
.app_theme_a #sidebar_main .menu_section > ul > li.current_section > a > .menu_icon .material-icons {
  color: #ff4081;
}
.app_theme_a #sidebar_main .menu_section > ul > li.current_section > a .menu_title {
  color: #ff4081;
}
.app_theme_a .sidebar_mini #sidebar_main .menu_section > ul > li > a > .menu_title {
  background: #ff4081;
  color: #fff;
}
.app_theme_a .sidebar_mini #sidebar_main .menu_section > ul > li:hover.sidebar_submenu {
  background: #ff4081;
}
.app_theme_a .sidebar_mini #sidebar_main .menu_section > ul > li:hover.sidebar_submenu > a .menu_icon .material-icons {
  color: #fff;
}
.app_theme_a .sidebar_mini #sidebar_main .menu_section > ul > li:hover.sidebar_submenu ul {
  border-left-color: #ff4081;
}
.app_theme_a .sidebar_mini #sidebar_main .menu_section > ul > li.current_section > a > .menu_icon .material-icons {
  color: #ff4081;
}
.app_theme_a .clndr .clndr_days .clndr_days_grid .day.today > span {
  background: #ff4081;
}
.app_theme_a .DTTT_print_info h6 {
  color: #ff4081;
}
.app_theme_a .fc-unthemed .fc-button.fc-state-active:after {
  color: #ff4081;
}
.app_theme_a .fc-unthemed .fc-highlight {
  -webkit-box-shadow: inset 0 0 0 2px #3f51b5, inset 0 -1px 0 2px #3f51b5;
  box-shadow: inset 0 0 0 2px #3f51b5, inset 0 -1px 0 2px #3f51b5;
}
.app_theme_a .tablesorter-altair tbody > tr.row_highlighted > td {
  background: #e8eaf6;
}
.app_theme_a .tablesorter-altair .headerSortUp,
.app_theme_a .tablesorter-altair .tablesorter-headerSortUp,
.app_theme_a .tablesorter-altair .tablesorter-headerAsc,
.app_theme_a .tablesorter-altair .headerSortDown,
.app_theme_a .tablesorter-altair .tablesorter-headerSortDown,
.app_theme_a .tablesorter-altair .tablesorter-headerDesc {
  color: #ff4081;
}
.app_theme_a .wizard > .steps {
  background: #3f51b5;
}
.app_theme_a .wizard > .steps > ul > li.current a,
.app_theme_a .wizard > .steps > ul > li.current a:hover,
.app_theme_a .wizard > .steps > ul > li.current a:active {
  background: #606fc7;
}
.app_theme_a .wizard > .steps > ul > li.done a,
.app_theme_a .wizard > .steps > ul > li.done a:hover,
.app_theme_a .wizard > .steps > ul > li.done a:active {
  background: #303f9f;
}
.app_theme_a .md-card-primary {
  border-left-color: #3f51b5;
}
.app_theme_a .waves-effect.md-btn-flat-primary {
  background: none;
}
.app_theme_a .waves-effect.md-btn-flat-primary .waves-ripple {
  background: rgba(63, 81, 181, 0.4);
}
.app_theme_a .search_list_link {
  color: #ff4081;
}
.app_theme_a .chatbox.cb_active .chatbox_header {
  background: #303f9f;
}
.app_theme_a .chatbox_content .chatbox_message.own .chatbox_message_content li > span {
  background: #3f51b5;
}
.app_theme_a .uk-table tr.row_checked td,
.app_theme_a .uk-table-hover tbody tr:hover {
  background: #e8eaf6;
}
