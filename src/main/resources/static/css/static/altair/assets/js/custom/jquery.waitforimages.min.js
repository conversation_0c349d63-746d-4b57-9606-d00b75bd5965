!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){var r,s=(r=new Image).srcset&&r.sizes;e.waitForImages={hasImageProperties:["backgroundImage","listStyleImage","borderImage","borderCornerImage","cursor"],hasImageAttributes:["srcset"]},e.expr[":"]["has-src"]=function(r){return e(r).is('img[src][src!=""]')},e.expr[":"].uncached=function(r){return!!e(r).is(":has-src")&&!r.complete},e.fn.waitForImages=function(){var r,t,i,a,n=0,c=e.Deferred(),o=this,u=[],h=e.waitForImages.hasImageProperties||[],f=e.waitForImages.hasImageAttributes||[],l=/url\(\s*(['"]?)(.*?)\1\s*\)/g;if(e.isPlainObject(arguments[0])?(a=arguments[0].waitForAll,i=arguments[0].each,t=arguments[0].finished):1===arguments.length&&"boolean"===e.type(arguments[0])?a=arguments[0]:(t=arguments[0],i=arguments[1],a=arguments[2]),t=t||e.noop,i=i||e.noop,a=!!a,!e.isFunction(t)||!e.isFunction(i))throw new TypeError("An invalid callback was supplied.");return this.each(function(){var r=e(this);a?r.find("*").addBack().each(function(){var r=e(this);r.is("img:has-src")&&!r.is("[srcset]")&&u.push({src:r.attr("src"),element:r[0]}),e.each(h,function(e,s){var t,i=r.css(s);if(!i)return!0;for(;t=l.exec(i);)u.push({src:t[2],element:r[0]})}),e.each(f,function(e,s){if(!r.attr(s))return!0;u.push({src:r.attr("src"),srcset:r.attr("srcset"),element:r[0]})})}):r.find("img:has-src").each(function(){u.push({src:this.src,element:this})})}),r=u.length,n=0,0===r&&(t.call(o),c.resolveWith(o)),e.each(u,function(a,u){var h=new Image,f="load.waitForImages error.waitForImages";e(h).one(f,function s(a){var h=[n,r,"load"==a.type];if(n++,i.apply(u.element,h),c.notifyWith(u.element,h),e(this).off(f,s),n==r)return t.call(o[0]),c.resolveWith(o[0]),!1}),s&&u.srcset&&(h.srcset=u.srcset,h.sizes=u.sizes),h.src=u.src}),c.promise()}});