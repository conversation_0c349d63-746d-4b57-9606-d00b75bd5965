$(function(){altair_tree.tree_a(),altair_tree.tree_b(),altair_tree.tree_filter()}),altair_tree={tree_a:function(){$("#tA").fancytree({checkbox:!0,selectMode:1,imagePath:"assets/icons/others/",extensions:["dnd","wide"],autoScroll:!0,activate:function(e,t){var a=t.node;a.data.href&&window.open(a.data.href,a.data.target)}})},tree_b:function(){$("#tB").fancytree({checkbox:!0,selectMode:3,imagePath:"assets/icons/others/",extensions:["dnd","wide"],autoScroll:!0,activate:function(e,t){var a=t.node;a.data.href&&window.open(a.data.href,a.data.target)}})},tree_filter:function(){var e=$("#tFilter");e.fancytree({extensions:["filter"],quicksearch:!0,source:{url:"data/fancytree/ajax-tree-local.json"},filter:{autoApply:!0,counter:!0,fuzzy:!1,hideExpandedCounter:!0,highlight:!0,mode:"dimm"},activate:function(e,t){},lazyLoad:function(e,t){t.result={url:"data/fancytree/ajax-sub2.json"}}});var t=e.fancytree("getTree");$("#filter_input").keyup(function(e){var a={autoExpand:$("#autoExpand").is(":checked"),leavesOnly:$("#leavesOnly").is(":checked")},i=$(this).val();e&&e.which===$.ui.keyCode.ESCAPE||""===$.trim(i)?$("#tree_filter_reset").click():($("#tree_filter_regex").is(":checked")?t.filterNodes(function(e){return new RegExp(i,"i").test(e.title)},a):t.filterNodes(i,a),$("#tree_filter_reset").attr("disabled",!1))}),$("#tree_filter_reset").click(function(e){$("#filter_input").val(""),t.clearFilter()}).attr("disabled",!0),$("#filter_switches").find("input:checkbox").on("ifChanged",function(e){var a=$(this).attr("id"),i=$(this).is(":checked");switch(a){case"autoExpand":case"regex":case"leavesOnly":break;case"hideMode":t.options.filter.mode=i?"hide":"dimm";break;case"counter":case"fuzzy":case"hideExpandedCounter":case"highlight":t.options.filter[a]=i}t.clearFilter(),$("#filter_input").keyup()}),$("#counter,#hideExpandedCounter,#highlight").iCheck("check")}};