Handlebars.registerHelper("dateFormat",function(e,r){if(window.moment){var s=r.hash.format||"MMM DD, YYYY hh:mm:ss A";return moment(e).format(s)}return e}),Handlebars.registerHelper("ifCond",function(e,r,s,t){switch(r){case"==":return e==s?t.fn(this):t.inverse(this);case"===":return e===s?t.fn(this):t.inverse(this);case"!==":return e!==s?t.fn(this):t.inverse(this);case"<":return e<s?t.fn(this):t.inverse(this);case"<=":return e<=s?t.fn(this):t.inverse(this);case">":return e>s?t.fn(this):t.inverse(this);case">=":return e>=s?t.fn(this):t.inverse(this);case"&&":return e&&s?t.fn(this):t.inverse(this);case"||":return e||s?t.fn(this):t.inverse(this);default:return t.inverse(this)}}),Handlebars.registerHelper("exists",function(e,r){return void 0!==e?r.fn(this):r.inverse(this)});