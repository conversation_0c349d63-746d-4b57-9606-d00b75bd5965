$(function(){altair_crud_table.init()}),altair_crud_table={init:function(){$("#students_crud").jtable({title:"The Student List",paging:!0,pageSize:10,addRecordButton:$("#recordAdd"),deleteConfirmation:function(t){t.deleteConfirmMessage="Are you sure to delete student "+t.record.Name+"?"},formCreated:function(t,e){e.form.find(".jtable-option-text-clickable").each(function(){var t=$(this).prev().attr("id");$(this).attr("data-click-target",t).off("click").on("click",function(t){t.preventDefault(),$("#"+$(this).attr("data-click-target")).iCheck("toggle")})}),e.form.find("select").each(function(){$(this).after('<div class="selectize_fix"></div>').selectize({dropdownParent:"body",placeholder:"Click here to select ...",onDropdownOpen:function(t){t.hide().velocity("slideDown",{begin:function(){t.css({"margin-top":"0"})},duration:200,easing:easing_swiftOut})},onDropdownClose:function(t){t.show().velocity("slideUp",{complete:function(){t.css({"margin-top":""})},duration:200,easing:easing_swiftOut})}})}),e.form.find('input[type="checkbox"],input[type="radio"]').each(function(){var t=$(this);t.iCheck({checkboxClass:"icheckbox_md",radioClass:"iradio_md",increaseArea:"20%"}).on("ifChecked",function(e){t.parent("div.icheckbox_md").next("span").text("Active")}).on("ifUnchecked",function(e){t.parent("div.icheckbox_md").next("span").text("Passive")})}),e.form.find(".jtable-input").children('input[type="text"],input[type="password"],textarea').not(".md-input").each(function(){$(this).addClass("md-input"),altair_forms.textarea_autosize()}),altair_md.inputs()},actions:{listAction:"data/crud_table/studentsActions.php?action=list",createAction:"data/crud_table/studentsActions.php?action=create",updateAction:"data/crud_table/studentsActions.php?action=update",deleteAction:"data/crud_table/studentsActions.php?action=delete"},fields:{StudentId:{key:!0,create:!1,edit:!1,list:!1},Name:{title:"Name",width:"23%"},EmailAddress:{title:"Email address",list:!1},Password:{title:"User Password",type:"password",list:!1},Gender:{title:"Gender",width:"13%",options:{M:"Male",F:"Female"}},CityId:{title:"City",width:"12%",options:"data/crud_table/cities.php"},BirthDate:{title:"Birth Date",width:"15%",displayFormat:"dd/mm/yy",type:"date",input:function(t){return t.record?'<input class="md-input" type="text" name="BirthDate" value="'+t.value+'" data-uk-datepicker="{format:\'DD/MM/YYYY\'}"/>':'<input class="md-input" type="text" name="BirthDate"  value="" data-uk-datepicker="{format:\'DD/MM/YYYY\'}"/>'}},Education:{title:"Education",type:"radiobutton",options:{1:"Primary school",2:"High school",3:"University"}},About:{title:"About this person",type:"textarea",list:!1},IsActive:{title:"Status",width:"12%",type:"checkbox",values:{false:"Passive",true:"Active"},defaultValue:"true"},RecordDate:{title:"Record date",width:"15%",type:"date",displayFormat:"dd/mm/yy",create:!1,edit:!1}}}).jtable("load"),$(".ui-dialog-buttonset").children("button").attr("class","").addClass("md-btn md-btn-flat").off("mouseenter focus"),$("#AddRecordDialogSaveButton,#EditDialogSaveButton,#DeleteDialogButton").addClass("md-btn-flat-primary")}};