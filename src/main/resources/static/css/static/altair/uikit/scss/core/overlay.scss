// Name:            Overlay
// Description:     Defines styles for image overlays
//
// Component:       `uk-overlay`
//
// Sub-objects:     `uk-overlay-panel`
//                  `uk-overlay-hover`
//                  `uk-overlay-active`
//                  `uk-overlay-icon`
//
// Modifier:        `uk-overlay-background`
//                  `uk-overlay-image`
//                  `uk-overlay-top`
//                  `uk-overlay-bottom`
//                  `uk-overlay-left`
//                  `uk-overlay-right`
//                  `uk-overlay-fade`
//                  `uk-overlay-scale`
//                  `uk-overlay-spin`
//                  `uk-overlay-grayscale`
//                  `uk-overlay-slide-top`
//                  `uk-overlay-slide-bottom`
//                  `uk-overlay-slide-left`
//                  `uk-overlay-slide-right`
//
// DEPRECATED:      `uk-overlay-area`
//                  `uk-overlay-area-content`
//                  `uk-overlay-caption`
//                  `uk-overlay-toggle`
//
// States:          `uk-hover`
//                  `uk-active`
//                  `uk-ignore`
//
// Uses:            Icon: `[class*='uk-icon-']`
//                  `uk-border-circle`
//
// Markup:
//
// <!-- uk-overlay -->
// <figure class="uk-overlay" href="">
//     <img src="" alt="">
//     <figcaption class="uk-overlay-panel"></figcaption>
// </figure>
//
// ========================================================================


// Variables
// ========================================================================

$overlay-panel-padding:                          20px !default;
$overlay-panel-color:                            #fff !default;

$overlay-panel-background:                       rgba(0,0,0,0.5) !default;

$overlay-icon:                                   "\f002" !default;
$overlay-icon-size:                              50px !default;
$overlay-icon-color:                             #fff !default;

$overlay-fade-in-opacity:                        1 !default;
$overlay-fade-out-opacity:                       0.7 !default;

$overlay-scale-in-scale:                         1.1 !default;
$overlay-scale-out-scale:                        1 !default;

$overlay-spin-in-scale:                          1.1 !default;
$overlay-spin-out-scale:                         1 !default;
$overlay-spin-in-rotate:                         3deg !default;
$overlay-spin-out-rotate:                        0deg !default;

$overlay-panel-link-color:                       inherit !default;
$overlay-panel-link-text-decoration:             underline !default;

// DEPRECATED
$overlay-area-background:                        rgba(0,0,0,0.3) !default;

$overlay-area-icon:                              "\f002" !default;
$overlay-area-icon-size:                         50px !default;
$overlay-area-icon-color:                        #fff !default;

$overlay-area-content-font-size:                 1rem !default;
$overlay-area-content-padding-horizontal:        15px !default;
$overlay-area-content-color:                     #fff !default;
$overlay-area-content-link-color:                inherit !default;

$overlay-caption-background:                     rgba(0,0,0,0.5) !default;
$overlay-caption-padding:                        15px !default;
$overlay-caption-color:                          #fff !default;


/* ========================================================================
   Component: Overlay
 ========================================================================== */

/*
 * 1. Container width fits its content
 * 2. Create position context
 * 3. Set max-width for responsive images to prevent `inline-block` consequences
 * 4. Remove the gap between the container and its child element
 * 5. Needed for transitions and to fixed wrong scaling calculation for images in Chrome
 * 6. Fixed `overflow: hidden` to be ignored with border-radius and CSS transforms in Webkit
 * 7. Reset margin
 */

.uk-overlay {
    /* 1 */
    display: inline-block;
    /* 2 */
    position: relative;
    /* 3 */
    max-width: 100%;
    /* 4 */
    vertical-align: middle;
    /* 5 */
    overflow: hidden;
    /* 6 */
    -webkit-transform: translateZ(0);
    /* 7 */
    margin: 0;
}

/* 6 for Safari */
.uk-overlay.uk-border-circle { -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

/*
 * Remove margin from content
 */

.uk-overlay > :first-child { margin-bottom: 0; }


/* Sub-object `uk-overlay-panel`
 ========================================================================== */

/*
 * 1. Position cover
 * 2. Style
 */

.uk-overlay-panel {
    /* 1 */
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    /* 2 */
    padding: $overlay-panel-padding;
    color: $overlay-panel-color;
    @include hook-overlay-panel();
}

/*
 * Remove margin from the last-child
 */

.uk-overlay-panel > :last-child,
.uk-overlay-panel.uk-flex > * > :last-child { margin-bottom: 0; }

/*
 * Keep color for headings if the default heading color is changed
 */

.uk-overlay-panel h1,
.uk-overlay-panel h2,
.uk-overlay-panel h3,
.uk-overlay-panel h4,
.uk-overlay-panel h5,
.uk-overlay-panel h6 { color: inherit; }

.uk-overlay-panel a:not([class]) {
    color: $overlay-panel-link-color;
    text-decoration: $overlay-panel-link-text-decoration;
}

.uk-overlay-panel a[class*='uk-icon-']:not(.uk-icon-button) { color: $overlay-panel-link-color; }


/* Sub-object `uk-overlay-hover` and `uk-overlay-active`
 ========================================================================== */

.uk-overlay-hover:not(:hover):not(.uk-hover) .uk-overlay-panel:not(.uk-ignore) { opacity: 0; }

.uk-overlay-active :not(.uk-active) > .uk-overlay-panel:not(.uk-ignore) { opacity: 0; }


/* Modifier `uk-overlay-background`
 ========================================================================== */

.uk-overlay-background {
    background: $overlay-panel-background;
    @include hook-overlay-background();
}


/* Modifier `uk-overlay-image`
 ========================================================================== */

/*
 * Reset panel
 */

.uk-overlay-image {
    padding: 0;
    @include hook-overlay-image();
}


/* Position modifiers
 ========================================================================== */

.uk-overlay-top { bottom: auto; }

.uk-overlay-bottom { top: auto; }

.uk-overlay-left { right: auto; }

.uk-overlay-right { left: auto; }


/* Sub-object `uk-overlay-icon`
 ========================================================================== */

.uk-overlay-icon:before {
    content: $overlay-icon;
    position: absolute;
    top: 50%;
    left: 50%;
    width: $overlay-icon-size;
    height: $overlay-icon-size;
    margin-top: -($overlay-icon-size / 2);
    margin-left: -($overlay-icon-size / 2);
    font-size: $overlay-icon-size;
    line-height: 1;
    font-family: FontAwesome;
    text-align: center;
    color: $overlay-icon-color;
    @include hook-overlay-icon();
}


/* Transitions
 ========================================================================== */

.uk-overlay-fade,
.uk-overlay-scale,
.uk-overlay-spin,
.uk-overlay-grayscale,
.uk-overlay-blur,
[class*='uk-overlay-slide'] {
    transition-duration: 0.3s;
    transition-timing-function: ease-out;
    transition-property: opacity, transform, filter;
}

.uk-overlay-active .uk-overlay-fade,
.uk-overlay-active .uk-overlay-scale,
.uk-overlay-active .uk-overlay-spin,
.uk-overlay-active [class*='uk-overlay-slide'] { transition-duration: 0.8s; }

/*
 * Fade
 */

.uk-overlay-fade { opacity: $overlay-fade-out-opacity; }

.uk-overlay-hover:hover .uk-overlay-fade,
.uk-overlay-hover.uk-hover .uk-overlay-fade,
.uk-overlay-active .uk-active > .uk-overlay-fade { opacity: $overlay-fade-in-opacity; }

/*
 * Scale
 */

.uk-overlay-scale {
    -webkit-transform: scale($overlay-scale-out-scale);
    transform: scale($overlay-scale-out-scale);
}

.uk-overlay-hover:hover .uk-overlay-scale,
.uk-overlay-hover.uk-hover .uk-overlay-scale,
.uk-overlay-active .uk-active > .uk-overlay-scale {
    -webkit-transform: scale($overlay-scale-in-scale);
    transform: scale($overlay-scale-in-scale);
}

/*
 * Spin
 */

.uk-overlay-spin {
    -webkit-transform: scale($overlay-spin-out-scale) rotate($overlay-spin-out-rotate);
    transform: scale($overlay-spin-out-scale) rotate($overlay-spin-out-rotate);
}

.uk-overlay-hover:hover .uk-overlay-spin,
.uk-overlay-hover.uk-hover .uk-overlay-spin,
.uk-overlay-active .uk-active > .uk-overlay-spin {
    -webkit-transform: scale($overlay-spin-in-scale) rotate($overlay-spin-in-rotate);
    transform: scale($overlay-spin-in-scale) rotate($overlay-spin-in-rotate);
}

/*
 * Grayscale
 */

.uk-overlay-grayscale {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
}

.uk-overlay-hover:hover .uk-overlay-grayscale,
.uk-overlay-hover.uk-hover .uk-overlay-grayscale,
.uk-overlay-active .uk-active > .uk-overlay-grayscale {
    -webkit-filter: grayscale(0%);
    filter: grayscale(0%);
}

/*
 * Slide
 */

[class*='uk-overlay-slide'] { opacity: 0; }

/* Top */
.uk-overlay-slide-top {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
}

/* Bottom */
.uk-overlay-slide-bottom {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
}

/* Left */
.uk-overlay-slide-left {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
}

/* Right */
.uk-overlay-slide-right {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
}

/* Hover */
.uk-overlay-hover:hover [class*='uk-overlay-slide'],
.uk-overlay-hover.uk-hover [class*='uk-overlay-slide'],
.uk-overlay-active .uk-active > [class*='uk-overlay-slide'] {
    opacity: 1;
    -webkit-transform: translateX(0) translateY(0);
    transform: translateX(0) translateY(0);
}


/* DEPRECATED
 * Sub-object `uk-overlay-area`
 ========================================================================== */

/*
 * 1. Set position
 * 2. Set style
 * 3. Fade-in transition
 */

.uk-overlay-area {
    /* 1 */
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    /* 2 */
    background: $overlay-area-background;
    /* 3 */
    opacity: 0;
    -webkit-transition: opacity 0.15s linear;
    transition: opacity 0.15s linear;
    -webkit-transform: translate3d(0,0,0);
    @include hook-overlay-area();
}

/*
 * Hover
 * 1. `uk-hover` to support touch devices
 * 2. Use optional `uk-overlay-toggle` to trigger the overlay earlier
 */

.uk-overlay:hover .uk-overlay-area,
.uk-overlay.uk-hover .uk-overlay-area, // 1
.uk-overlay-toggle:hover .uk-overlay-area, // 2
.uk-overlay-toggle.uk-hover .uk-overlay-area { opacity: 1; }

/*
 * Icon
 */

.uk-overlay-area:empty:before {
    content: $overlay-area-icon;
    position: absolute;
    top: 50%;
    left: 50%;
    width: $overlay-area-icon-size;
    height: $overlay-area-icon-size;
    margin-top: -($overlay-area-icon-size / 2);
    margin-left: -($overlay-area-icon-size / 2);
    font-size: $overlay-area-icon-size;
    line-height: 1;
    font-family: FontAwesome;
    text-align: center;
    color: $overlay-area-icon-color;
    @include hook-overlay-area-icon();
}


/* DEPRECATED
 * Sub-object `uk-overlay-area-content`
 ========================================================================== */

/*
 * Remove whitespace between child elements when using `inline-block`
 * Needed for Firefox
 */

.uk-overlay-area:not(:empty) { font-size: 0.001px; }

/*
 * 1. Needed for vertical alignment
 */

.uk-overlay-area:not(:empty):before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

/*
 * 1. Set vertical alignment
 * 2. Reset whitespace hack
 * 3. Set horizontal alignment
 * 4. Set style
 */

.uk-overlay-area-content {
    /* 1 */
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    vertical-align: middle;
    /* 2 */
    font-size: $overlay-area-content-font-size;
    /* 3 */
    text-align: center;
    /* 4 */
    padding: 0 $overlay-area-content-padding-horizontal;
    color: $overlay-area-content-color;
    @include hook-overlay-area-content();
}

/*
 * Remove margin from the last-child
 */

.uk-overlay-area-content > :last-child { margin-bottom: 0; }

/*
 * Links in overlay area
 */

.uk-overlay-area-content a:not([class]),
.uk-overlay-area-content a:not([class]):hover { color: $overlay-area-content-link-color; }


/* DEPRECATED
 * Sub-object `uk-overlay-caption`
 ========================================================================== */

/*
 * 1. Set position
 * 2. Set style
 * 3. Fade-in transition
 */

.uk-overlay-caption {
    /* 1 */
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    /* 2 */
    padding: $overlay-caption-padding;
    background: $overlay-caption-background;
    color: $overlay-caption-color;
    /* 3 */
    opacity: 0;
    -webkit-transition: opacity 0.15s linear;
    transition: opacity 0.15s linear;
    -webkit-transform: translate3d(0,0,0);
    @include hook-overlay-caption();
}

/*
 * Hover
 * 1. `uk-hover` to support touch devices
 * 2. Use optional `uk-overlay-toggle` to trigger the overlay earlier
 */

.uk-overlay:hover .uk-overlay-caption,
.uk-overlay.uk-hover .uk-overlay-caption, // 1
.uk-overlay-toggle:hover .uk-overlay-caption, // 2
.uk-overlay-toggle.uk-hover .uk-overlay-caption { opacity: 1; }


// Hooks
// ========================================================================

@include hook-overlay-misc();

// @mixin hook-overlay-panel(){}
// @mixin hook-overlay-background(){}
// @mixin hook-overlay-image(){}
// @mixin hook-overlay-icon(){}
// @mixin hook-overlay-misc(){}

// DEPRECATED
// @mixin hook-overlay-area(){}
// @mixin hook-overlay-area-icon(){}
// @mixin hook-overlay-area-content(){}
// @mixin hook-overlay-caption(){}
