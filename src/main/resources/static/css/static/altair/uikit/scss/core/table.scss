// Name:            Table
// Description:     Defines styles for tables
//
// Component:       `uk-table`
//
// Modifiers:       `uk-table-middle`
//                  `uk-table-striped`
//                  `uk-table-condensed`
//                  `uk-table-hover`
//
// States:          `uk-active`
//
// ========================================================================


// Variables
// ========================================================================

$table-margin-vertical:                          15px !default;
$table-padding-vertical:                         8px !default;
$table-padding-horizontal:                       8px !default;

$table-caption-color:                            #999 !default;
$table-caption-font-size:                        12px !default;

$table-striped-background:                       #f5f5f5 !default;

$table-condensed-padding-vertical:               4px !default;
$table-condensed-padding-horizontal:             8px !default;

$table-hover-background:                         #EEE !default;

$table-active-background:                        $table-hover-background !default;


/* ========================================================================
   Component: Table
 ========================================================================== */

/*
 * 1. Remove most spacing between table cells.
 * 2. Block element behavior
 * 3. Style
 */

.uk-table {
    /* 1 */
    border-collapse: collapse;
    border-spacing: 0;
    /* 2 */
    width: 100%;
    /* 3 */
    margin-bottom: $table-margin-vertical;
}

/*
 * Add margin if adjacent element
 */

* + .uk-table { margin-top: $table-margin-vertical; }

.uk-table th,
.uk-table td {
    padding: $table-padding-vertical $table-padding-horizontal;
    @include hook-table();
}

/*
 * Set alignment
 */

.uk-table th { text-align: left; }
.uk-table td { vertical-align: top; }

.uk-table thead th { vertical-align: bottom; }

/*
 * Caption and footer
 */

.uk-table caption,
.uk-table tfoot {
    font-size: $table-caption-font-size;
    font-style: italic;
}

.uk-table caption {
    text-align: left;
    color: $table-caption-color;
}

/*
 * Active State
 */

.uk-table tbody tr.uk-active {
    background: $table-active-background;
    @include hook-table-active();
}


/* Sub-modifier: `uk-table-middle`
 ========================================================================== */

.uk-table-middle,
.uk-table-middle td { vertical-align: middle !important; }


/* Modifier: `uk-table-striped`
 ========================================================================== */

.uk-table-striped tbody tr:nth-of-type(odd) {
    background: $table-striped-background;
    @include hook-table-striped();
}


/* Modifier: `uk-table-condensed`
 ========================================================================== */

.uk-table-condensed td {
    padding: $table-condensed-padding-vertical $table-condensed-padding-horizontal;
    @include hook-table-condensed();
}


/* Modifier: `uk-table-hover`
 ========================================================================== */

.uk-table-hover tbody tr:hover {
    background: $table-hover-background;
    @include hook-table-hover();
}



// Hooks
// ========================================================================

@include hook-table-misc();

// @mixin hook-table(){}
// @mixin hook-table-active(){}
// @mixin hook-table-striped(){}
// @mixin hook-table-condensed(){}
// @mixin hook-table-hover(){}
// @mixin hook-table-misc(){}