// Name:            Modal
// Description:     Defines styles for modal dialogs
//
// Component:       `uk-modal`
//
// Sub-objects:     `uk-modal-dialog`
//                  `uk-modal-close`
//                  `uk-modal-header`
//                  `uk-modal-footer`
//                  `uk-modal-caption`
//                  `uk-modal-spinner`
//
// Modifiers:       `uk-modal-dialog-lightbox`
//                  `uk-modal-dialog-blank`
//                  `uk-modal-dialog-large`
//
// States:          `uk-open`
//
// Uses:            Close: `uk-close`
//
// Markup:
//
// <!-- uk-modal -->
// <a href="#modal" data-uk-modal=""></a>
// <div id="modal" class="uk-modal">
//     <div class="uk-modal-dialog">
//         <a href="" class="uk-modal-close uk-close"></a>
//         <p></p>
//     </div>
// </div>
//
// ========================================================================


// Variables
// ========================================================================

@modal-z-index:                                 1010;
@modal-background:                              rgba(0,0,0,0.6);

@modal-dialog-margin-vertical:                  50px;
@modal-dialog-padding:                          20px;
@modal-dialog-width:                            600px;
@modal-dialog-background:                       #fff;

@modal-dialog-large-width:                      930px;
@modal-dialog-large-width-large:                1130px;

@modal-header-margin-bottom:                    15px;

@modal-footer-margin-top:                       15px;

@modal-caption-margin-bottom:                   -10px;
@modal-caption-color:                           #fff;


/* ========================================================================
   Component: Modal
 ========================================================================== */

/*
 * This is the modal overlay and modal dialog container
 * 1. Hide by default
 * 2. Set fixed position
 * 3. Allow scrolling for the modal dialog
 * 4. Mask the background page
 * 5. Fade-in transition
 * 6. Deactivate browser history navigation in IE11
 * 7. force hardware acceleration to prevent browser rendering hiccups
 */

.uk-modal {
    /* 1 */
    display: none;
    /* 2 */
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: @modal-z-index;
    /* 3 */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* 4 */
    background: @modal-background;
    /* 5 */
    opacity: 0;
    -webkit-transition: opacity 0.15s linear;
    transition: opacity 0.15s linear;
    /* 6 */
    touch-action: cross-slide-y pinch-zoom double-tap-zoom;
    /* 7 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    .hook-modal;
}

/*
 * Open state
 */

.uk-modal.uk-open { opacity: 1; }

/*
 * Prevents duplicated scrollbar caused by 4.
 */

.uk-modal-page,
.uk-modal-page body { overflow: hidden; }


/* Sub-object: `uk-modal-dialog`
 ========================================================================== */

/*
 * 1. Create position context for caption, spinner and close button
 * 2. Set box sizing
 * 3. Set style
 * 4. Slide-in transition
 */

.uk-modal-dialog {
    /* 1 */
    position: relative;
    /* 2 */
    box-sizing: border-box;
    margin: @modal-dialog-margin-vertical auto;
    padding: @modal-dialog-padding;
    width: @modal-dialog-width;
    max-width: 100%;
    max-width: ~"calc(100% - 20px)";
    /* 3 */
    background: @modal-dialog-background;
    /* 4 */
    opacity: 0;
    -webkit-transform: translateY(-100px);
    transform: translateY(-100px);
    -webkit-transition: opacity 0.3s linear, -webkit-transform 0.3s ease-out;
    transition: opacity 0.3s linear, transform 0.3s ease-out;
    .hook-modal-dialog;
}

/* Phone landscape and smaller */
@media (max-width: @breakpoint-small-max) {

    /*
     * Fit in small screen
     */

    .uk-modal-dialog {
        width: auto;
        margin: 10px auto;
    }

}

/*
 * Open state
 */

.uk-open .uk-modal-dialog {
    /* 4 */
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
}

/*
 * Remove margin from the last-child
 */

.uk-modal-dialog > :not([class*='uk-modal-']):last-child { margin-bottom: 0; }


/* Close in modal
 ========================================================================== */

.uk-modal-dialog > .uk-close:first-child {
    margin: -10px -10px 0 0;
    float: right;
}

/*
 * Remove margin from adjacent element
 */

 .uk-modal-dialog > .uk-close:first-child + :not([class*='uk-modal-']) { margin-top: 0; }


/* Modifier: `uk-modal-dialog-lightbox`
 ========================================================================== */

.uk-modal-dialog-lightbox {
    margin: 15px auto;
    padding: 0;
    max-width: 95%;
    max-width: ~"calc(100% - 30px)";
    min-height: 50px;
    .hook-modal-dialog-lightbox;
}

/*
 * Close button
 */

.uk-modal-dialog-lightbox > .uk-close:first-child {
    position: absolute;
    top: -12px;
    right: -12px;
    margin: 0;
    float: none;
}

/* Phone landscape and smaller */
@media (max-width: @breakpoint-small-max) {

    .uk-modal-dialog-lightbox > .uk-close:first-child {
        top: -7px;
        right: -7px;
    }

}

/* Modifier: `uk-modal-dialog-blank`
 ========================================================================== */

.uk-modal-dialog-blank {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    -webkit-transition: opacity 0.3s linear;
    transition: opacity 0.3s linear;
}

/*
* Close button
*/

.uk-modal-dialog-blank > .uk-close:first-child {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
    margin: 0;
    float: none;
}


/* Modifier: `uk-modal-dialog-large`
 ========================================================================== */

/* Tablet and bigger */
@media (min-width: @breakpoint-medium) {

    .uk-modal-dialog-large { width: @modal-dialog-large-width; }

}

/* Large screen and bigger */
@media (min-width: @breakpoint-xlarge) {

    .uk-modal-dialog-large { width: @modal-dialog-large-width-large; }

}


/* Sub-Object: `uk-modal-header` and `uk-modal-footer`
 ========================================================================== */

.uk-modal-header {
    margin-bottom: @modal-header-margin-bottom;
    .hook-modal-header;
}

.uk-modal-footer {
    margin-top: @modal-footer-margin-top;
    .hook-modal-footer;
}

/*
 * Remove margin from the last-child
 */

.uk-modal-header  > :last-child,
.uk-modal-footer  > :last-child { margin-bottom: 0; }


/* Sub-Object: `uk-modal-caption`
 ========================================================================== */

.uk-modal-caption {
    position: absolute;
    left: 0;
    right: 0;
    bottom: (-@modal-dialog-padding);
    margin-bottom: @modal-caption-margin-bottom;
    color: @modal-caption-color;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .hook-modal-caption;
}


/* Sub-Object: `uk-modal-spinner`
 ========================================================================== */

.uk-modal-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    font-size: 25px;
    color: #ddd;
    .hook-modal-spinner;
}

.uk-modal-spinner:after {
    content: "\f110";
    display: block;
    font-family: FontAwesome;
    -webkit-animation: uk-rotate 2s infinite linear;
    animation: uk-rotate 2s infinite linear;
}


// Hooks
// ========================================================================

.hook-modal-misc;

.hook-modal() {}
.hook-modal-dialog() {}
.hook-modal-dialog-lightbox() {}
.hook-modal-header() {}
.hook-modal-footer() {}
.hook-modal-caption() {}
.hook-modal-spinner() {}
.hook-modal-misc() {}
