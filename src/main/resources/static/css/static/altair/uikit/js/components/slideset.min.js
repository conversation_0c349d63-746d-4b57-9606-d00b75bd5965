/*! UIkit 2.27.5 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){var i;window.UIkit2&&(i=t(UIkit2)),"function"==typeof define&&define.amd&&define("uikit-slideset",["uikit"],function(){return i||t(UIkit2)})}(function(t){"use strict";function i(i,e,n,s){var a,o,r,l,h=t.$.Deferred(),u=this.options.delay===!1?Math.floor(this.options.duration/2):this.options.delay,d=this;if(s=s||1,this.element.css("min-height",this.element.height()),n[0]===e[0])return h.resolve(),h.promise();if("object"==typeof i?(a=i[0],o=i[1]||i[0]):(a=i,o=a),t.$body.css("overflow-x","hidden"),r=function(){if(e&&e.length&&e.hide().removeClass(o+" uk-animation-reverse").css({opacity:"","animation-delay":"",animation:""}),!n.length)return h.resolve(),void 0;for(l=0;l<n.length;l++)n.eq(1==s?l:n.length-l-1).css("animation-delay",l*u+"ms");var i=function(){n.removeClass(""+a).css({opacity:"",display:"","animation-delay":"",animation:""}),h.resolve(),t.$body.css("overflow-x",""),d.element.css("min-height",""),i=!1};n.addClass(a)[1==s?"last":"first"]().one(t.support.animation.end,function(){i&&i()}).end().css("display",""),setTimeout(function(){i&&i()},n.length*u*2)},n.length&&n.css("animation-duration",this.options.duration+"ms"),e&&e.length)for(e.css("animation-duration",this.options.duration+"ms")[1==s?"last":"first"]().one(t.support.animation.end,function(){r()}),l=0;l<e.length;l++)!function(i,e){setTimeout(function(){e.css("display","none").css("display","").css("opacity",0).on(t.support.animation.end,function(){e.removeClass(o)}).addClass(o+" uk-animation-reverse")}.bind(this),l*u)}(l,e.eq(1==s?l:e.length-l-1));else r();return h.promise()}function e(t,i){var e,n=0,s=-1,a=t.length||0,o=[];if(1>i)return null;for(;a>n;)e=n%i,e?o[s][e]=t[n]:o[++s]=[t[n]],n++;for(n=0,a=o.length;a>n;)o[n]=jQuery(o[n]),n++;return o}var n;t.component("slideset",{defaults:{"default":1,animation:"fade",duration:200,filter:"",delay:!1,controls:!1,autoplay:!1,autoplayInterval:7e3,pauseOnHover:!0},sets:[],boot:function(){t.ready(function(i){t.$("[data-uk-slideset]",i).each(function(){var i=t.$(this);i.data("slideset")||t.slideset(i,t.Utils.options(i.attr("data-uk-slideset")))})})},init:function(){var i=this;this.activeSet=!1,this.list=this.element.find(".uk-slideset"),this.nav=this.element.find(".uk-slideset-nav"),this.controls=this.options.controls?t.$(this.options.controls):this.element,t.$win.on("resize load",t.Utils.debounce(function(){i.update()},100)),i.list.addClass("uk-grid-width-1-"+i.options.default),["xlarge","large","medium","small"].forEach(function(t){i.options[t]&&i.list.addClass("uk-grid-width-"+t+"-1-"+i.options[t])}),this.on("click.uk.slideset","[data-uk-slideset-item]",function(e){if(e.preventDefault(),!i.animating){var n=t.$(this).attr("data-uk-slideset-item");if(i.activeSet!==n)switch(n){case"next":case"previous":i["next"==n?"next":"previous"]();break;default:i.show(parseInt(n,10))}}}),this.controls.on("click.uk.slideset","[data-uk-filter]",function(e){var n=t.$(this);n.parent().hasClass("uk-slideset")||(e.preventDefault(),i.animating||i.currentFilter==n.attr("data-uk-filter")||(i.updateFilter(n.attr("data-uk-filter")),i._hide().then(function(){i.update(!0,!0)})))}),this.on("swipeRight swipeLeft",function(t){i["swipeLeft"==t.type?"next":"previous"]()}),this.updateFilter(this.options.filter),this.update(),this.element.on({mouseenter:function(){i.options.pauseOnHover&&(i.hovering=!0)},mouseleave:function(){i.hovering=!1}}),this.options.autoplay&&this.start(),t.domObserve(this.list,function(){i.list.children(":visible:not(.uk-active)").length&&i.update(!1,!0)})},update:function(t,i){var n,s=this.visible;if(this.visible=this.getVisibleOnCurrenBreakpoint(),s!=this.visible||i){for(this.children=this.list.children().hide(),this.items=this.getItems(),this.sets=e(this.items,this.visible),n=0;n<this.sets.length;n++)this.sets[n].css({display:"none"});if(this.nav.length&&this.nav.empty()){for(n=0;n<this.sets.length;n++)this.nav.append('<li data-uk-slideset-item="'+n+'"><a></a></li>');this.nav[1==this.nav.children().length?"addClass":"removeClass"]("uk-invisible")}this.activeSet=!1,this.show(0,!t)}},updateFilter:function(i){var e,n=this;this.currentFilter=i,this.controls.find("[data-uk-filter]").each(function(){e=t.$(this),e.parent().hasClass("uk-slideset")||(e.attr("data-uk-filter")==n.currentFilter?e.addClass("uk-active"):e.removeClass("uk-active"))})},getVisibleOnCurrenBreakpoint:function(){var i=null,e=t.$('<div style="position:absolute;height:1px;top:-1000px;width:100px"><div></div></div>').appendTo("body"),n=e.children().eq(0),s=this.options;return["xlarge","large","medium","small"].forEach(function(t){s[t]&&!i&&(e.attr("class","uk-grid-width-"+t+"-1-2").width(),50==n.width()&&(i=t))}),e.remove(),this.options[i]||this.options["default"]},getItems:function(){var i,e=[];return this.currentFilter?(i=this.currentFilter||[],"string"==typeof i&&(i=i.split(/,/).map(function(t){return t.trim()})),this.children.each(function(){var n=t.$(this),s=n.attr("data-uk-filter"),a=i.length?!1:!0;s&&(s=s.split(/,/).map(function(t){return t.trim()}),i.forEach(function(t){s.indexOf(t)>-1&&(a=!0)})),a&&e.push(n[0])}),e=t.$(e)):e=this.list.children(),e},show:function(i,e,s){var a=this;if(this.activeSet!==i&&!this.animating){s=s||(i<this.activeSet?-1:1);var o=this.sets[this.activeSet]||[],r=this.sets[i],l=this._getAnimation();(e||!t.support.animation)&&(l=n.none),this.animating=!0,this.nav.length&&this.nav.children().removeClass("uk-active").eq(i).addClass("uk-active"),l.apply(a,[o,r,s]).then(function(){t.Utils.checkDisplay(r,!0),a.children.hide().removeClass("uk-active"),r.addClass("uk-active").css({display:"",opacity:""}),a.animating=!1,a.activeSet=i,t.Utils.checkDisplay(r,!0),a.trigger("show.uk.slideset",[r])})}},_getAnimation:function(){var i=n[this.options.animation]||n.none;return t.support.animation||(i=n.none),i},_hide:function(){var t=this,i=this.sets[this.activeSet]||[],e=this._getAnimation();return this.animating=!0,e.apply(t,[i,[],1]).then(function(){t.animating=!1})},next:function(){this.show(this.sets[this.activeSet+1]?this.activeSet+1:0,!1,1)},previous:function(){this.show(this.sets[this.activeSet-1]?this.activeSet-1:this.sets.length-1,!1,-1)},start:function(){this.stop();var t=this;this.interval=setInterval(function(){t.hovering||t.animating||t.next()},this.options.autoplayInterval)},stop:function(){this.interval&&clearInterval(this.interval)}}),n={none:function(){var i=t.$.Deferred();return i.resolve(),i.promise()},fade:function(t,e){return i.apply(this,["uk-animation-fade",t,e])},"slide-bottom":function(t,e){return i.apply(this,["uk-animation-slide-bottom",t,e])},"slide-top":function(t,e){return i.apply(this,["uk-animation-slide-top",t,e])},"slide-vertical":function(t,e,n){var s=["uk-animation-slide-top","uk-animation-slide-bottom"];return-1==n&&s.reverse(),i.apply(this,[s,t,e])},"slide-horizontal":function(t,e,n){var s=["uk-animation-slide-right","uk-animation-slide-left"];return-1==n&&s.reverse(),i.apply(this,[s,t,e,n])},scale:function(t,e){return i.apply(this,["uk-animation-scale-up",t,e])}},t.slideset.animations=n});