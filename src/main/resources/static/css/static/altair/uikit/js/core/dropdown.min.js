/*! UIkit 2.27.5 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";function o(o,e,i,n){if(o=t.$(o),e=t.$(e),i=i||window.innerWidth,n=n||o.offset(),e.length){var r=e.outerWidth();if(o.css("min-width",r),"right"==t.langdirection){var s=i-(e.offset().left+r),d=i-(o.offset().left+o.outerWidth());o.css("margin-right",s-d)}else o.css("margin-left",e.offset().left-n.left)}}var e,i=!1,n={x:{"bottom-left":"bottom-right","bottom-right":"bottom-left","bottom-center":"bottom-center","top-left":"top-right","top-right":"top-left","top-center":"top-center","left-top":"right-top","left-bottom":"right-bottom","left-center":"right-center","right-top":"left-top","right-bottom":"left-bottom","right-center":"left-center"},y:{"bottom-left":"top-left","bottom-right":"top-right","bottom-center":"top-center","top-left":"bottom-left","top-right":"bottom-right","top-center":"bottom-center","left-top":"left-bottom","left-bottom":"left-top","left-center":"left-center","right-top":"right-bottom","right-bottom":"right-top","right-center":"right-center"},xy:{"bottom-left":"top-right","bottom-right":"top-left","bottom-center":"top-center","top-left":"bottom-right","top-right":"bottom-left","top-center":"bottom-center","left-top":"right-bottom","left-bottom":"right-top","left-center":"right-center","right-top":"left-bottom","right-bottom":"left-top","right-center":"left-center"}};t.component("dropdown",{defaults:{mode:"hover",pos:"bottom-left",offset:0,remaintime:800,justify:!1,boundary:t.$win,delay:0,dropdownSelector:".uk-dropdown,.uk-dropdown-blank",hoverDelayIdle:250,preventflip:!1},remainIdle:!1,boot:function(){var o=t.support.touch?"click":"mouseenter";t.$html.on(o+".dropdown.uikit focus pointerdown","[data-uk-dropdown]",function(e){var i=t.$(this);if(!i.data("dropdown")){var n=t.dropdown(i,t.Utils.options(i.attr("data-uk-dropdown")));("click"==e.type||"mouseenter"==e.type&&"hover"==n.options.mode)&&n.element.trigger(o),n.dropdown.length&&e.preventDefault()}})},init:function(){var o=this;this.dropdown=this.find(this.options.dropdownSelector),this.offsetParent=this.dropdown.parents().filter(function(){return-1!==t.$.inArray(t.$(this).css("position"),["relative","fixed","absolute"])}).slice(0,1),this.offsetParent.length||(this.offsetParent=this.element),this.centered=this.dropdown.hasClass("uk-dropdown-center"),this.justified=this.options.justify?t.$(this.options.justify):!1,this.boundary=t.$(this.options.boundary),this.boundary.length||(this.boundary=t.$win),this.dropdown.hasClass("uk-dropdown-up")&&(this.options.pos="top-left"),this.dropdown.hasClass("uk-dropdown-flip")&&(this.options.pos=this.options.pos.replace("left","right")),this.dropdown.hasClass("uk-dropdown-center")&&(this.options.pos=this.options.pos.replace(/(left|right)/,"center")),this.element.attr("aria-haspopup","true"),this.element.attr("aria-expanded",this.element.hasClass("uk-open")),this.dropdown.attr("aria-hidden","true"),"click"==this.options.mode||t.support.touch?this.on("click.uk.dropdown",function(e){var i=t.$(e.target);i.parents(o.options.dropdownSelector).length||((i.is("a[href='#']")||i.parent().is("a[href='#']")||o.dropdown.length&&!o.dropdown.is(":visible"))&&e.preventDefault(),i.blur()),o.element.hasClass("uk-open")?(!o.dropdown.find(e.target).length||i.is(".uk-dropdown-close")||i.parents(".uk-dropdown-close").length)&&o.hide():o.show()}):this.on("mouseenter",function(){o.trigger("pointerenter.uk.dropdown",[o]),o.remainIdle&&clearTimeout(o.remainIdle),e&&clearTimeout(e),i&&i==o||(e=i&&i!=o?setTimeout(function(){e=setTimeout(o.show.bind(o),o.options.delay)},o.options.hoverDelayIdle):setTimeout(o.show.bind(o),o.options.delay))}).on("mouseleave",function(){e&&clearTimeout(e),o.remainIdle=setTimeout(function(){i&&i==o&&o.hide()},o.options.remaintime),o.trigger("pointerleave.uk.dropdown",[o])}).on("click",function(e){var n=t.$(e.target);return o.remainIdle&&clearTimeout(o.remainIdle),i&&i==o?((!o.dropdown.find(e.target).length||n.is(".uk-dropdown-close")||n.parents(".uk-dropdown-close").length)&&o.hide(),void 0):((n.is("a[href='#']")||n.parent().is("a[href='#']"))&&e.preventDefault(),o.show(),void 0)})},show:function(){t.$html.off("click.outer.dropdown"),i&&i!=this&&i.hide(!0),e&&clearTimeout(e),this.trigger("beforeshow.uk.dropdown",[this]),this.checkDimensions(),this.element.addClass("uk-open"),this.element.attr("aria-expanded","true"),this.dropdown.attr("aria-hidden","false"),this.trigger("show.uk.dropdown",[this]),t.Utils.checkDisplay(this.dropdown,!0),t.Utils.focus(this.dropdown),i=this,this.registerOuterClick()},hide:function(t){this.trigger("beforehide.uk.dropdown",[this,t]),this.element.removeClass("uk-open"),this.remainIdle&&clearTimeout(this.remainIdle),this.remainIdle=!1,this.element.attr("aria-expanded","false"),this.dropdown.attr("aria-hidden","true"),this.trigger("hide.uk.dropdown",[this,t]),i==this&&(i=!1)},registerOuterClick:function(){var o=this;t.$html.off("click.outer.dropdown"),setTimeout(function(){t.$html.on("click.outer.dropdown",function(n){e&&clearTimeout(e);t.$(n.target);i!=o||o.element.find(n.target).length||(o.hide(!0),t.$html.off("click.outer.dropdown"))})},10)},checkDimensions:function(){if(this.dropdown.length){this.dropdown.removeClass("uk-dropdown-top uk-dropdown-bottom uk-dropdown-left uk-dropdown-right uk-dropdown-stack uk-dropdown-autoflip").css({topLeft:"",left:"",marginLeft:"",marginRight:""}),this.justified&&this.justified.length&&this.dropdown.css("min-width","");var e,i=t.$.extend({},this.offsetParent.offset(),{width:this.offsetParent[0].offsetWidth,height:this.offsetParent[0].offsetHeight}),r=this.options.offset,s=this.dropdown,d=(s.show().offset()||{left:0,top:0},s.outerWidth()),h=s.outerHeight(),p=this.boundary.width(),l=(this.boundary[0]!==window&&this.boundary.offset()?this.boundary.offset():{top:0,left:0},this.options.pos),a={"bottom-left":{top:0+i.height+r,left:0},"bottom-right":{top:0+i.height+r,left:0+i.width-d},"bottom-center":{top:0+i.height+r,left:0+i.width/2-d/2},"top-left":{top:0-h-r,left:0},"top-right":{top:0-h-r,left:0+i.width-d},"top-center":{top:0-h-r,left:0+i.width/2-d/2},"left-top":{top:0,left:0-d-r},"left-bottom":{top:0+i.height-h,left:0-d-r},"left-center":{top:0+i.height/2-h/2,left:0-d-r},"right-top":{top:0,left:0+i.width+r},"right-bottom":{top:0+i.height-h,left:0+i.width+r},"right-center":{top:0+i.height/2-h/2,left:0+i.width+r}},f={};if(e=l.split("-"),f=a[l]?a[l]:a["bottom-left"],this.justified&&this.justified.length)o(s.css({left:0}),this.justified,p);else if(this.options.preventflip!==!0){var u;switch(this.checkBoundary(i.left+f.left,i.top+f.top,d,h,p)){case"x":"x"!==this.options.preventflip&&(u=n.x[l]||"right-top");break;case"y":"y"!==this.options.preventflip&&(u=n.y[l]||"top-left");break;case"xy":this.options.preventflip||(u=n.xy[l]||"right-bottom")}u&&(e=u.split("-"),f=a[u]?a[u]:a["bottom-left"],s.addClass("uk-dropdown-autoflip"),this.checkBoundary(i.left+f.left,i.top+f.top,d,h,p)&&(e=l.split("-"),f=a[l]?a[l]:a["bottom-left"]))}d>p&&(s.addClass("uk-dropdown-stack"),this.trigger("stack.uk.dropdown",[this])),s.css(f).css("display","").addClass("uk-dropdown-"+e[0])}},checkBoundary:function(o,e,i,n,r){var s="";return(0>o||o-t.$win.scrollLeft()+i>r)&&(s+="x"),(e-t.$win.scrollTop()<0||e-t.$win.scrollTop()+n>window.innerHeight)&&(s+="y"),s}}),t.component("dropdownOverlay",{defaults:{justify:!1,cls:"",duration:200},boot:function(){t.ready(function(o){t.$("[data-uk-dropdown-overlay]",o).each(function(){var o=t.$(this);o.data("dropdownOverlay")||t.dropdownOverlay(o,t.Utils.options(o.attr("data-uk-dropdown-overlay")))})})},init:function(){var e=this;this.justified=this.options.justify?t.$(this.options.justify):!1,this.overlay=this.element.find("uk-dropdown-overlay"),this.overlay.length||(this.overlay=t.$('<div class="uk-dropdown-overlay"></div>').appendTo(this.element)),this.overlay.addClass(this.options.cls),this.on({"beforeshow.uk.dropdown":function(t,i){e.dropdown=i,e.justified&&e.justified.length&&o(e.overlay.css({display:"block",marginLeft:"",marginRight:""}),e.justified,e.justified.outerWidth())},"show.uk.dropdown":function(){var o=e.dropdown.dropdown.outerHeight(!0);e.dropdown.element.removeClass("uk-open"),e.overlay.stop().css("display","block").animate({height:o},e.options.duration,function(){e.dropdown.dropdown.css("visibility",""),e.dropdown.element.addClass("uk-open"),t.Utils.checkDisplay(e.dropdown.dropdown,!0)}),e.pointerleave=!1},"hide.uk.dropdown":function(){e.overlay.stop().animate({height:0},e.options.duration)},"pointerenter.uk.dropdown":function(){clearTimeout(e.remainIdle)},"pointerleave.uk.dropdown":function(){e.pointerleave=!0}}),this.overlay.on({mouseenter:function(){e.remainIdle&&(clearTimeout(e.dropdown.remainIdle),clearTimeout(e.remainIdle))},mouseleave:function(){e.pointerleave&&i&&(e.remainIdle=setTimeout(function(){i&&i.hide()},i.options.remaintime))}})}})}(UIkit2);