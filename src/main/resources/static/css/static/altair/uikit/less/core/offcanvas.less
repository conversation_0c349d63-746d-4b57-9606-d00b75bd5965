// Name:            Off-canvas
// Description:     Defines styles for an off-canvas sidebar that slides in and out of the page
//
// Component:       `uk-offcanvas`
//
// Sub-objects:     `uk-offcanvas-page`
//                  `uk-offcanvas-bar`
//                  `uk-offcanvas-close`
//
// Modifiers:       `uk-offcanvas-bar-flip`
//
// States:          `uk-active`
//
// Uses:            Panel: `uk-panel`
//
// Markup:
//
// <!-- uk-offcanvas -->
// <a href="#offcanvas" data-uk-offcanvas>Open</a>
// <div id="offcanvas" class="uk-offcanvas">
//     <div class="uk-offcanvas-bar"></div>
// </div>
//
// ========================================================================


// Variables
// ========================================================================

@offcanvas-z-index:                             1000;
@offcanvas-background:                          rgba(0,0,0,0.1);

@offcanvas-bar-width:                           270px;
@offcanvas-bar-background:                      #333;

@offcanvas-panel-margin:                        20px 15px;
@offcanvas-panel-color:                         #777;
@offcanvas-panel-link-color:                    #ccc;
@offcanvas-panel-link-hover-color:              #fff;
@offcanvas-panel-title-color:                   @offcanvas-panel-link-color;


/* ========================================================================
   Component: Off-canvas
 ========================================================================== */

/*
 * This is the offcanvas overlay and bar container
 * 1. Hide by default
 * 2. Set fixed position
 * 3. Deactivate browser touch actions in IE11
 * 4. Mask the background page
 */

.uk-offcanvas {
    /* 1 */
    display: none;
    /* 2 */
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: @offcanvas-z-index;
    /* 3 */
    touch-action: none;
    /* 4 */
    background: @offcanvas-background;
    .hook-offcanvas;
}

.uk-offcanvas.uk-active { display: block; }


/* Sub-object `uk-offcanvas-page`
 ========================================================================== */

/*
 * Prepares the whole HTML page to slide-out
 * 1. Fix the main page and disallow scrolling
 * 2. Side-out transition
 * 3. Needed for the transition to work instead of just letting it pop to the side
 */

.uk-offcanvas-page {
    /* 1 */
    position: fixed;
    /* 2 */
    -webkit-transition: margin-left 0.3s ease-in-out;
    transition: margin-left 0.3s ease-in-out;
    /* 3 */
    margin-left: 0;
}


/* Sub-object `uk-offcanvas-bar`
 ========================================================================== */

/*
 * This is the offcanvas bar
 * 1. Set fixed position
 * 2. Size and style
 * 3. Allow scrolling
 * 4. Side-out transition
 * 5. Deactivate scroll chaining in IE11
 */

.uk-offcanvas-bar {
    /* 1 */
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    z-index: @offcanvas-z-index + 1;
    /* 2 */
    width: @offcanvas-bar-width;
    max-width: 100%;
    background: @offcanvas-bar-background;
    /* 3 */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* 4 */
    -webkit-transition: -webkit-transform 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out;
    /* 5 */
    -ms-scroll-chaining: none;
    .hook-offcanvas-bar;
}

.uk-offcanvas.uk-active .uk-offcanvas-bar.uk-offcanvas-bar-show {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
}

/* Modifier `uk-offcanvas-bar-flip`
 ========================================================================== */

.uk-offcanvas-bar-flip {
    left: auto;
    right: 0;
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
}

/* Offcanvase modes
 ========================================================================== */
.uk-offcanvas-bar[mode='none'] {
    -webkit-transition: none;
    transition: none;
}

.uk-offcanvas-bar[mode='reveal']{
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
    clip: rect(0, 0, 100vh, 0);
    -webkit-transition: -webkit-transform 0.3s ease-in-out, clip 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out, clip 0.3s ease-in-out;
}

.uk-offcanvas-bar-flip[mode='reveal']{
    clip: none;
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
}

.uk-offcanvas-bar-flip[mode='reveal'] > * {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    -webkit-transition: -webkit-transform 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out;
}

.uk-offcanvas.uk-active .uk-offcanvas-bar-flip[mode='reveal'].uk-offcanvas-bar-show > * {
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
}

/* Panel in offcanvas
 ========================================================================== */

.uk-offcanvas .uk-panel {
    margin: @offcanvas-panel-margin;
    color: @offcanvas-panel-color;
    .hook-offcanvas-panel;
}

.uk-offcanvas .uk-panel-title { color: @offcanvas-panel-title-color; }

.uk-offcanvas .uk-panel a:not([class]) { color: @offcanvas-panel-link-color; }
.uk-offcanvas .uk-panel a:not([class]):hover { color: @offcanvas-panel-link-hover-color; }


// Hooks
// ========================================================================

.hook-offcanvas-misc;

.hook-offcanvas() {}
.hook-offcanvas-bar() {}
.hook-offcanvas-panel() {}
.hook-offcanvas-misc() {}
