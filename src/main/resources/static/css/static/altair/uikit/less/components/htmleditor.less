// Name:            HTML editor
// Description:     Defines styles for an HTML editor
//
// Component:       `uk-htmleditor`
//
// Sub-objects:     `uk-htmleditor-navbar`
//                  `uk-htmleditor-navbar-nav`
//                  `uk-htmleditor-navbar-flip`
//                  `uk-htmleditor-button-code
//                  `uk-htmleditor-button-preview`
//                  `uk-htmleditor-content`
//                  `uk-htmleditor-code`
//                  `uk-htmleditor-preview`
//                  `uk-htmleditor-iframe`
//
// Modifier:        `uk-htmleditor-fullscreen`
//                  `[data-mode='tab']`
//                  `[data-mode='split']`
//
// States:          `uk-active`
//
// ========================================================================


// Variables
// ========================================================================

@htmleditor-navbar-background:                  #eee;

@htmleditor-navbar-nav-height:                  40px;
@htmleditor-navbar-nav-padding-horizontal:      15px;
@htmleditor-navbar-nav-line-height:             @htmleditor-navbar-nav-height;
@htmleditor-navbar-nav-color:                   #444;

@htmleditor-navbar-nav-hover-background:        #f5f5f5;
@htmleditor-navbar-nav-hover-color:             #444;
@htmleditor-navbar-nav-onclick-background:      #ddd;
@htmleditor-navbar-nav-onclick-color:           #444;
@htmleditor-navbar-nav-active-background:       #f5f5f5;
@htmleditor-navbar-nav-active-color:            #444;

@htmleditor-content-border:                     #ddd;
@htmleditor-content-background:                 #fff;
@htmleditor-content-padding:                    20px;
@htmleditor-content-split-border:               #eee;

@htmleditor-fullscreen-z-index:                 990;
@htmleditor-fullscreen-content-top:             @htmleditor-navbar-nav-height;
@htmleditor-fullscreen-icon-resize:             "\f066";


/* ========================================================================
   Component: HTML editor
 ========================================================================== */

.uk-htmleditor {
    .hook-htmleditor;
}


/* Sub-object `uk-htmleditor-navbar`
 ========================================================================== */

.uk-htmleditor-navbar {
    background: @htmleditor-navbar-background;
    .hook-htmleditor-navbar;
}

/*
 * Micro clearfix
 */

.uk-htmleditor-navbar:before,
.uk-htmleditor-navbar:after {
    content: "";
    display: table;
}

.uk-htmleditor-navbar:after { clear: both; }


/* Sub-object `uk-htmleditor-navbar-nav`
 ========================================================================== */

.uk-htmleditor-navbar-nav {
    margin: 0;
    padding: 0;
    list-style: none;
    float: left;
}

.uk-htmleditor-navbar-nav > li { float: left; }

/*
 * 1. Dimensions
 * 2. Style
 */

.uk-htmleditor-navbar-nav > li > a {
    display: block;
    box-sizing: border-box;
    text-decoration: none;
    /* 1 */
    height: @htmleditor-navbar-nav-height;
    padding: 0 @htmleditor-navbar-nav-padding-horizontal;
    line-height: @htmleditor-navbar-nav-line-height;
    /* 2 */
    color: @htmleditor-navbar-nav-color;
    font-size: 11px;
    cursor: pointer;
    .hook-htmleditor-navbar-nav;
}

/*
 * Hover
 * 1. Apply hover style also to focus state
 * 2. Remove default focus style
 */

.uk-htmleditor-navbar-nav > li:hover > a,
.uk-htmleditor-navbar-nav > li > a:focus { // 1
    background-color: @htmleditor-navbar-nav-hover-background;
    color: @htmleditor-navbar-nav-hover-color;
    outline: none; /* 2 */
    .hook-htmleditor-navbar-nav-hover;
}

/* OnClick */
.uk-htmleditor-navbar-nav > li > a:active {
    background-color: @htmleditor-navbar-nav-onclick-background;
    color: @htmleditor-navbar-nav-onclick-color;
    .hook-htmleditor-navbar-nav-onclick;
}

/* Active */
.uk-htmleditor-navbar-nav > li.uk-active > a {
    background-color: @htmleditor-navbar-nav-active-background;
    color: @htmleditor-navbar-nav-active-color;
    .hook-htmleditor-navbar-nav-active;
}


/* Sub-object: `uk-htmleditor-navbar-flip`
 ========================================================================== */

 .uk-htmleditor-navbar-flip { float: right; }


/* Sub-object for special buttons
 ========================================================================== */

[data-mode='split'] .uk-htmleditor-button-code,
[data-mode='split'] .uk-htmleditor-button-preview { display: none; }


/* Sub-object `uk-htmleditor-content`
 ========================================================================== */

.uk-htmleditor-content {
    border-left: 1px solid @htmleditor-content-border;
    border-right: 1px solid @htmleditor-content-border;
    border-bottom: 1px solid @htmleditor-content-border;
    background: @htmleditor-content-background;
    .hook-htmleditor-content;
}

/*
 * Micro clearfix
 */

.uk-htmleditor-content:before,
.uk-htmleditor-content:after {
    content: "";
    display: table;
}

.uk-htmleditor-content:after { clear: both; }


/* Modifier `uk-htmleditor-fullscreen`
 ========================================================================== */

.uk-htmleditor-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: @htmleditor-fullscreen-z-index;
}

.uk-htmleditor-fullscreen .uk-htmleditor-content {
    position: absolute;
    top: @htmleditor-fullscreen-content-top;
    left: 0;
    right: 0;
    bottom: 0;
}


.uk-htmleditor-fullscreen .uk-icon-expand:before { content: @htmleditor-fullscreen-icon-resize; }


/* Sub-objects `uk-htmleditor-code` and `uk-htmleditor-preview`
 ========================================================================== */

.uk-htmleditor-code,
.uk-htmleditor-preview { box-sizing: border-box; }

.uk-htmleditor-preview {
    padding: @htmleditor-content-padding;
    overflow-y: scroll;
    position: relative;
}

/*
 * Tab view
 */

[data-mode='tab'][data-active-tab='code'] .uk-htmleditor-preview,
[data-mode='tab'][data-active-tab='preview'] .uk-htmleditor-code { display: none; }

/*
 * Split view
 */

[data-mode='split'] .uk-htmleditor-code,
[data-mode='split'] .uk-htmleditor-preview {
    float: left;
    width: 50%;
}

[data-mode='split'] .uk-htmleditor-code { border-right: 1px solid @htmleditor-content-split-border; }


/* Sub-object `uk-htmleditor-iframe`
 ========================================================================== */

.uk-htmleditor-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}


/* CodeMirror modifications
 ========================================================================== */

.uk-htmleditor .CodeMirror {
    padding: 10px;
    box-sizing: border-box;
}


// Hooks
// ========================================================================

.hook-htmleditor-misc;

.hook-htmleditor() {}
.hook-htmleditor-navbar() {}
.hook-htmleditor-navbar-nav() {}
.hook-htmleditor-navbar-nav-hover() {}
.hook-htmleditor-navbar-nav-onclick() {}
.hook-htmleditor-navbar-nav-active() {}
.hook-htmleditor-content() {}
.hook-htmleditor-misc() {}