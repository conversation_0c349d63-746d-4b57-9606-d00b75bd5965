/*! UIkit 2.27.5 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){var i;window.UIkit2&&(i=t(UIkit2)),"function"==typeof define&&define.amd&&define("uikit-grid",["uikit"],function(){return i||t(UIkit2)})}(function(t){"use strict";t.component("grid",{defaults:{colwidth:"auto",animation:!0,duration:300,gutter:0,controls:!1,filter:!1,origin:t.langdirection},boot:function(){t.ready(function(i){t.$("[data-uk-grid]",i).each(function(){var i=t.$(this);i.data("grid")||t.grid(i,t.Utils.options(i.attr("data-uk-grid")))})})},init:function(){var i=this,e=String(this.options.gutter).trim().split(" ");this.gutterv=parseInt(e[0],10),this.gutterh=parseInt(e[1]||e[0],10),this.element.css({position:"relative"}),this.controls=null,this.origin=this.options.origin,this.options.controls&&(this.controls=t.$(this.options.controls),this.controls.on("click","[data-uk-filter]",function(e){e.preventDefault(),i.filter(t.$(this).attr("data-uk-filter"))}),this.controls.on("click","[data-uk-sort]",function(e){e.preventDefault();var n=t.$(this).attr("data-uk-sort").split(":");i.sort(n[0],n[1])})),t.$win.on("load resize orientationchange",t.Utils.debounce(function(){i.currentfilter?i.filter(i.currentfilter):this.update()}.bind(this),100)),this.on("display.uk.check",function(){i.element.is(":visible")&&i.update()}),t.domObserve(this.element,function(){i.update()}),this.options.filter!==!1?this.filter(this.options.filter):this.update()},_prepareElements:function(){var t,i=this.element.children().not("[data-grid-prepared]");i.length&&(t={position:"absolute",boxSizing:"border-box",width:"auto"==this.options.colwidth?"":this.options.colwidth},this.options.gutter&&(t["padding-"+this.origin]=this.gutterh,t["padding-bottom"]=this.gutterv,this.element.css("margin-"+this.origin,-1*this.gutterh)),i.attr("data-grid-prepared","true").css(t))},update:function(i){var e=this;this._prepareElements(),i=i||this.element.children(":visible");var n,o,s,r,a,h,d,l,u,c=i,f=this.element.width()+2*this.gutterh+2,p=0,g=0,m=[];this.trigger("beforeupdate.uk.grid",[c]),c.each(function(){for(n=t.$(this),u=this.getBoundingClientRect(),o=u.width,s=u.height,p=0,g=0,h=0,l=m.length;l>h;h++)r=m[h],p<=r.aX&&(p=r.aX),p+o>f&&(p=0),g<=r.aY&&(g=r.aY);a={ele:n,top:g,width:o,height:s,aY:g+s,aX:p+o},a[e.origin]=p,m.push(a)});var v,k,b=0;for(h=0,l=m.length;l>h;h++){for(r=m[h],g=0,d=0;h>d;d++)v=m[d],r[this.origin]<v.aX&&v[this.origin]+1<r.aX&&(g=v.aY);r.top=g,r.aY=g+r.height,b=Math.max(b,r.aY)}b-=this.gutterv,this.options.animation?(this.element.stop().animate({height:b},100),m.forEach(function(t){k={top:t.top,opacity:1},k[e.origin]=t[e.origin],t.ele.stop().animate(k,this.options.duration)}.bind(this))):(this.element.css("height",b),m.forEach(function(t){k={top:t.top,opacity:1},k[e.origin]=t[e.origin],t.ele.css(k)}.bind(this))),setTimeout(function(){t.$doc.trigger("scrolling.uk.document")},2*this.options.duration*(this.options.animation?1:0)),this.trigger("afterupdate.uk.grid",[c])},filter:function(i){this.currentfilter=i,i=i||[],"number"==typeof i&&(i=i.toString()),"string"==typeof i&&(i=i.split(/,/).map(function(t){return t.trim()}));var e=this,n=this.element.children(),o={visible:[],hidden:[]};n.each(function(){var e=t.$(this),n=e.attr("data-uk-filter"),s=i.length?!1:!0;n&&(n=n.split(/,/).map(function(t){return t.trim()}),i.forEach(function(t){n.indexOf(t)>-1&&(s=!0)})),o[s?"visible":"hidden"].push(e)}),o.hidden=t.$(o.hidden).map(function(){return this[0]}),o.visible=t.$(o.visible).map(function(){return this[0]}),o.hidden.attr("aria-hidden","true").filter(":visible").fadeOut(this.options.duration),o.visible.attr("aria-hidden","false").filter(":hidden").css("opacity",0).show(),e.update(o.visible),this.controls&&this.controls.length&&this.controls.find("[data-uk-filter]").removeClass("uk-active").filter('[data-uk-filter="'+i+'"]').addClass("uk-active")},sort:function(i,e){e=e||1,"string"==typeof e&&(e="desc"==e.toLowerCase()?-1:1);var n=this.element.children();n.sort(function(n,o){return n=t.$(n),o=t.$(o),(o.data(i)||"")<(n.data(i)||"")?e:-1*e}).appendTo(this.element),this.update(n.filter(":visible")),this.controls&&this.controls.length&&this.controls.find("[data-uk-sort]").removeClass("uk-active").filter('[data-uk-sort="'+i+":"+(-1==e?"desc":"asc")+'"]').addClass("uk-active")}})});