/*! UIkit 2.27.5 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){var i;window.UIkit2&&(i=t(UIkit2)),"function"==typeof define&&define.amd&&define("uikit-accordion",["uikit"],function(){return i||t(UIkit2)})}(function(t){"use strict";function i(i){var e=t.$(i),o="auto";if(e.is(":visible"))o=e.outerHeight();else{var a={position:e.css("position"),visibility:e.css("visibility"),display:e.css("display")};o=e.css({position:"absolute",visibility:"hidden",display:"block"}).outerHeight(),e.css(a)}return o}return t.component("accordion",{defaults:{showfirst:!0,collapse:!0,animate:!0,easing:"swing",duration:300,toggle:".uk-accordion-title",containers:".uk-accordion-content",clsactive:"uk-active"},boot:function(){t.ready(function(i){setTimeout(function(){t.$("[data-uk-accordion]",i).each(function(){var i=t.$(this);i.data("accordion")||t.accordion(i,t.Utils.options(i.attr("data-uk-accordion")))})},0)})},init:function(){var i=this;this.element.on("click.uk.accordion",this.options.toggle,function(e){e.preventDefault(),i.toggleItem(t.$(this).data("wrapper"),i.options.animate,i.options.collapse)}),this.update(!0),t.domObserve(this.element,function(){i.element.children(i.options.containers).length&&i.update()})},toggleItem:function(e,o,a){var n=this;e.data("toggle").toggleClass(this.options.clsactive),e.data("content").toggleClass(this.options.clsactive);var s=e.data("toggle").hasClass(this.options.clsactive);a&&(this.toggle.not(e.data("toggle")).removeClass(this.options.clsactive),this.content.not(e.data("content")).removeClass(this.options.clsactive).parent().stop().css("overflow","hidden").animate({height:0},{easing:this.options.easing,duration:o?this.options.duration:0}).attr("aria-expanded","false")),e.stop().css("overflow","hidden"),o?e.animate({height:s?i(e.data("content")):0},{easing:this.options.easing,duration:this.options.duration,complete:function(){s&&(e.css({overflow:"",height:"auto"}),t.Utils.checkDisplay(e.data("content"))),n.trigger("display.uk.check")}}):(e.height(s?"auto":0),s&&(e.css({overflow:""}),t.Utils.checkDisplay(e.data("content"))),this.trigger("display.uk.check")),e.attr("aria-expanded",s),this.element.trigger("toggle.uk.accordion",[s,e.data("toggle"),e.data("content")])},update:function(i){var e,o,a,n=this;this.toggle=this.find(this.options.toggle),this.content=this.find(this.options.containers),this.content.each(function(i){e=t.$(this),e.parent().data("wrapper")?o=e.parent():(o=t.$(this).wrap('<div data-wrapper="true" style="overflow:hidden;height:0;position:relative;"></div>').parent(),o.attr("aria-expanded","false")),a=n.toggle.eq(i),o.data("toggle",a),o.data("content",e),a.data("wrapper",o),e.data("wrapper",o)}),this.element.trigger("update.uk.accordion",[this]),i&&this.options.showfirst&&this.toggleItem(this.toggle.eq(0).data("wrapper"),!1,!1)}}),t.accordion});