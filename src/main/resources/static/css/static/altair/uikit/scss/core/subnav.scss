// Name:            Subnav
// Description:     Defines styles for the sub navigation
//
// Component:       `uk-subnav`
//
// Modifiers:       `uk-subnav-line`
//                  `uk-subnav-pill`
//
// States:          `uk-active`
//
// Markup:
//
// <!-- uk-subnav -->
// <ul class="uk-subnav">
//     <li><a href=""></a></li>
// </ul>
//
// ========================================================================


// Variables
// ========================================================================

$subnav-margin-horizontal:                       10px !default;
$subnav-margin-vertical:                         $subnav-margin-horizontal !default;

$subnav-color:                                   #444 !default;
$subnav-hover-color:                             #07D !default;
$subnav-hover-text-decoration:                   none !default;
$subnav-active-color:                            #07D !default;

$subnav-line-border-height:                      10px !default;
$subnav-line-border:                             #ddd !default;
$subnav-line-border-width:                       1px !default;

$subnav-pill-padding-vertical:                   3px !default;
$subnav-pill-padding-horizontal:                 9px !default;
$subnav-pill-hover-background:                   #eee !default;
$subnav-pill-hover-color:                        #444 !default;
$subnav-pill-active-background:                  #00a8e6 !default;
$subnav-pill-active-color:                       #fff !default;

$subnav-disabled-color:                          #999 !default;


/* ========================================================================
   Component: Subnav
 ========================================================================== */

/*
 * 1. Gutter
 * 2. Remove default list style
 */

.uk-subnav {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    /* 1 */
    margin-left: -$subnav-margin-horizontal;
    margin-top: -$subnav-margin-vertical;
    /* 2 */
    padding: 0;
    list-style: none;
}

/*
 * 1. Space is allocated solely based on content dimensions
 * 2. Horizontal gutter is using `padding` so `uk-width-*` classes can be applied
 * 3. Create position context for dropdowns
 */

.uk-subnav > * {
    /* 1 */
    -ms-flex: none;
    -webkit-flex: none;
    flex: none;
    /* 2 */
    padding-left: $subnav-margin-horizontal;
    margin-top: $subnav-margin-vertical;
    /* 3 */
    position: relative;
}

/*
 * DEPRECATED IE9 Support
 */

.uk-subnav:before,
.uk-subnav:after {
    content: "";
    display: block;
    overflow: hidden;
}

.uk-subnav:after { clear: both; }

.uk-subnav > * { float: left; }


/* Items
 ========================================================================== */

.uk-subnav > * > * {
    display: inline-block;
    color: $subnav-color;
    @include hook-subnav();
}

/*
 * Hover
 * 1. Apply hover style also to focus state
 */

.uk-subnav > * > :hover,
.uk-subnav > * > :focus { // 1
    color: $subnav-hover-color;
    text-decoration: $subnav-hover-text-decoration;
    @include hook-subnav-hover();
}

/*
 * Active
 */

.uk-subnav > .uk-active > * {
    color: $subnav-active-color;
    @include hook-subnav-active();
}


/* Modifier: 'subnav-line'
 ========================================================================== */

.uk-subnav-line > :before {
    content: "";
    display: inline-block;
    height: $subnav-line-border-height;
    vertical-align: middle;
}

.uk-subnav-line > :nth-child(n+2):before {
    margin-right: $subnav-margin-horizontal;
    border-left: $subnav-line-border-width solid $subnav-line-border;
    @include hook-subnav-line-divider();
}


/* Modifier: 'subnav-pill'
 ========================================================================== */

.uk-subnav-pill > * > * {
    padding: $subnav-pill-padding-vertical $subnav-pill-padding-horizontal;
    @include hook-subnav-pill();
}

/*
 * Hover
 * 1. Apply hover style also to focus state
 * 2. Remove default focus style
 */

.uk-subnav-pill > * > :hover,
.uk-subnav-pill > * > :focus { // 1
    background: $subnav-pill-hover-background;
    color: $subnav-pill-hover-color;
    text-decoration: none;
    /* 2 */
    outline: none;
    @include hook-subnav-pill-hover();
}

/*
 * Active
 * `li` needed for higher specificity to override hover
 */

.uk-subnav-pill > .uk-active > * {
    background: $subnav-pill-active-background;
    color: $subnav-pill-active-color;
    @include hook-subnav-pill-active();
}


/* Disabled state
 ========================================================================== */

.uk-subnav > .uk-disabled > * {
    background: none;
    color: $subnav-disabled-color;
    text-decoration: none;
    cursor: text;
    @include hook-subnav-disabled();
}


// Hooks
// ========================================================================

@include hook-subnav-misc();

// @mixin hook-subnav(){}
// @mixin hook-subnav-hover(){}
// @mixin hook-subnav-active(){}
// @mixin hook-subnav-line-divider(){}
// @mixin hook-subnav-pill(){}
// @mixin hook-subnav-pill-hover(){}
// @mixin hook-subnav-pill-active(){}
// @mixin hook-subnav-disabled(){}
// @mixin hook-subnav-misc(){}