/*! UIkit 2.27.5 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){var n;if(!window.jQuery)throw new Error("UIkit 2.x requires jQuery");n=t(window.jQuery),"function"==typeof define&&define.amd&&define("uikit",function(){return n.load=function(t,e,o,i){var r,a=t.split(","),s=[],u=(i.config&&i.config.uikit&&i.config.uikit.base?i.config.uikit.base:"").replace(/\/+$/g,"");if(!u)throw new Error("Please define base path to UIkit in the requirejs config.");for(r=0;r<a.length;r+=1){var c=a[r].replace(/\./g,"/");s.push(u+"/components/"+c)}e(s,function(){o(n)})},n})}(function(t){"use strict";if(window.UIkit2)return window.UIkit2;var n={},e=window.UIkit||void 0;if(n.version="2.27.5",n.noConflict=function(){return e&&(window.UIkit=e,t.UIkit=e,t.fn.uk=e.fn),n},window.UIkit2=n,e||(window.UIkit=n),n.$=t,n.$doc=n.$(document),n.$win=n.$(window),n.$html=n.$("html"),n.support={},n.support.transition=function(){var t=function(){var t,n=document.body||document.documentElement,e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(t in e)if(void 0!==n.style[t])return e[t]}();return t&&{end:t}}(),n.support.animation=function(){var t=function(){var t,n=document.body||document.documentElement,e={WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(t in e)if(void 0!==n.style[t])return e[t]}();return t&&{end:t}}(),function(){Date.now=Date.now||function(){return(new Date).getTime()};for(var t=["webkit","moz"],n=0;n<t.length&&!window.requestAnimationFrame;++n){var e=t[n];window.requestAnimationFrame=window[e+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e+"CancelAnimationFrame"]||window[e+"CancelRequestAnimationFrame"]}if(/iP(ad|hone|od).*OS 6/.test(window.navigator.userAgent)||!window.requestAnimationFrame||!window.cancelAnimationFrame){var o=0;window.requestAnimationFrame=function(t){var n=Date.now(),e=Math.max(o+16,n);return setTimeout(function(){t(o=e)},e-n)},window.cancelAnimationFrame=clearTimeout}}(),n.support.touch="ontouchstart"in document||window.DocumentTouch&&document instanceof window.DocumentTouch||window.navigator.msPointerEnabled&&window.navigator.msMaxTouchPoints>0||window.navigator.pointerEnabled&&window.navigator.maxTouchPoints>0||!1,n.support.mutationobserver=window.MutationObserver||window.WebKitMutationObserver||null,n.Utils={},n.Utils.isFullscreen=function(){return document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.fullscreenElement||!1},n.Utils.str2json=function(t,n){try{return n?JSON.parse(t.replace(/([\$\w]+)\s*:/g,function(t,n){return'"'+n+'":'}).replace(/'([^']+)'/g,function(t,n){return'"'+n+'"'})):new Function("","var json = "+t+"; return JSON.parse(JSON.stringify(json));")()}catch(e){return!1}},n.Utils.debounce=function(t,n,e){var o;return function(){var i=this,r=arguments,a=function(){o=null,e||t.apply(i,r)},s=e&&!o;clearTimeout(o),o=setTimeout(a,n),s&&t.apply(i,r)}},n.Utils.throttle=function(t,n){var e=!1;return function(){e||(t.call(),e=!0,setTimeout(function(){e=!1},n))}},n.Utils.removeCssRules=function(t){var n,e,o,i,r,a,s,u,c,d;t&&setTimeout(function(){try{for(d=document.styleSheets,i=0,s=d.length;s>i;i++){for(o=d[i],e=[],o.cssRules=o.cssRules,n=r=0,u=o.cssRules.length;u>r;n=++r)o.cssRules[n].type===CSSRule.STYLE_RULE&&t.test(o.cssRules[n].selectorText)&&e.unshift(n);for(a=0,c=e.length;c>a;a++)o.deleteRule(e[a])}}catch(l){}},0)},n.Utils.isInView=function(e,o){var i=t(e);if(!i.is(":visible"))return!1;var r=n.$win.scrollLeft(),a=n.$win.scrollTop(),s=i.offset(),u=s.left,c=s.top;return o=t.extend({topoffset:0,leftoffset:0},o),c+i.height()>=a&&c-o.topoffset<=a+n.$win.height()&&u+i.width()>=r&&u-o.leftoffset<=r+n.$win.width()?!0:!1},n.Utils.checkDisplay=function(e,o){var i=n.$("[data-uk-margin], [data-uk-grid-match], [data-uk-grid-margin], [data-uk-check-display]",e||document);return e&&!i.length&&(i=t(e)),i.trigger("display.uk.check"),o&&("string"!=typeof o&&(o='[class*="uk-animation-"]'),i.find(o).each(function(){var t=n.$(this),e=t.attr("class"),o=e.match(/uk-animation-(.+)/);t.removeClass(o[0]).width(),t.addClass(o[0])})),i},n.Utils.options=function(e){if("string"!=t.type(e))return e;-1!=e.indexOf(":")&&"}"!=e.trim().substr(-1)&&(e="{"+e+"}");var o=e?e.indexOf("{"):-1,i={};if(-1!=o)try{i=n.Utils.str2json(e.substr(o))}catch(r){}return i},n.Utils.animate=function(e,o){var i=t.Deferred();return e=n.$(e),e.css("display","none").addClass(o).one(n.support.animation.end,function(){e.removeClass(o),i.resolve()}),e.css("display",""),i.promise()},n.Utils.uid=function(t){return(t||"id")+(new Date).getTime()+"RAND"+Math.ceil(1e5*Math.random())},n.Utils.template=function(t,n){for(var e,o,i,r,a=t.replace(/\n/g,"\\n").replace(/\{\{\{\s*(.+?)\s*\}\}\}/g,"{{!$1}}").split(/(\{\{\s*(.+?)\s*\}\})/g),s=0,u=[],c=0;s<a.length;){if(e=a[s],e.match(/\{\{\s*(.+?)\s*\}\}/))switch(s+=1,e=a[s],o=e[0],i=e.substring(e.match(/^(\^|\#|\!|\~|\:)/)?1:0),o){case"~":u.push("for(var $i=0;$i<"+i+".length;$i++) { var $item = "+i+"[$i];"),c++;break;case":":u.push("for(var $key in "+i+") { var $val = "+i+"[$key];"),c++;break;case"#":u.push("if("+i+") {"),c++;break;case"^":u.push("if(!"+i+") {"),c++;break;case"/":u.push("}"),c--;break;case"!":u.push("__ret.push("+i+");");break;default:u.push("__ret.push(escape("+i+"));")}else u.push("__ret.push('"+e.replace(/\'/g,"\\'")+"');");s+=1}return r=new Function("$data",["var __ret = [];","try {","with($data){",c?'__ret = ["Not all blocks are closed correctly."]':u.join(""),"};","}catch(e){__ret = [e.message];}",'return __ret.join("").replace(/\\n\\n/g, "\\n");',"function escape(html) { return String(html).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/</g, '&lt;').replace(/>/g, '&gt;');}"].join("\n")),n?r(n):r},n.Utils.focus=function(n,e){if(n=t(n),!n.length)return n;var o,i=n.find("[autofocus]:first");return i.length?i.focus():(i=n.find(":input"+(e&&","+e||"")).first(),i.length?i.focus():(n.attr("tabindex")||(o=1e3,n.attr("tabindex",o)),n[0].focus(),o&&n.attr("tabindex",""),n))},n.Utils.events={},n.Utils.events.click=n.support.touch?"tap":"click",n.fn=function(e,o){var i=arguments,r=e.match(/^([a-z\-]+)(?:\.([a-z]+))?/i),a=r[1],s=r[2];return n[a]?this.each(function(){var e=t(this),r=e.data(a);r||e.data(a,r=n[a](this,s?void 0:o)),s&&r[s].apply(r,Array.prototype.slice.call(i,1))}):(t.error("UIkit component ["+a+"] does not exist."),this)},t.UIkit=n,t.fn.uk=n.fn,n.langdirection="rtl"==n.$html.attr("dir")?"right":"left",n.components={},n.component=function(e,o,i){if(n.components[e]&&!i)return n.components[e];var r=function(o,i){var a=this;return this.UIkit=n,this.element=o?n.$(o):null,this.options=t.extend(!0,{},this.defaults,i),this.plugins={},this.element&&this.element.data(e,this),this.init(),(this.options.plugins.length?this.options.plugins:Object.keys(r.plugins)).forEach(function(t){r.plugins[t].init&&(r.plugins[t].init(a),a.plugins[t]=!0)}),this.trigger("init.uk.component",[e,this]),this};return r.plugins={},t.extend(!0,r.prototype,{defaults:{plugins:[]},boot:function(){},init:function(){},on:function(t,e,o){return n.$(this.element||this).on(t,e,o)},one:function(t,e,o){return n.$(this.element||this).one(t,e,o)},off:function(t){return n.$(this.element||this).off(t)},trigger:function(t,e){return n.$(this.element||this).trigger(t,e)},find:function(t){return n.$(this.element?this.element:[]).find(t)},proxy:function(t,n){var e=this;n.split(" ").forEach(function(n){e[n]||(e[n]=function(){return t[n].apply(t,arguments)})})},mixin:function(t,n){var e=this;n.split(" ").forEach(function(n){e[n]||(e[n]=t[n].bind(e))})},option:function(){return 1==arguments.length?this.options[arguments[0]]||void 0:(2==arguments.length&&(this.options[arguments[0]]=arguments[1]),void 0)}},o),this.components[e]=r,this[e]=function(){var o,i;if(arguments.length)switch(arguments.length){case 1:"string"==typeof arguments[0]||arguments[0].nodeType||arguments[0]instanceof jQuery?o=t(arguments[0]):i=arguments[0];break;case 2:o=t(arguments[0]),i=arguments[1]}return o&&o.data(e)?o.data(e):new n.components[e](o,i)},n.domready&&n.component.boot(e),r},n.plugin=function(t,n,e){this.components[t].plugins[n]=e},n.component.boot=function(t){n.components[t].prototype&&n.components[t].prototype.boot&&!n.components[t].booted&&(n.components[t].prototype.boot.apply(n,[]),n.components[t].booted=!0)},n.component.bootComponents=function(){for(var t in n.components)n.component.boot(t)},n.domObservers=[],n.domready=!1,n.ready=function(t){n.domObservers.push(t),n.domready&&t(document)},n.on=function(t,e,o){return t&&t.indexOf("ready.uk.dom")>-1&&n.domready&&e.apply(n.$doc),n.$doc.on(t,e,o)},n.one=function(t,e,o){return t&&t.indexOf("ready.uk.dom")>-1&&n.domready?(e.apply(n.$doc),n.$doc):n.$doc.one(t,e,o)},n.trigger=function(t,e){return n.$doc.trigger(t,e)},n.domObserve=function(t,e){n.support.mutationobserver&&(e=e||function(){},n.$(t).each(function(){var t=this,o=n.$(t);if(!o.data("observer"))try{var i=new n.support.mutationobserver(n.Utils.debounce(function(){e.apply(t,[o]),o.trigger("changed.uk.dom")},50),{childList:!0,subtree:!0});i.observe(t,{childList:!0,subtree:!0}),o.data("observer",i)}catch(r){}}))},n.init=function(t){t=t||document,n.domObservers.forEach(function(n){n(t)})},n.on("domready.uk.dom",function(){n.init(),n.domready&&n.Utils.checkDisplay()}),document.addEventListener("DOMContentLoaded",function(){var e=function(){n.$body=n.$("body"),n.trigger("beforeready.uk.dom"),n.component.bootComponents();var e=requestAnimationFrame(function(){var t={dir:{x:0,y:0},x:window.pageXOffset,y:window.pageYOffset},o=function(){var i=window.pageXOffset,r=window.pageYOffset;(t.x!=i||t.y!=r)&&(t.dir.x=i!=t.x?i>t.x?1:-1:0,t.dir.y=r!=t.y?r>t.y?1:-1:0,t.x=i,t.y=r,n.$doc.trigger("scrolling.uk.document",[{dir:{x:t.dir.x,y:t.dir.y},x:i,y:r}])),cancelAnimationFrame(e),e=requestAnimationFrame(o)};return n.support.touch&&n.$html.on("touchmove touchend MSPointerMove MSPointerUp pointermove pointerup",o),(t.x||t.y)&&o(),o}());if(n.trigger("domready.uk.dom"),n.support.touch&&navigator.userAgent.match(/(iPad|iPhone|iPod)/g)&&n.$win.on("load orientationchange resize",n.Utils.debounce(function(){var n=function(){return t(".uk-height-viewport").css("height",window.innerHeight),n};return n()}(),100)),n.trigger("afterready.uk.dom"),n.domready=!0,n.support.mutationobserver){var o=n.Utils.debounce(function(){requestAnimationFrame(function(){n.init(document.body)})},10);new n.support.mutationobserver(function(t){var n=!1;t.every(function(t){if("childList"!=t.type)return!0;for(var e,o=0;o<t.addedNodes.length;++o)if(e=t.addedNodes[o],e.outerHTML&&-1!==e.outerHTML.indexOf("data-uk-"))return(n=!0)&&!1;return!0}),n&&o()}).observe(document.body,{childList:!0,subtree:!0})}};return("complete"==document.readyState||"interactive"==document.readyState)&&setTimeout(e),e}()),n.$html.addClass(n.support.touch?"uk-touch":"uk-notouch"),n.support.touch){var o,i=!1,r="uk-hover",a=".uk-overlay, .uk-overlay-hover, .uk-overlay-toggle, .uk-animation-hover, .uk-has-hover";n.$html.on("mouseenter touchstart MSPointerDown pointerdown",a,function(){i&&t("."+r).removeClass(r),i=t(this).addClass(r)}).on("mouseleave touchend MSPointerUp pointerup",function(n){o=t(n.target).parents(a),i&&i.not(o).removeClass(r)})}return n});