// Name:            Progress
// Description:     Defines styles for progress bars
//
// Component:       `uk-progress`
//
// Sub-objects:     `uk-progress-bar`
//
// Modifiers:       `uk-progress-mini`
//                  `uk-progress-small`
//                  `uk-progress-success`
//                  `uk-progress-warning`
//                  `uk-progress-danger`
//                  `uk-progress-striped`
//
// Markup:
//
// <!-- uk-progress -->
// <div class="uk-progress">
//     <div class="uk-progress-bar" style="width: 40%;"></div>
// </div>
//
// ========================================================================


// Variables
// ========================================================================

@progress-height:                               20px;
@progress-mini-height:                          6px;
@progress-small-height:                         12px;

@progress-margin-vertical:                      15px;
@progress-background:                           #eee;

@progress-bar-background:                       #00a8e6;
@progress-bar-font-size:                        12px;
@progress-bar-color:                            #fff;

@progress-bar-success-background:               #8cc14c;
@progress-bar-warning-background:               #faa732;
@progress-bar-danger-background:                #da314b;


/* ========================================================================
   Component: Progress
 ========================================================================== */

/*
 * 1. Clearing
 * 2. Vertical alignment if text is used
 */

.uk-progress {
    box-sizing: border-box;
    height: @progress-height;
    margin-bottom: @progress-margin-vertical;
    background: @progress-background;
    /* 1 */
    overflow: hidden;
    /* 2 */
    line-height: @progress-height;
    .hook-progress;
}

/*
 * Add margin if adjacent element
 */

* + .uk-progress { margin-top: @progress-margin-vertical; }


/* Sub-object: `uk-progress-bar`
 ========================================================================== */

/*
 * 1. Transition
 * 2. Needed for text
 */

.uk-progress-bar {
    width: 0;
    height: 100%;
    background: @progress-bar-background;
    float: left;
    /* 1 */
    -webkit-transition: width 0.6s ease;
    transition: width 0.6s ease;
    /* 2 */
    font-size: @progress-bar-font-size;
    color: @progress-bar-color;
    text-align: center;
    .hook-progress-bar;
}


/* Size modifiers
 ========================================================================== */

/* Mini */
.uk-progress-mini {
    height: @progress-mini-height;
    .hook-progress-mini;
}


/* Small */
.uk-progress-small {
    height: @progress-small-height;
    .hook-progress-small;
}


/* Color modifiers
 ========================================================================== */

.uk-progress-success .uk-progress-bar {
    background-color: @progress-bar-success-background;
    .hook-progress-bar-success;
}

.uk-progress-warning .uk-progress-bar {
    background-color: @progress-bar-warning-background;
    .hook-progress-bar-warning;
}

.uk-progress-danger .uk-progress-bar {
    background-color: @progress-bar-danger-background;
    .hook-progress-bar-danger;
}


/* Modifier: `uk-progress-striped`
 ========================================================================== */

.uk-progress-striped .uk-progress-bar {
    background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 30px 30px;
}

/*
 * Animation
 */

.uk-progress-striped.uk-active .uk-progress-bar {
    -webkit-animation: uk-progress-bar-stripes 2s linear infinite;
    animation: uk-progress-bar-stripes 2s linear infinite;
}

@-webkit-keyframes uk-progress-bar-stripes {
    0% { background-position: 0 0;}
    100% { background-position: 30px 0; }
}

@keyframes uk-progress-bar-stripes {
    0% { background-position: 0 0;}
    100% { background-position: 30px 0; }
}


// Hooks
// ========================================================================

.hook-progress-misc;

.hook-progress() {}
.hook-progress-bar() {}
.hook-progress-mini() {}
.hook-progress-small() {}
.hook-progress-bar-success() {}
.hook-progress-bar-warning() {}
.hook-progress-bar-danger() {}
.hook-progress-misc() {}