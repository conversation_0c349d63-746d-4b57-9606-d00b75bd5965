app.version=1.0.2.PROD
app.site=CUSM
## FlyWay Config

spring.flyway.locations=classpath:db/migrations/oracle/prod
spring.flyway.schemas=OWORD_TOOLS
spring.flyway.placeholders.schema.name=OWORD_TOOLS
#Accesanyware stest datasource
spring.datasource.aaw.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.aaw.url=*******************************************************
spring.datasource.aaw.username=STRM_GUA

# Primary DataSource OWord QA
spring.datasource.osearch.url=*********************************************************
spring.datasource.osearch.username=OWORD
spring.datasource.osearch.driver-class-name=oracle.jdbc.driver.OracleDriver
# Secondary DataSource OWord_Tools QA
spring.datasource.owordtools.url=*********************************************************
spring.datasource.owordtools.username=OWORD_TOOLS
spring.datasource.owordtools.driver-class-name=oracle.jdbc.driver.OracleDriver

server.port=9090
spring.config.import=file:c:/data/input/config/prod/secret.properties