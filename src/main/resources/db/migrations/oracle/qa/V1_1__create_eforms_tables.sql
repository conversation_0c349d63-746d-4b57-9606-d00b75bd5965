-- Create tables for eforms model

-- Role table
CREATE TABLE roles (
  id NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  name VA<PERSON>HAR2(45) NOT NULL
);

-- Account table

CREATE TABLE account (
  id NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  FIRST_NAME VARCHAR2(32) NULL,
  LAST_NAME VARCHAR2(32) DEFAULT 'Last name' NOT NULL,
  USER_NAME VARCHAR2(16) DEFAULT 'username' NOT NULL,
  CREATED TIMESTAMP NULL,
  EMAIL VARCHAR2(128) NULL,
  PASSWORD VARCHAR2(255) NULL,
  ROLE VARCHAR2(255) NULL,
  LANGUAGE VARCHAR2(45) DEFAULT 'FR' NOT NULL,
  STATUS VARCHAR2(45) DEFAULT 'ACTIVE' NOT NULL,
  CONSTRAINT Email UNIQUE (EMAIL),
  CONSTRAINT UserName UNIQUE (USER_NAME)
);

-- LogDocumentMngt table

CREATE TABLE DOCUMENT_MNGT (
  DOCUMENT_MNGT_ID NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  DOCUMENT_MNGT_ACTION VARCHAR2(255) NOT NULL,
  DOCUMENT_MNGT_LAST_STATUS VARCHAR2(255) NOT NULL,
  DOCUMENT_MNGT_NEW_STATUS VARCHAR2(255) NOT NULL,
  DOCUMENT_MNGT_DOCUMENT_ID VARCHAR2(255) NOT NULL,
  DOCUMENT_MNGT_DOCUMENT_MRN VARCHAR2(255) NOT NULL,
  DOCUMENT_MNGT_DOCUMENT_SITE VARCHAR2(255) NOT NULL,
  DOCUMENT_MNGT_USER VARCHAR2(255) NOT NULL,
  DOCUMENT_MNGT_DATE TIMESTAMP NOT NULL,
  DOCUMENT_MNGT_DESCRIPTION VARCHAR2(255) NOT NULL
);

-- LogRapportMngt table
CREATE TABLE RAPPORT_MNGT (
  RAPPORT_MNGT_ID NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  RAPPORT_MNGT_TYPE VARCHAR2(255) NOT NULL,
  RAPPORT_MNGT_USER VARCHAR2(255) NOT NULL,
  RAPPORT_MNGT_DATE TIMESTAMP NOT NULL,
  RAPPORT_MNGT_DESCRIPTION VARCHAR2(255)
);

-- LogConfigMngt table

CREATE TABLE CONFIG_MNGT (
  CONFIG_MNGT_ID NUMBER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  CONFIG_MNGT_TABLE VARCHAR2(255) NOT NULL,
  CONFIG_MNGT_ACTION VARCHAR2(255) NOT NULL,
  CONFIG_MNGT_USER VARCHAR2(255) NOT NULL,
  CONFIG_MNGT_DATE TIMESTAMP NOT NULL,
  CONFIG_MNGT_ELEMKEY VARCHAR2(255) NOT NULL,
  CONFIG_MNGT_DESCRIPTION VARCHAR2(255)
);