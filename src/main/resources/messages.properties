# Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
# Click nbfs://nbhost/SystemFileSystem/Templates/Other/properties.properties to edit this template
view.index.title=Oword  
# Menu title 
title.doctype=Doc Types 
#clinicien
copy.row=copy a result  
# validtion
validate.txtfrench=Please enter the name in French
validate.txtenglish=Please enter the name in English
validate.txtcategory=Please enter a category
validate.txtclassification=Please enter a classification
validate.txtservicecode=Please enter a service code 
validate.select.doctype=Please select a document type
validate.select.classification=Please select a classification code
validate.select.service=Please select a service
validate.nopatient=No Patient Found
validate.txtlastName=Please enter the user Last Name
validate.txtfirstName=Please enter the user First Name
validate.txtemail=Please enter the user Email
validate.txtpassword=Please enter the password
validate.txtconfirpassword=Please enter the password
# default
lbl.search=Search
lbl.search.user=Search User
lbl.select=Select
lbl.select.patient=Select Patient
lbl.select.site.unit=Select Site and Unit
lbl.select.site.mrn=Select MRN and Site
lbl.select.visit=Select Visit date
lbl.select.visit.date=Visit date
lbl.mrn=Medical Record Number (MRN)
lbl.site=Site
lbl.unit=Unit
lbl.firstname=First Name
lbl.lastname=Last Name
lbl.patient=Patient
# Additional properties #
# Menu
lang.dashboard=Dashboard
lang.configuration=Administration
lang.templates=Templates
lang.doctypes=Document Types
lang.services=Services
lang.classifications=Classification
lang.categories=Categories
lang.rapports=OWord Reports
lang.consmed=Medical Reconciliation
lang.consmedgen=Search options
lang.conscli=Clinical Reconciliation
lang.conscligen=Search options
lang.support=OWord Support
lang.restore=Change Documents
lang.home=Home
lang.new=New
lang.return=Return
lang.returndashbord=Return to Dashboard
lang.signout=Sign Out
lang.change=Change Language
lang.eng=EN
lang.fr=FR
lang.hi=Hello
lang.checkforms=Launch Bookmark Analysis
lang.exportval=Export Analysis Results
# Dashboard
lang.docprelim=Status: Preliminary
lang.docdraft=Status: Draft
lang.docfinal=Status: Final
lang.docdel=Status: Deleted
lang.docexp=Status: Expired
lang.docpriv=Status: Private
lang.details=More information...
# Reconciliation reports
lang.begin=From
lang.end=To
lang.period=Period
lang.stfin=Final
lang.stpre=Preliminary
lang.stdra=Draft
lang.stdel=Deleted
lang.stexp=Expired
lang.selperiod=Select a period
lang.yesterday=Yesterday
lang.today=Today
lang.last7day=Last 7 days
lang.thismonth=CURRENT Month
lang.lastmonth=LAST Month
lang.nolimit=No date limit
lang.find=Search
lang.reset=Reset
lang.export=Export search results
lang.nodoc=No Document found...
lang.user=OACIS User
lang.category=Category
lang.doctype=Document Type
lang.statrev=Revision status
# Column Tables
lang.cl.idoword=Oword ID
lang.cl.mrn=MRN
lang.cl.site=Site
lang.cl.patientid=Patient ID
lang.cl.visitetype=Visit type
lang.cl.visit=Visit
lang.cl.visitdt=Visite date
lang.cl.doctype=Document type
lang.cl.category=Category
lang.cl.template=Template (FMU)
lang.cl.description=Description
lang.cl.createdt=Created on
lang.cl.createby=Created by
lang.cl.updatedt=Updated on
lang.cl.updateby=Updated by
lang.cl.statrev=Revision status
lang.cl.status=Status
lang.next=Next
lang.previous=Previous
lang.save=Save
lang.cancel=Cancel
lang.totaldocument=Total documents:
lang.support=Support
lang.finddoc=Find Documents
lang.typeIndent=Search Fields
lang.selecttypeIndent=Select a field
lang.docid=OWORD Document ID
lang.mrn=MRN
lang.site=Site
lang.typedate=Date Type 
lang.selecttypedate=Select a type
lang.finaldt=Final on
lang.prelimdt=Preliminary on
lang.creatdt=Created on
lang.datevisit=Admission/Visit Date
lang.docList=Document List
# Categories Colonne
lang.cat.code=Code
lang.cat.nameFr=Category Name (French)
lang.cat.nameEn=Category Name (English)
lang.cat.editcateg=Edit
# Classification Colonne
lang.cla.code=Code
lang.cla.nameFr=Classification Name (French)
lang.cla.nameEn=Classification Name (English)
lang.cla.editclass=Edit
# Service Colonne
lang.ser.code=Code
lang.ser.nameFr=Service Name (French)
lang.ser.nameEn=Service Name (English)
lang.ser.editserv=Edit
lang.ser.newserv=Add new service
# DocType Colonnes
lang.dt.code=FMU
lang.dt.name=Document Type Name
lang.dt.namefr=Document Type Name (French)
lang.dt.nameen=Document Type Name (English)
lang.dt.description=Description
lang.dt.descriptionfr=Document Type Description (French)
lang.dt.descriptionen=Document Type Description (English)
lang.dt.category=Category
lang.dt.classification=Classification
lang.dt.editdoctype=Edit
lang.dt.newdoctype=Add new document type
# Template Colonnes
lang.tp.id=Template ID
lang.tp.doctype=Template (FMU)
lang.tp.alias=Alias
lang.tp.aldr=Draft
lang.tp.alpr=Preliminary
lang.tp.alpt=Printing
lang.tp.alpv=Copy previous content
lang.tp.dt=Created on
lang.tp.ad=Allow Draft
lang.tp.ap=Allow Preliminary
lang.tp.pt=Allow Print
lang.tp.cpd=Copy Previous Content by Default
lang.tp.cpm=Copy Previous Content Mandatory
lang.tp.rv=Reviewable
lang.tp.cp=Allow Copy Previous Content
lang.tp.cpu=Allow Copy Previous Content by User
lang.tp.po=Print Only
lang.tp.pd=Disable Draft Printing
lang.tp.wk=Allow Workgroup
lang.tp.nv=Allow Non-contact Visit
lang.tp.ea=Editable by Archivist
lang.tp.file=UPLOAD TEMPLATE FILE
lang.tp.up=Permission
lang.tp.st=Status
lang.tp.comment=Comments
lang.tp.desc=Description
lang.tp.al=Alias
lang.tp.et=Encounter Type
lang.tp.di=Instances
lang.tp.service=Service
lang.tp.classification=Classification
lang.tp.edittemp=Edit Template
lang.tp.newtemp=New Template
lang.tp.rt=Replace Template
lang.tp.rv=Reviewable
# troisieme partie
lang.connexion=Sign in
lang.password=Password
lang.tp.edit=Edit
lang.tp.download=Download
lang.tp.activate=Activate
lang.tp.desactivate=Desactivate
lang.tp.histories=Versions
lang.tp.histories=Compare Versions
lang.tp.list=Return to Form list
lang.tp.check=Check Template
lang.dc.preliminaryby=Preliminary by
lang.dc.finalby=Final by
lang.dc.finaldate=Final Date
lang.cl.visitnumber=Visit Number
lang.template.versions=Template versions
lang.delete=Delete
lang.public=Revert to Public Status
lang.reactivate=Reactivate document
lang.deactivate=Deactivate document
lang.changestatus=Change Document Status
lang.cl.delon=Deleted on
lang.cl.delby=Deleted by
lang.cl.upon=Updated on
lang.cl.upby=Updated by
lang.cl.resby=Reserved by
# Gestion Application
lang.mngtapp=OWord Tools Management
lang.users=Users
lang.user.firstname=First Name
lang.user.lastname=Last Name
lang.user.email=Email
lang.user.username=Login ID
lang.user.password=Password
lang.user.confirmpassword=Confirm Password
lang.user.role=Role
lang.user.status=Status
lang.user.createdt=Created on
lang.user.roles=Role
lang.user.selrole=Select user role..
lang.user.admin=Administrator
lang.user.supadmin=Super Administrator
lang.user.manager=Manager
lang.user.user=User
lang.user.support=HelpDesk
lang.user.edituser=Create or Edit User
lang.error.adexistemail=Email already registred
lang.error.usernameexists=User Name already registred
# Check Template
lang.bmtitle=Bookmark List
lang.cttitle=Content Control List
lang.bmtitledouble=List of Bookmark used more than once
lang.checktemplate=Bookmark Analysis
lang.validate=Validate
lang.bmname=Bookmark
lang.check=Analyze Bookmarks
lang.ctname=Content Control
lang.ctid=Content Control ID
lang.ctvalue=Content Control Value 
lang.cttype=Content Control Type
lang.bmtype=Bookmark Type
lang.bmvalue=Bookmark Value
lang.result=Results
# Manage activities
lang.activities=Activity Logs
lang.adminactivities=Actions in Administration
lang.reportactivities=Actions in Reports
lang.documentactivities=Actions in Support
lang.act.reportid=Action ID
lang.act.reporttype=Report
lang.act.reportuser=User
lang.act.reportdate=Date
lang.act.reportdesc=Search Options
lang.act.documentactid=Action ID
lang.act.documentaction=Action
lang.act.documentlaststatus=Last Status
lang.act.documentnewstatus=New Status
lang.act.documentid=OWord ID
lang.act.documentmrn=MRN
lang.act.documentsite=Site
lang.act.documentmngtuser=User
lang.act.documentmngtdate=Date
lang.act.documentmngtdesc=Description
lang.act.configid=Action ID
lang.act.configtable=Table
lang.act.configaction=Action
lang.act.configuser=User
lang.act.configdate=Date
lang.act.configelemKey=Element ID
lang.act.configdesc=Description
lang.warning=Warning
lang.dt.exists.message=This document type already exists in the database.
lang.ser.exists.message=This service code already exists in the database.
lang.dt.code=Code
lang.continue=Continue
lang.select.choose=-- Choose --