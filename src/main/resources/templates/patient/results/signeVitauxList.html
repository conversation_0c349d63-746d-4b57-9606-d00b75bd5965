<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org" 
      xmlns:sec="https://www.thymeleaf.org/extras/spring-security" lang="en">   
    <head>
        <title th:text="#{view.index.title}">Welcome!</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- comment -->
        <meta name="_csrf" content="${_csrf.token}" />
        <meta name="_csrf_header" content="${_csrf.headerName}" />
        <meta name="_ajaxAuth" content="Basic dXNlcjE6MTIz" />  <!--YXBpMToxMjM= //dXNlcjE6MTIz-->
        <sec:csrfMetaTags />
  
    </head>
     <body class="skin-blue sidebar-mini">
        <div th:include="fragments/header::head"></div> 
        <div class="wrapper">
            <!-- Header Container -->
            <span th:replace="fragments/layout :: oword-header"></span>
            <!-- Main Sidebar Container -->
            <div th:replace="fragments/layout :: oword-leftSide"></div> 
            <div class="content-wrapper"> 
                <!-- Patient info section -->
                <div th:replace="fragments/layout :: oword-patietn-info"></div> 
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1> <small>Signe Vitaux list</small></h1>
                            </div>
                            <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right">
                                    <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                                    <li class="breadcrumb-item active">Signe Vitaux list</li>
                                </ol>
                            </div>
                        </div>
                    </div><!-- /.container-fluid -->
                </section> 
                <div class="content">
                    <!-- /* Handle the flash message */-->
                    <th:block th:if="${message != null}">
                        <!-- /* The message code is returned from the @Controller */ -->
                        <div th:replace="fragments/components :: alert (type=${#strings.toLowerCase(message.type)}, message=#{${message.message}(${#authentication.name})})">&nbsp;</div>
                    </th:block>
                    <div class="container-fluid"> 
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                     <table id="tblDocTypes" class="table table-bordered table-striped">  
                                         <thead>
                                             <tr>
                                                
                                                <th class="all">Category</th>
                                                <th>Value</th>
                                                <th>Change Date</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                        <tr role="row" th:each="signeVitalResult, rowStat: ${signeVitauxResults}" th:class="${rowStat.odd} ? 'odd' : 'even'">
                                          
                                            <td class="all" th:text="${signeVitalResult.category}">[[${signeVitalResult.category}]]</td>
                                            <td th:text="${signeVitalResult.value}">[[${signeVitalResult.value}]]</td>  
                                            <td class="signeVitalLastChgDtm" th:text="${signeVitalResult.lastChgDtm}">[[${signeVitalResult.lastChgDtm}]]</td>  
                                           
                                        </tr>
                                      </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                          
                
                </div>
            </div>
        </div>

            
        <script>
            const dateList = document.querySelectorAll(".signeVitalLastChgDtm");
            for (let i = 0; i < dateList.length; i++) {
                let date = new Date(dateList[i].innerHTML); 
                if(date != "Invalid Date"){dateList[i].innerHTML = date.toISOString().split('T')[0] + " " + date.toTimeString().split(' ')[0];}
            }  
        </script>  
            
        <!-- jQuery -->
        <script th:src="@{/plugins/jquery/jquery.min.js}"></script>
        <!-- Bootstrap 4 -->
        <script th:src="@{/plugins/bootstrap/js/bootstrap.bundle.min.js}"></script>
        <!-- DataTables  & Plugins -->
        <script th:src="@{/plugins/datatables/jquery.dataTables.min.js}"></script>
        <script th:src="@{/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js}"></script>
        <script th:src="@{/plugins/datatables-responsive/js/dataTables.responsive.min.js}"></script>
        <script th:src="@{/plugins/datatables-responsive/js/responsive.bootstrap4.min.js}"></script>
        <script th:src="@{/plugins/datatables-buttons/js/dataTables.buttons.min.js}"></script>
        <script th:src="@{/plugins/datatables-buttons/js/buttons.bootstrap4.min.js}"></script>
        <script th:src="@{/plugins/jszip/jszip.min.js}"></script>
        <script th:src="@{/plugins/pdfmake/pdfmake.min.js}"></script>
        <script th:src="@{/plugins/pdfmake/vfs_fonts.js}"></script>
        <script th:src="@{/plugins/datatables-buttons/js/buttons.html5.min.js}"></script>
        <script th:src="@{/plugins/datatables-buttons/js/buttons.print.min.js}"></script>
        <script th:src="@{/plugins/datatables-buttons/js/buttons.colVis.min.js}"></script>
        <!-- AdminLTE App -->
        <script th:src="@{/dist/js/adminlte.min.js}"></script>
        <!-- AdminLTE for demo purposes -->
        <script th:src="@{/dist/js/demo.js}"></script>
        <script> 
            $(function () {
                $('#tblDocTypes').DataTable({
                    "responsive": true,
                    'paging': true,
                    'lengthChange': true,
                    'searching': true,
                    'ordering': true,
                    'info': true,
                    'autoWidth': false,
                    'lengthMenu': [ [25, 50, -1], [25, 50, 'All']]
                });
                $('#tblDocTypes tbody').on('click', 'tr', function () {
                    $(this).toggleClass('selected');
                });
                $("#tblDocTypes_length").closest("div.row").addClass("o-tb-top");
                
            });   
 

        </script>

       
        

        <div th:replace="fragments/layout :: footer"></div>
    </body>
</html>