<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" lang="en">
    <head>
       <title th:text="#{view.index.title}">Welcome!</title>
       
       <style>
.dropbtn {
  background-color: #04AA6D;
  color: white;
  padding: 16px;
  font-size: 16px;
  border: none;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #f1f1f1;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 1;
}

.dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {background-color: #ddd;}

.dropdown:hover .dropdown-content {display: block;}

.dropdown:hover .dropbtn {background-color: #3e8e41;}
</style>
    </head>
    <body>
        <!-- Header -->
        <header class="main-header" th:fragment="header"> 
            <!-- Logo --> 
            <a th:href="@{/}"  class="logo oword-logo oword-mobile-display-none">
                 <img th:src="@{/images/logo_cusm_32X32.png}" style="width:33px; height:33px; opacity: .8;" class="logo-mini" >
                 <span class="logo-lg"><b>e</b>Forms</span> 
            </a>
            <!-- Header Navbar: style can be found in header.less -->
            <nav class="navbar navbar-static-top">
                <!-- Sidebar toggle button-->
                <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </a>
                <div class="navbar-custom-menu">
                    <ul class="nav navbar-nav" > 
                        <li class="dropdown messages-menu oword-display-none"> 
                            <a href="#">                              
                                <div style="width:300px">
                                    <!-- search form -->
                                    <form action="#" method="get" class="form-control-navbar">
                                        <div class="input-group">
                                            <input type="text" name="q" class="form-control" placeholder="Search...">
                                            <span class="input-group-btn">
                                                <button type="submit" name="search" id="search-btn" class="btn btn-flat">
                                                    <i class="fa fa-search"></i>
                                                </button>
                                            </span>
                                        </div>
                                        
                                    </form>
                                </div>
                            </a>
                        </li>
                        <!-- /.search form -->

                        <!-- Messages: style can be found in dropdown.less-->
                        <li class="dropdown messages-menu oword-display-none">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-envelope-o"></i>
                                <span class="label label-success">4</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li class="header">You have 4 messages</li>
                                <li>
                                    <!-- inner menu: contains the actual data -->
                                    <ul class="menu">
                                        <li><!-- start message -->
                                            <a href="#">
                                                <div class="pull-left">
                                                    
                                                </div>
                                                <h4>
                                                    Support Team
                                                    <small><i class="fa fa-clock-o"></i> 5 mins</small>
                                                </h4>
                                                <p>Why not buy a new awesome theme?</p>
                                            </a>
                                        </li>
                                        <!-- end message -->
                                        <li>
                                            <a href="#">
                                                <div class="pull-left">
                                                   
                                                </div>
                                                <h4>
                                                    AdminLTE Design Team
                                                    <small><i class="fa fa-clock-o"></i> 2 hours</small>
                                                </h4>
                                                <p>Why not buy a new awesome theme?</p>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <div class="pull-left">
                                                    
                                                </div>
                                                <h4>
                                                    Developers
                                                    <small><i class="fa fa-clock-o"></i> Today</small>
                                                </h4>
                                                <p>Why not buy a new awesome theme?</p>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <div class="pull-left">
                                                    
                                                </div>
                                                <h4>
                                                    Sales Department
                                                    <small><i class="fa fa-clock-o"></i> Yesterday</small>
                                                </h4>
                                                <p>Why not buy a new awesome theme?</p>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <div class="pull-left">
                                                   
                                                </div>
                                                <h4>
                                                    Reviewers
                                                    <small><i class="fa fa-clock-o"></i> 2 days</small>
                                                </h4>
                                                <p>Why not buy a new awesome theme?</p>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="footer"><a href="#">See All Messages</a></li>
                            </ul>
                        </li>
                        <!-- Notifications: style can be found in dropdown.less -->
                        <li class="dropdown notifications-menu oword-display-none">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-bell-o"></i>
                                <span class="label label-warning">10</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li class="header">You have 10 notifications</li>
                                <li>
                                    <!-- inner menu: contains the actual data -->
                                    <ul class="menu">
                                        <li>
                                            <a href="#">
                                                <i class="fa fa-users text-aqua"></i> 5 new members joined today
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fa fa-warning text-yellow"></i> Very long description here that may not fit into the
                                                page and may cause design problems
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fa fa-users text-red"></i> 5 new members joined
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fa fa-shopping-cart text-green"></i> 25 sales made
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#">
                                                <i class="fa fa-user text-light-blue"></i> You changed your username
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="footer"><a href="#">View all</a></li>
                            </ul>
                        </li>
                        <!-- Tasks: style can be found in dropdown.less -->
                        <li class="dropdown tasks-menu oword-display-none">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-flag-o"></i>
                                <span class="label label-danger">9</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li class="header">You have 9 tasks</li>
                                <li>
                                    <!-- inner menu: contains the actual data -->
                                    <ul class="menu">
                                        <li><!-- Task item -->
                                            <a href="#">
                                                <h3>
                                                    Design some buttons
                                                    <small class="pull-right">20%</small>
                                                </h3>
                                                <div class="progress xs">
                                                    <div class="progress-bar progress-bar-aqua" style="width: 20%" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="sr-only">20% Complete</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        <!-- end task item -->
                                        <li><!-- Task item -->
                                            <a href="#">
                                                <h3>
                                                    Create a nice theme
                                                    <small class="pull-right">40%</small>
                                                </h3>
                                                <div class="progress xs">
                                                    <div class="progress-bar progress-bar-green" style="width: 40%" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="sr-only">40% Complete</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        <!-- end task item -->
                                        <li><!-- Task item -->
                                            <a href="#">
                                                <h3>
                                                    Some task I need to do
                                                    <small class="pull-right">60%</small>
                                                </h3>
                                                <div class="progress xs">
                                                    <div class="progress-bar progress-bar-red" style="width: 60%" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="sr-only">60% Complete</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        <!-- end task item -->
                                        <li><!-- Task item -->
                                            <a href="#">
                                                <h3>
                                                    Make beautiful transitions
                                                    <small class="pull-right">80%</small>
                                                </h3>
                                                <div class="progress xs">
                                                    <div class="progress-bar progress-bar-yellow" style="width: 80%" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">
                                                        <span class="sr-only">80% Complete</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        <!-- end task item -->
                                    </ul>
                                </li>
                                <li class="footer">
                                    <a href="#">View all tasks</a>
                                </li>
                            </ul>
                        </li>

                        <!-- Control Sidebar Toggle Button -->
                        <li class="oword-display-none">
                            <a href="#" data-toggle="control-sidebar"><i class="fa fa-gears"></i></a>
                        </li>
                                                
                        <li class="dropdown user user-menu">    
                                <span th:text="${#authentication.name}"></span>
                            </a>
                            <ul class="dropdown-menu" style="background: #44444400;right: 0px !important; border: 0;">
                                
                                <!-- Menu Footer-->
                                <li class="user-footer" style="background: #44444400;  padding:0px;">
                                   
                                    <div class="pull-right" >
                                        <a th:href="@{/custom-logout}" class="btn btn-default btn-flat" style="font-size:12px;">
                                            <i class="fa fa-sign-out"></i><span data-th-text="#{lang.signout}">Sign out </span>
                                        </a>
                                    </div>
                                </li>
                            </ul>
                        </li>
                        
                    </ul>
                </div>
            </nav>
        </header>

        <!-- Header 2 -->
        <div class="main-header" th:fragment="header2">
            <!-- Logo -->
            
            <a th:href="@{/}"  class="logo oword-logo ">
                <!-- mini logo for sidebar mini 50x50 pixels -->
                <span class="logo-mini">owordTools</span>
                <!-- logo for regular state and mobile devices -->
                <span class="logo-lg">owordTools</span>
            </a>
            <!-- Header Navbar: style can be found in header.less -->
            <nav class="navbar navbar-static-top oword-mobile-display-none">
                <div class="navbar-custom-menu"></div>
            </nav>
        </div>
        
        <!--------------------------->
        <!-- TOP navigation Navbar oword-header-->
        <!--------------------------->
        <div th:fragment="oword-header">        
           <!-- Preloader -->
            <div class="preloader flex-column justify-content-center align-items-center">
                <img class="animation__shake" th:src="@{/images/logo_cusm_32X32.png}" alt="owordToolsLogo" height="60" width="60">
            </div>    
            <nav class="main-header navbar navbar-expand navbar-white navbar-light">
                <!-- Left navbar links -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                    </li>
                    <li class="nav-item d-none d-sm-inline-block">
                        <a th:href="@{/}" class="nav-link" data-th-text="#{lang.returndashbord}">Home</a>
                    </li> 
                    <li class="nav-item d-none d-sm-inline-block">
                        <a th:href="@{/}" class="nav-link" th:text="#{lang.hi} + ' ' + ${#authentication.name}">Name</a>
                    </li> 
                </ul>

                <!-- Development Environment Banner (Centered) -->
                <div th:if="${@environment.getActiveProfiles()[0] == 'dev'}" class="navbar-nav mx-auto">
                    <span class="badge badge-warning" style="font-size: 14px; padding: 8px 15px; margin-top: 5px;">
                        DEVELOPMENT ENVIRONMENT
                    </span>
                </div>

                <!-- Right navbar links  -->
                <ul class="navbar-nav ml-auto">
                    <li>
                        <a th:href="@{/custom-logout}" class="dropdown-item">
                            <span data-th-text="#{lang.signout}">Sign out</span>
                        </a>
                    </li>

                    <li>
                        <a href="#" class="dropdown-item" onclick="getlink('fr')" id="fr" th:text="#{lang.fr}"></a>
                    </li>
                    <li>
                         <a href="#" class="dropdown-item" onclick="getlink('en')" id="en" th:text="#{lang.eng}"></a>
                    </li>
                </ul>
            </nav>
            <!-- /.navbar -->
            <!--------------------------->
        </div>
          
        <!--------------------------->
        <!-- TOP navigation Navbar oword-header - error -->
        <!--------------------------->
        <div th:fragment="oword-header-error">        
           <!-- Preloader -->
            <div class="preloader flex-column justify-content-center align-items-center">
                <img class="animation__shake" th:src="@{/images/logo_cusm_32X32.png}" alt="AdminLTELogo" height="60" width="60">
            </div>    
            <nav class="main-header navbar navbar-expand navbar-white navbar-light">
                <!-- Left navbar links -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                    </li>
                    <li class="nav-item d-none d-sm-inline-block">
                        <a th:href="@{/}" class="nav-link" data-th-text="#{lang.home}">Home</a>
                    </li> 
                </ul>

                <!-- Right navbar links  -->
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
 
                        <ul class="dropdown-menu" style="background: #44444400; right: 0px !important; border: 0; padding-right: 32px;  text-align: right;  box-shadow: rgb(0 0 0 / 0%) 0px 0.5rem 1rem;   width: 100%; padding-top: 0px;">
                            <li class="user-footer" style="background: #44444400;  padding:0px;"> 
                                <div class="pull-right" >
                                    <a th:href="@{/custom-logout}" class="btn btn-default btn-flat" style="font-size:12px;">
                                        <i class="fa fa-sign-out"></i><span data-th-text="#{lang.signout}">Sign out</span>
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </li> 
                </ul>
            </nav>
             <!--------------------------->
        </div>
         
       
        <!-- oword-leftSide -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4" th:fragment="oword-leftSide" style="min-height: 100vh;">
            <!-- Brand Logo -->
            <a  th:href="@{/}"  class="brand-link">
                <img th:src="@{/images/logo_cusm_32X32.png}"  alt="Oword" class="brand-image img-circle elevation-3" style="opacity: .8">
                <span th:classappend="${module == 'dashboardAdmin' ? 'active' : ''}" sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN', 'ROLE_USER', 'ROLE_SUPPORT')">
                    <span class="brand-text font-weight-light"><b>O</b>Word Tools</span>
                </span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <br> 
                <!-- SidebarSearch Form -->
                <div class="form-inline">
                    <div class="input-group" data-widget="sidebar-search">
                        <input class="form-control form-control-sidebar" type="search" placeholder="Search" aria-label="Search">
                        <div class="input-group-append">
                            <button class="btn btn-sidebar">
                                <i class="fas fa-search fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column sidebar-menu tree" data-widget="treeview" role="menu" data-accordion="false">
                        <!------------------------>
                        <!--  Dashboard admin   --> 
                        <!------------------------>
                         <li class="nav-item" th:classappend="${module == 'dashboardAdmin' ? 'active' : ''}" sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')"> 
                            <a th:href="@{/dashboardAdmin}" class="nav-link" >
                                <i class="nav-icon fas fa-tachometer-alt"></i><p data-th-text="#{lang.dashboard}" ></p>
                            </a>
                           
                        </li>  
                        <!-- administration -->
                        <li class="nav-item" 
                            th:classappend="${module == 'doctypes' || module == 'categories' || module == 'classifications' || module == 'services-code' || module == 'templates' || module == 'checktemplate' ? 'active menu-is-opening menu-open' : ''}" 
                            sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')">
                            <a href="#" class="nav-link ">
                                <i class="nav-icon fas fa-cog"></i>
                                <p data-th-text="#{lang.configuration}"></p> <i class="right fas fa-angle-left"></i>
                            </a>
                            <ul class="nav nav-treeview">
                                
                                <li class="nav-item">
                                    <a th:href="@{/categories}" class="nav-link" th:classappend="${module == 'categories' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.category}"></p>
                                    </a>
                                </li>
                                <li class="nav-item"
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')">
                                    <a th:href="@{/classifications}" class="nav-link" th:classappend="${module == 'classifications' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.classifications}"></p>
                                    </a>
                                </li>
                                <li class="nav-item"
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')">
                                    <a th:href="@{/services-code}" class="nav-link" th:classappend="${module == 'services-code' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.services}"></p>
                                    </a>
                               </li>
                               <li class="nav-item" 
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')">
                                    <a th:href="@{/doctypes}" class="nav-link" th:classappend="${module == 'doctypes' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.doctypes}"></p>
                                    </a>
                                </li>
                                <li class="nav-item"
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')">
                                    <a th:href="@{/templates}" class="nav-link" th:classappend="${module == 'templates' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.templates}"></p>
                                    </a>
                                </li>
                                <li class="nav-item"
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')">
                                    <a th:href="@{/checktemplate}" class="nav-link" th:classappend="${module == 'checktemplate' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.checktemplate}"></p>
                                    </a>
                                </li>  

                                
                            </ul>
                        </li> 
                        
                        <!-- reports -->
                        <li class="nav-item"  
                            th:classappend="${module == 'rapportmedical' || module == 'rapportclinique' ? 'active menu-is-opening menu-open' : ''}" 
                            sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN', 'ROLE_USER', 'ROLE_SUPPORT')">
                            <a href="#" class="nav-link ">
                                <i class="nav-icon fa fa-book fa-fw"></i>
                                <p data-th-text="#{lang.rapports}"> </p><i class="right fas fa-angle-left"></i>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item" 
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN', 'ROLE_USER', 'ROLE_SUPPORT')">
                                    <a th:href="@{/rapconcli}" class="nav-link" th:classappend="${module == 'rapportclinique' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.conscli}"></p>
                                    </a>
                                </li>
                                <li class="nav-item"
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN', 'ROLE_USER', 'ROLE_SUPPORT')">
                                    <a th:href="@{/rapconmed}" class="nav-link" th:classappend="${module == 'rapportmedical' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.consmed}"></p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        
                        <li class="nav-item" 
                            th:classappend="${module == 'restauredocument' || module == 'finddocument' ? 'active menu-is-opening menu-open' : ''}" 
                            sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_SUPPORT')"  th:if="${envToggle == 'dev'}">
                            
                            
                            <a href="#" class="nav-link " sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_SUPPORT')">
                                <i class="nav-icon fas fa-support"></i>
                                <p data-th-text="#{lang.support}"></p> <i class="right fas fa-angle-left"></i>
                            </a>
                            <ul class="nav nav-treeview">

                                <li class="nav-item" 
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_SUPPORT')"  >
                                    <a th:href="@{/finddocforrestaure}" class="nav-link" th:classappend="${module == 'finddocument' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.finddoc}" ></p>
                                    </a>
                                </li>
                                
                                <li class="nav-item" 
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_SUPPORT')"  >
                                    <a th:href="@{/restoredocuments}" class="nav-link" th:classappend="${module == 'restauredocument' ? 'active' : ''}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p data-th-text="#{lang.restore}" ></p>
                                    </a>
                                </li>

                            </ul>
                        </li>
                        
                        <li class="nav-item" 
                            th:classappend="${module == 'reportactivities' || module == 'adminactivities' || module == 'documentactivities' ? 'active menu-is-opening menu-open' : ''}" 
                            sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN', 'ROLE_SUPPORT')">
                            <a href="#" class="nav-link ">
                                <i class="nav-icon fas fa-tasks"></i>
                                <p data-th-text="#{lang.activities}">Activities </p> <i class="right fas fa-angle-left"></i>
                            </a>
                            <ul class="nav nav-treeview"> 
                                <li class="nav-item" 
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')"  >
                                    <a th:href="@{/configactivities}" class="nav-link" th:classappend="${module == 'adminactivities' ? 'active' : ''}">
                                        <i class="fa fa-cog nav-icon"></i>
                                        <p data-th-text="#{lang.adminactivities}" ></p>
                                    </a>
                                </li>
                                <li class="nav-item" 
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_SUPPORT')"  >
                                    <a th:href="@{/reportactivities}" class="nav-link" th:classappend="${module == 'reportactivities' ? 'active' : ''}">
                                        <i class="fa fa-file nav-icon"></i>
                                        <p data-th-text="#{lang.reportactivities}" ></p>
                                    </a>
                                </li>
                                <li class="nav-item" 
                                    sec:authorize="hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_SUPPORT')"  >
                                    <a th:href="@{/documentactivities}" class="nav-link" th:classappend="${module == 'documentactivities' ? 'active' : ''}"
                                       th:if="${envToggle == 'dev'}">
                                        <i class="fab fa-wpforms nav-icon"></i>
                                        <p data-th-text="#{lang.documentactivities}" ></p>
                                    </a>
                                </li>
                            </ul>
                        </li> 
                        <li class="nav-item" 
                            th:classappend="${module == 'accounts' ? 'active menu-is-opening menu-open' : ''}" 
                            sec:authorize="hasAuthority('ROLE_SUP_ADMIN')">
                            <a href="#" class="nav-link ">
                                <i class="nav-icon fas fa-support"></i>
                                <p data-th-text="#{lang.mngtapp}"></p> <i class="right fas fa-angle-left"></i>
                            </a>
                            <ul class="nav nav-treeview"> 
                                <li class="nav-item" 
                                    sec:authorize="hasAuthority('ROLE_SUP_ADMIN')"  >
                                    <a th:href="@{/accounts}" class="nav-link" th:classappend="${module == 'accounts' ? 'active' : ''}">
                                        <i class="far fa-user nav-icon"></i>
                                        <p data-th-text="#{lang.users}" ></p>
                                    </a>
                                </li>
                            </ul>
                        </li> 
                        
                        <script th:src="@{/js/oword.js}"></script>

                    </ul>
                </nav>
              <!-- /.sidebar-menu -->
            </div>
            <!-- /.sidebar -->
        </aside>

        <!-- right Side -->
        <aside class="control-sidebar control-sidebar-dark" th:fragment="rightSide" style="position: fixed; bottom: 0px; width: 100%;">
            <!-- Create the tabs -->
            <ul class="nav nav-tabs nav-justified control-sidebar-tabs">
                <li class="active">
                    <a href="#control-sidebar-theme-demo-options-tab" data-toggle="tab">
                        <i class="fa fa-wrench"></i></a>
                </li>
                <li><a href="#control-sidebar-home-tab" data-toggle="tab"><i class="fa fa-home"></i></a></li>
                <li><a href="#control-sidebar-settings-tab" data-toggle="tab"><i class="fa fa-gears"></i></a></li>
            </ul>
        </aside>    

        <!-- Footer -->
        <footer class="main-footer" th:fragment="footer">
            <div class="pull-right hidden-xs">
                <b>Version</b> <span th:text="${@environment.getProperty('app.version')}"></span>
            </div>
            <strong>OWord Tools.</strong>
        </footer>
        
        <script th:inline="javascript" type="text/javascript">

            $("document").ready(function () {
                    $("#lang_switcher").change(function () {
                    var selectedOption = $('#lang_switcher').val();
                    if (selectedOption !== ''){
                        var currentUrl = window.location.pathname;
                        var newUrl = currentUrl + (currentUrl.indexOf('?') === -1 ? '?' : '&') + 'lang=' + selectedOption;
                         window.location.replace(newUrl);
                    }
                });
            });
        </script>

        
    </body>
</html>
