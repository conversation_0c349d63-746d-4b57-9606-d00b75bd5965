<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0" />
  <title>BezKoder - Spring Boot Thymeleaf Pagination example</title>

  <link rel="stylesheet" type="text/css" th:href="@{/webjars/bootstrap/css/bootstrap.min.css}" />
  <link rel="stylesheet" type="text/css" th:href="@{/css/style.css}" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"
    integrity="sha512-xh6O/CkQoPOWDdYTDqeRdPCVd1SpvCA9XXcUnZS2FmJNp1coAFzvtCN9BmamE+4aHK8yyUHUSCcJHgXloTyT2A=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script type="text/javascript" th:src="@{/webjars/jquery/jquery.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/bootstrap/js/bootstrap.min.js}"></script>
</head>

<body>
  <div th:replace="fragments/header :: header"></div>

  <a th:fragment="paging(pageNum, label, tooltip)" class="page-link"
  th:href="@{'/report/subrecmed?' + ${beginDate!=null && beginDate!=''? 'beginDate=' + beginDate + '&' : ''} + 
  ${beginDate!=null && beginDate!=''? 'beginDate=' + beginDate + '&' : ''} + 
  ${endDate!=null && endDate!=''? 'endDate=' + endDate + '&' : ''} + 
  ${dateInterval!=null && dateInterval!=''? 'dateInterval=' + dateInterval + '&' : ''} +
  ${isFin!=null && isFin!=''? 'isFin=' + isFin + '&' : ''} + ${isPre!=null && isPre!=''? 'isPre=' + isPre + '&' : ''} + 
  ${isDra!=null && isDra!=''? 'isDra=' + isDra + '&' : ''} + ${isDel!=null && isDel!=''? 'isDel=' + isDel + '&' : ''} +
  ${isExp!=null && isExp!=''? 'isExp=' + isExp + '&' : ''} + ${isScan!=null && isScan!=''? 'isScan=' + isScan + '&' : ''} +
  'page=' + ${pageNum} + '&size=' + ${pageSize}}"
  th:title="${tooltip}" rel="tooltip">
  [[${label}]]
    </a>

  <div th:replace="fragments/footer :: footer"></div>
</body>

</html>