<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org"
      lang="en">
<head>
    <title th:text="#{view.index.title}">Welcome!</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta th:name="_csrf" th:content="${_csrf.token}"/>
    <meta th:name="_csrf_header" th:content="${_csrf.headerName}"/>
</head>

<body class="skin-blue sidebar-mini">
<div th:include="fragments/header::head"></div>
<div class="wrapper">
    <!-- Header Container -->
    <span th:replace="~{fragments/layout :: oword-header}"></span>
    <!-- Main Sidebar Container -->
    <span th:replace="~{fragments/layout :: oword-leftSide}"></span>
    <div class="content-wrapper">
        <!-- Patient info section -->
        <span th:replace="~{fragments/layout :: oword-patietn-info}"></span>
        <section class="content-header">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-6">
                        <h1><small>Documents</small></h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                            <li class="breadcrumb-item active">Documents</li>
                        </ol>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>
        <section class="content">
            <!-- /* Handle the flash message */-->
            <th:block th:if="${message != null}">
                <!-- /* The message code is returned from the @Controller */ -->
                <div th:replace="~{fragments/components :: alert (type=${#strings.toLowerCase(message.type)}, message=#{${message.message}(${#authentication.name})})}">
                    &nbsp;
                </div>
            </th:block>
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card card-primary card-outline card-outline-tabs">
                            <div class="card-header p-0 border-bottom-0">
                                <ul class="nav nav-tabs" id="custom-tabs-four-tab" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="tabFavories-tab" data-toggle="pill"
                                           href="#tabFavories" role="tab" aria-controls="tabFavories"
                                           aria-selected="true">Favories</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tabTemplates-tab" data-toggle="pill"
                                           href="#tabTemplates" role="tab" aria-controls="tabTemplates"
                                           aria-selected="false">Templates</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tabDraftDocuments-tab" data-toggle="pill"
                                           href="#tabDraftDocuments" role="tab" aria-controls="tabDraftDocuments"
                                           aria-selected="false">Draft documents</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tabPreliminaryDocuments-tab" data-toggle="pill"
                                           href="#tabPreliminaryDocuments" role="tab"
                                           aria-controls="tabPreliminaryDocuments" aria-selected="false">Preliminary
                                            documents</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content" id="custom-tabs-four-tabContent">
                                    <div class="tab-pane fade show active" id="tabFavories" role="tabpanel"
                                         aria-labelledby="tabFavories-tab">
                                        <table id="tblFavories" class="table table-bordered table-striped">
                                            <thead>
                                            <tr>
                                                <th class="all">DocType</th>
                                                <th>Name</th>
                                                <th>Classif.</th>
                                                <th class="no-sort all">&nbsp;</th>
                                                <th class="no-sort all">&nbsp;</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr role="row" th:each="favory, rowStat: ${favories}"
                                                th:class="${rowStat.odd} ? 'odd' : 'even'">
                                                <td class="all" th:text="${favory.template.docType.id}">doctype.id</td>
                                                <td th:text="${favory.template.docType.nameEn}">doctype.nameEn</td>
                                                <td th:text="${favory.template.classification.code}">
                                                    classification.code
                                                </td>
                                                <td class="all">
                                                    <a th:href="@{/document/new(id=${favory.template.id})}"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fa fa-check"></i><span
                                                            class="o-btn-lable"> Select </span>
                                                    </a>
                                                </td>
                                                <td class="all">
                                                    <a th:href="@{/document/removefavory(id=${favory.template.id})}"
                                                       class="btn btn-sm btn-default">
                                                        <i class="fa fa-minus"></i><span class="o-btn-lable"> Remove from Favories </span>
                                                    </a>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="tab-pane fade" id="tabTemplates" role="tabpanel"
                                         aria-labelledby="tabTemplates-tab">
                                        <table id="tblTemplates" class="table table-bordered table-striped">
                                            <thead>
                                            <tr>
                                                <th class="all"> DocType</th>
                                                <th>Name</th>
                                                <th>Classif.</th>
                                                <th class="no-sort all">&nbsp;</th>
                                                <th class="no-sort all">&nbsp;</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr role="row" th:each="template, rowStat: ${templates}"
                                                th:class="${rowStat.odd} ? 'odd' : 'even'">
                                                <td class="all" th:text="${template.docType.id}">doctype.id</td>
                                                <td th:text="${template.docType.nameEn}">doctype.nameEn</td>
                                                <td th:text="${template.classification.code}">classification.code</td>
                                                <td class="all">
                                                    <a th:href="@{/document/new(id=${template.id})}"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fa fa-check"></i><span
                                                            class="o-btn-lable"> Select </span>
                                                    </a>
                                                </td>
                                                <td class="all">
                                                    <a th:href="@{/document/addfavory(id=${template.id})}"
                                                       class="btn btn-sm btn-default">
                                                        <i class="fa fa-plus"></i><span class="o-btn-lable"> Add To Favories </span>
                                                    </a>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="tab-pane fade" id="tabDraftDocuments" role="tabpanel"
                                         aria-labelledby="tabDraftDocuments-tab">
                                        <div class="tab-pane box-body" id="tabDraftDocuments">
                                            <table id="tblDraftDocuments" class="table table-bordered table-striped">
                                                <thead>
                                                <tr>
                                                    <th class="all">MRN</th>
                                                    <th>Facility</th>
                                                    <th>Num Visit</th>
                                                    <th>Visit Date</th>
                                                    <th>Template ID</th>
                                                    <th>Status</th>
                                                    <th>Updated on</th>
                                                    <th>Update by</th>
                                                    <th class="no-sort all">&nbsp;</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr role="row" th:each="document, rowStat: ${draftDocuments}"
                                                    th:class="${rowStat.odd} ? 'odd' : 'even'">
                                                    <td class="all" th:text="${document.mrn}">patient.mrn</td>
                                                    <td th:text="${document.facility.code}">facility.code</td>
                                                    <td th:text="${document.visitId}">visitId</td>
                                                    <td class="documentUpdatedOn" th:text="${document.admitDate}">Visit
                                                        Date
                                                    </td>
                                                    <td th:text="${document.template.id}">template.id</td>
                                                    <td th:text="${document.status}">status</td>
                                                    <td class="documentUpdatedOn" th:text="${document.updatedOn}">
                                                        updateOn
                                                    </td>
                                                    <td class="all" th:text="${document.updatedBy}">updateBy</td>
                                                    <td>
                                                        <a th:href="@{/document/edit(id=${document.id})}"
                                                           class="btn btn-sm btn-primary">
                                                            <i class="fa fa-edit"></i><span class="o-btn-lable"> Edit content </span>
                                                        </a>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="tabPreliminaryDocuments" role="tabpanel"
                                         aria-labelledby="tabPreliminaryDocuments-tab">
                                        <div class="tab-pane box-body" id="tabPreliminaryDocuments">
                                            <table id="tblPreliminaryDocuments"
                                                   class="table table-bordered table-striped">
                                                <thead>
                                                <tr>
                                                    <th class="all">MRN</th>
                                                    <th>Facility</th>
                                                    <th>Num Visit</th>
                                                    <th>Visit Date</th>
                                                    <th>Template ID</th>
                                                    <th>Status</th>
                                                    <th>Updated on</th>
                                                    <th>Update by</th>
                                                    <th class="no-sort all">&nbsp;</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr role="row" th:each="document, rowStat: ${preliminaryDocuments}"
                                                    th:class="${rowStat.odd} ? 'odd' : 'even'">
                                                    <td class="all" th:text="${document.mrn}">patient.mrn</td>
                                                    <td th:text="${document.facility.code}">facility.code</td>
                                                    <td th:text="${document.visitId}">Num Visit</td>
                                                    <td class="documentUpdatedOn" th:text="${document.admitDate}">Visit
                                                        Date
                                                    </td>
                                                    <td th:text="${document.template.id}">template.id</td>
                                                    <td th:text="${document.status}">status</td>
                                                    <td class="documentUpdatedOn" th:text="${document.updatedOn}">
                                                        updateOn
                                                    </td>
                                                    <td class="all" th:text="${document.updatedBy}">updateBy</td>
                                                    <td><a th:href="@{/document/edit(id=${document.id})}"
                                                           class="btn btn-sm btn-primary">
                                                        <i class="fa fa-edit"></i><span class="o-btn-lable"> Edit content </span>
                                                    </a>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- /.card -->
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div><!-- / content wrapper -->
    <span th:replace="fragments/layout :: footer"></span>
</div>
<script>
    const dateList = document.querySelectorAll(".documentUpdatedOn");
    for (let i = 0; i < dateList.length; i++) {
        let date = new Date(dateList[i].innerHTML);
        if (date != "Invalid Date") {
            dateList[i].innerHTML = date.toISOString().split('T')[0] + " " + date.toTimeString().split(' ')[0];
        }
    }
</script>

<!-- jQuery -->
<script th:src="@{/plugins/jquery/jquery.min.js}"></script>
<!-- Bootstrap 4 -->
<script th:src="@{/plugins/bootstrap/js/bootstrap.bundle.min.js}"></script>
<!-- DataTables  & Plugins -->
<script th:src="@{/plugins/datatables/jquery.dataTables.min.js}"></script>
<script th:src="@{/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js}"></script>
<script th:src="@{/plugins/datatables-responsive/js/dataTables.responsive.min.js}"></script>
<script th:src="@{/plugins/datatables-responsive/js/responsive.bootstrap4.min.js}"></script>
<script th:src="@{/plugins/datatables-buttons/js/dataTables.buttons.min.js}"></script>
<script th:src="@{/plugins/datatables-buttons/js/buttons.bootstrap4.min.js}"></script>
<script th:src="@{/plugins/jszip/jszip.min.js}"></script>
<script th:src="@{/plugins/pdfmake/pdfmake.min.js}"></script>
<script th:src="@{/plugins/pdfmake/vfs_fonts.js}"></script>
<script th:src="@{/plugins/datatables-buttons/js/buttons.html5.min.js}"></script>
<script th:src="@{/plugins/datatables-buttons/js/buttons.print.min.js}"></script>
<script th:src="@{/plugins/datatables-buttons/js/buttons.colVis.min.js}"></script>
<!-- AdminLTE App -->
<script th:src="@{/dist/js/adminlte.min.js}"></script>
<!-- AdminLTE for demo purposes -->
<script th:src="@{/dist/js/demo.js}"></script>

<script>
    $(function () {
        $('#tblFavories').DataTable({
            "responsive": true,
            'paging': true,
            'lengthChange': true,
            'searching': true,
            'ordering': true,
            'info': true,
            'autoWidth': false,
            'lengthMenu': [[25, 50, -1], [25, 50, 'All']]
        });
        $('#tblFavories tbody').on('click', 'tr', function () {
            $(this).toggleClass('selected');
        });
        $("#tblFavories_length").closest("div.row").addClass("o-tb-top");
    });

    $(function () {
        $('#tblTemplates').DataTable({
            'responsive': true,
            'paging': true,
            'lengthChange': true,
            'searching': true,
            'ordering': true,
            'info': true,
            'autoWidth': false,
            "pageLength": 10,
            'lengthMenu': [[25, 50, -1], [25, 50, 'All']]
        });
        $('#tblTemplates tbody').on('click', 'tr', function () {
            $(this).toggleClass('selected');
        });

        $("#tblTemplates_length").closest("div.row").addClass("o-tb-top");
    });


    $(function () {
        $('#tblDraftDocuments').DataTable({
            "responsive": true,
            'paging': true,
            'lengthChange': true,
            'searching': true,
            'ordering': true,
            'info': true,
            'autoWidth': false,
            'lengthMenu': [[25, 50, -1], [25, 50, 'All']]
        });
        $('#tblDraftDocuments tbody').on('click', 'tr', function () {
            $(this).toggleClass('selected');
        });
        // oword
        $("#tblDocuments_length").closest("div.row").addClass("o-tb-top");
        /*
        $(".tab_templates").on('click', function () {
            $("#tabTemplates").show();
        });
        */
    });

    $(function () {
        $('#tblPreliminaryDocuments').DataTable({
            "responsive": true,
            'paging': true,
            'lengthChange': true,
            'searching': true,
            'ordering': true,
            'info': true,
            'autoWidth': false,
            'lengthMenu': [[25, 50, -1], [25, 50, 'All']]
        });
        $('#tblPreliminaryDocuments tbody').on('click', 'tr', function () {
            $(this).toggleClass('selected');
        });
        // oword
        $("#tblDocuments_length").closest("div.row").addClass("o-tb-top");
        /*
        $(".tab_templates").on('click', function () {
            $("#tabTemplates").show();
        });
        */
    });

    $(document).ready(function () {
        $("#tabTemplates-tab").on('click', function () {
            $("#tabFavories").hide();
            $("#tabTemplates").show();
            $("#tabDraftDocuments").hide();
            $("#tabPreliminaryDocuments").hide();
            $('#tblTemplates').DataTable().responsive.recalc();
        });
        $("#tabDraftDocuments-tab").on('click', function () {
            $("#tabFavories").hide();
            $("#tabTemplates").hide();
            $("#tabDraftDocuments").show();
            $("#tabPreliminaryDocuments").hide();
            $('#tblDraftDocuments').DataTable().responsive.recalc();
        });
        $("#tabPreliminaryDocuments-tab").on('click', function () {
            $("#tabFavories").hide();
            $("#tabTemplates").hide();
            $("#tabDraftDocuments").hide();
            $("#tabPreliminaryDocuments").show();
            $('#tblPreliminaryDocuments').DataTable().responsive.recalc();
        });
        $("#tabFavories-tab").on('click', function () {
            $("#tabFavories").show();
            $("#tabTemplates").hide();
            $("#tabDraftDocuments").hide();
            $("#tabPreliminaryDocuments").hide();
            $('#tblFavories').DataTable().responsive.recalc();
        });
    });

</script>
</body>
</html>