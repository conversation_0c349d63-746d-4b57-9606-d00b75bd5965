<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org" 
      xmlns:sec="https://www.thymeleaf.org/extras/spring-security" lang="en">   
    <head>
        <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1"/> 
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
            <meta name="viewport" content="width=device-width, initial-scale=1.0"/> 
            <title th:text="#{view.index.title}">Welcome!</title> 
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
    </head>
                <body class="skin-blue sidebar-mini">
                    <div th:include="fragments/header::head"></div>
                    <div class="wrapper"> 
                        <!-- Header Container -->
                        <span th:replace="~{fragments/layout :: oword-header}"></span>
                        <!-- Main Sidebar Container -->
                        <div th:replace="~{fragments/layout :: oword-leftSide}"></div>

                        <!-- Content Wrapper. Contains page content -->
                        <div class="content-wrapper">
                            <!-- Content Header (Page header) -->
                            <div class="content-header">
                                <div class="container-fluid">
                                    <div class="row mb-2">
                                        <div class="col-sm-6"> 
                                            <h1 data-th-text="#{lang.dashboard}"> <small></small></h1>
                                        </div><!-- /.col -->

                                    </div><!-- /.row -->
                                </div><!-- /.container-fluid -->
                            </div>
                            <!-- /.content-header -->

                            <!-- Main content -->
                            <section class="content">
                                <div class="container-fluid">
                                    <!-- Small boxes (Stat box) -->
                                    <div class="row">
                                        <div class="col-lg-3 col-6">
                                            <!-- small box -->
                                            <div class="small-box bg-info">
                                                <div class="inner">
                                                    <h3 th:text="${nbrdraft}">0</h3> 
                                                    <p data-th-text="#{lang.docdraft}"></p>
                                                </div>
                                                <div class="icon">
                                                    <i class="far fa-thumbs-up"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- ./col -->
                                        <div class="col-lg-3 col-6">
                                            <!-- small box -->
                                            <div class="small-box bg-warning">
                                                <div class="inner">
                                                    <h3 th:text="${nbrprelim}">0</h3> 
                                                    <p data-th-text="#{lang.docprelim}"></p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-stats-bars"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- ./col -->
                                        <div class="col-lg-3 col-6">
                                            <!-- small box -->
                                            <div class="small-box bg-success">
                                                <div class="inner">
                                                    <h3 th:text="${nbrfinal}">0</h3> 
                                                    <p data-th-text="#{lang.docfinal}"></p>
                                                </div>
                                                <div class="icon">
                                                    <i class="far fa-thumbs-up"></i>
                                                </div>
                                                <!-- a href="#" class="small-box-footer" data-th-text="#{lang.details}">More info </a> <i class="fas fa-arrow-circle-right"></i -->
                                            </div>
                                        </div>
                                        <!-- ./col -->
                                        <div class="col-lg-3 col-6">
                                            <!-- small box -->
                                            <div class="small-box bg-danger">
                                                <div class="inner">
                                                    <h3 th:text="${nbrexp}">0</h3> 
                                                    <p data-th-text="#{lang.docexp}"></p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-pie-graph"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- ./col -->
                                    </div>
                                    <!-- /.row -->
                                    <!-- Main row -->
                                    <div class="row">
                                        <!-- Left col -->
                                        <section class="col-lg-12 connectedSortable">
                                            <!-- Template Document Report -->
                                            <div class="card mb-3">
                                                <div class="card-header border-0">
                                                    <h2 class="card-title" data-th-text="#{lang.template.report.title}">Template Document Report</h2>
                                                    <div class="card-tools">
                                                        <a href="#" class="btn btn-tool btn-sm">
                                                            <i class="bi bi-download"></i>
                                                        </a>
                                                        <a href="#" class="btn btn-tool btn-sm">
                                                            <i class="bi bi-list"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <table id="tblTemplateReport" class="table table-bordered table-striped">
                                                        <thead>
                                                        <tr>
                                                            <th data-th-text="#{lang.template.report.etat}">État</th>
                                                            <th data-th-text="#{lang.template.report.templateId}">Template ID</th>
                                                            <th data-th-text="#{lang.template.report.docType}">Doc Type</th>
                                                            <th data-th-text="#{lang.template.report.classificationCode}">Classification Code</th>
                                                            <th data-th-text="#{lang.template.report.nameFr}">Name FR</th>
                                                            <th data-th-text="#{lang.template.report.nameEn}">Name EN</th>
                                                            <th data-th-text="#{lang.template.report.templateStatus}">Status</th>
                                                            <th data-th-text="#{lang.template.report.createDate}">Create Date</th>
                                                            <th data-th-text="#{lang.template.report.aliasDescription}">Description</th>
                                                            <th data-th-text="#{lang.template.report.finalDocuments}">Final Documents</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr th:each="report : ${templateReports}">
                                                            <td th:text="${report.etat}"></td>
                                                            <td th:text="${report.templateId}"></td>
                                                            <td th:text="${report.docType}"></td>
                                                            <td th:text="${report.classificationCode}"></td>
                                                            <td th:text="${report.nameFr}"></td>
                                                            <td th:text="${report.nameEn}"></td>
                                                            <td th:text="${report.templateStatus}"></td>
                                                            <td th:text="${#dates.format(report.createDate, 'yyyy-MM-dd')}"></td>
                                                            <td th:text="${report.aliasDescription}"></td>
                                                            <td th:text="${report.finalDocuments}"></td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </section>
                                        <!-- right col -->
                                    </div>
                                    <!-- /.row (main row) -->
                                </div><!-- /.container-fluid -->
                            </section>
                            <!-- /.content -->
                        </div>
                        <!-- /.content-wrapper -->
                        <footer class="main-footer">
                            <strong>OWord Tools</strong>
                        </footer>


                        </aside>

                    </div>
                    <!-- ./wrapper -->

                    <!-- jQuery -->
                    <script th:src="@{/plugins/jquery/jquery.min.js}"></script>
                    <!-- jQuery UI 1.11.4 -->
                    <script th:src="@{/plugins/jquery-ui/jquery-ui.min.js}"></script>
                    <!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
                    <script>
                        $.widget.bridge('uibutton', $.ui.button)
                    </script>
                    <!-- Bootstrap 4 -->
                    <script th:src="@{/plugins/bootstrap/js/bootstrap.bundle.min.js}"></script>
                    <!-- ChartJS -->
                    <script th:src="@{/plugins/chart.js/Chart.min.js}"></script>
                    <!-- Sparkline -->
                    <script th:src="@{/plugins/sparklines/sparkline.js}"></script>
                    <!-- JQVMap -->
                    <script th:src="@{/plugins/jqvmap/jquery.vmap.min.js}"></script>
                    <script th:src="@{/plugins/jqvmap/maps/jquery.vmap.usa.js}"></script>
                    <!-- jQuery Knob Chart -->
                    <script th:src="@{/plugins/jquery-knob/jquery.knob.min.js}"></script>
                    <!-- DataTables  & Plugins -->
                    <script th:src="@{/plugins/datatables/jquery.dataTables.min.js}"></script>
                    <script th:src="@{/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js}"></script>
                    <script th:src="@{/plugins/datatables-responsive/js/dataTables.responsive.min.js}"></script>
                    <script th:src="@{/plugins/datatables-responsive/js/responsive.bootstrap4.min.js}"></script>
                    <script th:src="@{/plugins/datatables-buttons/js/dataTables.buttons.min.js}"></script>
                    <script th:src="@{/plugins/datatables-buttons/js/buttons.bootstrap4.min.js}"></script>
                    <script th:src="@{/plugins/jszip/jszip.min.js}"></script>
                    <script th:src="@{/plugins/pdfmake/pdfmake.min.js}"></script>
                    <script th:src="@{/plugins/pdfmake/vfs_fonts.js}"></script>
                    <script th:src="@{/plugins/datatables-buttons/js/buttons.html5.min.js}"></script>
                    <script th:src="@{/plugins/datatables-buttons/js/buttons.print.min.js}"></script>
                    <script th:src="@{/plugins/datatables-buttons/js/buttons.colVis.min.js}"></script>
                    <!-- daterangepicker -->
                    <script th:src="@{/plugins/moment/moment.min.js}"></script>
                    <script th:src="@{/plugins/daterangepicker/daterangepicker.js}"></script>
                    <!-- Tempusdominus Bootstrap 4 -->
                    <script th:src="@{/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js}"></script>
                    <!-- Summernote -->
                    <script th:src="@{/plugins/summernote/summernote-bs4.min.js}"></script>
                    <!-- overlayScrollbars -->
                    <script th:src="@{/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js}"></script>
                    <!-- AdminLTE App -->
                    <script th:src="@{/dist/js/adminlte.js}"></script>
                    <!-- AdminLTE for demo purposes -->
                    <script th:src="@{/dist/js/demo.js}"></script>
                    <!-- AdminLTE dashboard demo (This is only for demo purposes) -->
                    <script th:src="@{/dist/js/pages/dashboard.js}"></script>

                    <script>
                        $(function () {
                            $('#tblTemplateReport').DataTable({
                                "responsive": true,
                                'paging': true,
                                'lengthChange': true,
                                'searching': true,
                                'ordering': true,
                                'info': true,
                                'autoWidth': false,
                                "pageLength": 10
                            });
                            $('#tblTemplateReport tbody').on('click', 'tr', function () {
                                $(this).toggleClass('selected');
                            });
                            $("#tblTemplateReport_length").closest("div.row").addClass("o-tb-top");
                        });
                    </script>
                </body>
                </html>

