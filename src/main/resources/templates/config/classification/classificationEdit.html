<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org"
      lang="en">
<head>
    <title th:text="#{view.index.title}">Welcome!</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

</head>
<body class="skin-blue sidebar-mini">
<div th:include="fragments/header::head"></div>
<div class="wrapper">
    <!-- Header Container -->
    <span th:replace="~{fragments/layout :: oword-header}"></span>
    <!-- Main Sidebar Container -->
    <div th:replace="~{fragments/layout :: oword-leftSide}"></div>

    <div class="content-wrapper">
        <section class="content-header">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-6">
                        <h1><small data-th-text="#{lang.classifications}"></small></h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a th:href="@{/}" data-th-text="#{lang.home}"></a></li>
                            <li class="breadcrumb-item " data-th-text="#{lang.configuration}"></li>
                            <li class="breadcrumb-item active" data-th-text="#{lang.classifications}"></li>
                        </ol>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>
        <div class="content">
            <!-- /* Handle the flash message */-->
            <th:block th:if="${message != null}">
                <!-- /* The message code is returned from the @Controller */ -->
                <div th:replace="~{fragments/components :: alert (type=${#strings.toLowerCase(message.type)}, message=#{${message.message}(${#authentication.name})})}">
                    &nbsp;
                </div>
            </th:block>

            <div class="col-12">
                <div class="card">
                    <div class="card-header with-border">
                        <h5 class="box-title" data-th-text="#{lang.cla.editclass}"></h5>
                    </div>
                    <div class="card-body">
                        <form role="form" id="validateFormClassification" action="#" method="post"
                              th:object="${classification}" th:action="@{/classification/save}"
                              onSubmit="return confirm('Do you want to save Classification?') ">
                            <th:block th:if="${param.error != null}">
                                <div th:replace="~{fragments/components :: alert (type='danger', message='Sign in error. Please try again.')}">
                                    Alert
                                </div>
                            </th:block>
                            <div class="row">
                                <div class="form-group  col-lg-5 col-md-5 col-sm-12"
                                     th:classappend="${#fields.hasErrors('nameFr')}? 'has-error'">
                                    <label for="txtNameFr" class="control-label"
                                           data-th-text="#{lang.cla.nameFr}"></label>
                                    <div>
                                        <input type="text" id="txtNameFr" class="form-control" name="nameFr"
                                               th:field="*{nameFr}" placeholder="name (français)"
                                               th:data-msg="#{validate.txtfrench}" required/>
                                        <span class="help-block" th:if="${#fields.hasErrors('nameFr')}"
                                              th:errors="*{NameFr}">Classification name should not be empty</span>
                                    </div>
                                </div>
                                <div class="form-group  col-lg-4 col-md-4 col-sm-12"
                                     th:classappend="${#fields.hasErrors('nameEn')}? 'has-error'">
                                    <label for="txtNameEn" class="control-label"
                                           data-th-text="#{lang.cla.nameEn}"></label>
                                    <div>
                                        <input type="text" class="form-control" id="txtNameEn" name="nameEn"
                                               th:field="*{nameEn}" placeholder="name (english)"
                                               th:data-msg="#{validate.txtenglish}" required/>
                                        <span class="help-block" th:if="${#fields.hasErrors('nameEn')}"
                                              th:errors="*{nameEn}">Classification name should not be empty</span>
                                    </div>
                                </div>

                                <div class="box-footer col-lg-3 col-md-3 col-sm-12 o-padding-bottom-5 o-text-right">
                                    <label class="control-label oword-mobile-display-none">&nbsp;</label>
                                    <div class="o-text-right" style="text-align:right;">
                                        <a th:href="@{/classifications}" class="btn btn-primary"
                                           style="margin-right: 5px;" data-th-text="#{lang.cancel}"
                                           onClick="return confirm('Do you want to Cancel Edit Classification ?') ">
                                            <i class="fa"></i>
                                        </a>
                                        <button type="submit" class="btn btn-primary btn-group-lg"
                                                data-th-text="#{lang.save}"></button>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-lg-offset-6 col-lg-7">
                                    <input type="hidden" class="form-control" id="txtId" th:field="*{code}"
                                           placeholder="code" name="code"/>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- jQuery -->
<script th:src="@{/plugins/jquery/jquery.min.js}"></script>
<!-- Bootstrap 4 -->
<script th:src="@{/plugins/bootstrap/js/bootstrap.bundle.min.js}"></script>
<!-- AdminLTE App -->
<script th:src="@{/dist/js/adminlte.min.js}"></script>
<!-- AdminLTE for demo purposes -->
<script th:src="@{/dist/js/demo.js}"></script>
<!-- jquery-validation -->
<script th:src="@{/plugins/jquery-validation/jquery.validate.min.js}"></script>
<script th:src="@{/plugins/jquery-validation/additional-methods.min.js}"></script>

<script th:inline="javascript" type="text/javascript">
    /* Fill in modal with content loaded with Ajax */
    $(document).ready(function () {
        $('#signup').on('click', function () {
            $("#signup-modal").modal();
            $("#signup-modal-body").text("");
            $.ajax({
                url: "signup",
                cache: false
            }).done(function (html) {
                $("#signup-modal-body").append(html);
            });
        })

        $(function () {
            $.validator.setDefaults({
                submitHandler: function () {
                    $form.submit();
                }
            });
            $('#validateFormClassification').validate({

                errorElement: 'span',
                errorPlacement: function (error, element) {
                    error.addClass('invalid-feedback');
                    element.closest('.form-group').append(error);
                },
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass('is-invalid');
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).removeClass('is-invalid');
                }
            });
        }); /* /function  */
    });


</script>
<!-- Signup modal -->
<div th:replace="~{fragments/layout :: footer}">&copy; 2016 The Static Templates</div>
<div th:replace="~{fragments/components :: modal(id='signup-modal', title='Signup')}"></div>
</body>
</html>