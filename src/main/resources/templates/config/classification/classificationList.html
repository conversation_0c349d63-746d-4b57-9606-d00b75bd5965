<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org" 
      xmlns:sec="https://www.thymeleaf.org/extras/spring-security" lang="en"> 
    <head>
        <title th:text="#{view.index.title}">Welcome!</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="_ajaxAuth" content="Basic YWRtaW4gOiAxMjM=" /> 
        <meta th:name="_csrf" th:content="${_csrf.token}"/>
        <meta th:name="_csrf_header" th:content="${_csrf.headerName}"/>    
    </head>
    <style>
        table tbody th {
            font-weight: normal;
        }
    </style>
    <body class="skin-blue sidebar-mini">
        <div th:include="fragments/header::head"></div>
        <div class="wrapper">
            <!-- Header Container -->
            <span th:replace="~{fragments/layout :: oword-header}"></span>
            <!-- Main Sidebar Container -->
            <div th:replace="~{fragments/layout :: oword-leftSide}"></div>
            <div class="content-wrapper"> 
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-sm-6">
                                <h1> <small data-th-text="#{lang.classifications}"></small></h1>
                            </div>
                            <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right">
                                    <li class="breadcrumb-item"><a th:href="@{/}" data-th-text="#{lang.home}"></a></li>
                                    <li class="breadcrumb-item " data-th-text="#{lang.configuration}"></li>
                                    <li class="breadcrumb-item active" data-th-text="#{lang.classifications}"></li>
                                </ol>
                            </div>
                        </div>
                    </div><!-- /.container-fluid -->
                </section>      
                <div class="content">
                    <!-- /* Handle the flash message */-->
                    <th:block th:if="${message != null}">
                        <!-- /* The message code is returned from the @Controller */ -->
                        <div th:replace="~{fragments/components :: alert (type=${#strings.toLowerCase(message.type)}, message=#{${message.message}(${#authentication.name})})}">&nbsp;</div>
                    </th:block> 
                    <div class="col-12">
                        <div class="card">

                            <!-- /* Handle the flash message */-->
                            <th:block th:if="${message != null}">
                                <!-- /* The message code is returned from the @Controller */ -->
                                <div th:replace="~{fragments/components :: alert (type=${#strings.toLowerCase(message.type)}, message=#{${message.message}(${#authentication.name})})}">&nbsp;</div>
                            </th:block>
                            <div class ="card-header" id="addNewClassification" style="display:none"> 
                                <form role="form"  id="validateForm"   action="#" method="post" th:object="${classification}" th:action="@{/classification/save}">
                                    <th:block th:if="${param.error != null}">
                                        <div th:replace="~{fragments/components :: alert (type='danger', message='Sign in error. Please try again.')}">Alert</div>
                                    </th:block>
                                    <div class="row">
                                        <div class="col-lg-4 col-md-4 col-sm-12 form-group" th:classappend="${#fields.hasErrors('nameEn')}? 'has-error'">
                                            <label for="txtNameEn" class="control-label" data-th-text="#{lang.cla.nameEn}"></label>
                                            <div>
                                                <input type="text" class="form-control" id="txtNameEn" name="nameEn" th:field="*{nameEn}" placeholder="name (english)" />
                                                <span class="help-block" th:if="${#fields.hasErrors('nameEn')}" th:errors="*{nameEn}">Classification name should not be empty</span>
                                            </div>
                                        </div>
                                        <div class="col-lg-5 col-md-5 col-sm-12 o-padding-bottom-5 form-group" th:classappend="${#fields.hasErrors('nameFr')}? 'has-error'">
                                            <label for="txtNameFr" class="control-label" data-th-text="#{lang.cla.nameFr}"></label>
                                            <div>
                                                <input type="text" id="txtNameFr" class="form-control" name="nameFr" th:field="*{nameFr}"  placeholder="name (français)"></textarea>
                                                <span class="help-block" th:if="${#fields.hasErrors('nameFr')}" th:errors="*{+}">Classification name should not be empty</span>
                                            </div>
                                        </div> 
                                        <div class="box-footer col-lg-3 col-md-3 col-sm-12 o-padding-bottom-5 o-text-right">
                                            <label class="control-label oword-mobile-display-none">&nbsp;</label>
                                            <div style="text-align:right;">
                                                <a th:href="@{/classifications}" class="btn btn-primary" style="margin-right: 5px;">
                                                    <i class="fa"></i>
                                                </a>
                                                <button type="submit" class="btn btn-primary btn-group-lg"></button>
                                            </div>
                                        </div> 
                                        
                                        
                                    </div>
                                    <div class="form-group">
                                        <div class="col-lg-offset-6 col-lg-7">
                                            <input type="hidden" class="form-control" id="txtId" th:field="*{code}" placeholder="code" name="code" /> 
                                        </div>
                                    </div> 
                                </form> 
                            </div> 

                            <div class="card-body">
                                <form>
                                    <table id="tblClassifications" class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                               <th class="all" data-th-text="#{lang.cla.code}"></th>
                                               <th class="all" data-th-text="#{lang.cla.nameEn}"></th>
                                               <th class="all" data-th-text="#{lang.cla.nameFr}"></th> 
                                               <th class="no-sort all">&nbsp;</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="classification, iStat : ${classifications.content}">
                                            <th th:text="${classification.code}" ></th>
                                            <th th:text="${classification.nameEn}"></th>
                                            <th th:text="${classification.nameFr}"></th>
                                            <th class="no-sort all">
                                                <a th:href="@{classification/edit?id={id}(id=${classification.code})}" >
                                                    <i class="fa fa-edit fa-2x"></i> </a> </th>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <input type="hidden" id="csrfToken" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
                                </form>
                            </div>
                        </div>
                    </div> 
                </div>
            </div>
           
        </div>
        <div th:replace="~{fragments/layout :: footer}"></div>
        <script th:inline="javascript">
            /*<![CDATA[*/
            let contextPath = /*[[@{/}]]*/
                    /*]]>*/
        </script>
        
        <!-- jQuery -->
        <script th:src="@{/plugins/jquery/jquery.min.js}"></script>
        <!-- Bootstrap 4 -->
        <script th:src="@{/plugins/bootstrap/js/bootstrap.bundle.min.js}"></script>
        <!-- DataTables  & Plugins -->

        <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap4.min.js"></script>
        <script th:src="@{/plugins/sorting/diacritics-sort.js}"></script>
        <!-- AdminLTE App -->
        <script th:src="@{/dist/js/adminlte.min.js}"></script>
        <!-- AdminLTE for demo purposes -->
        <script th:src="@{/dist/js/demo.js}"></script>
        
        <!-- jquery-validation -->
        <script th:src="@{/plugins/jquery-validation/jquery.validate.min.js}"></script>
        <script th:src="@{/plugins/jquery-validation/additional-methods.min.js}"></script>
 
 
 

        <script>
            $(document).ready(function () {

                var token = $("meta[name='_csrf']").attr("content"); 
                var header = $("meta[name='_csrf_header']").attr("content");

                /* $.ajaxSetup({
                    xhrFields: {
                        withCredentials: true
                    },
                    beforeSend: function (xhr, settings) {
                        //alert("Req: headers " + "_csrf" + ", token: " + csrfToken);                  xhr.setRequestHeader("_csrf", csrfToken);
                        //  alert("Url: " settings.url +  ", data:"+ settings.data);
                    }
                });*/

                $('#tblClassifications').DataTable({
                    'columnDefs' : [{ targets: [2], type: 'diacritics-neutralise' },
                        { orderable: false, targets: [3]}],
                    "paging": true,
                    "searching": true,
                    "ordering": true
                });
/*
                $('#tblClassifications tbody').on('click', 'tr', function () {
                    $(this).toggleClass('selected');
                })
                $("#tblClassifications_length").closest("div.row").addClass("o-tb-top");*/
                
                $("#btn-new-classification").on('click', function () { 
                    $("#addNewClassification").toggle();
                })
                
                $(function () {
                    $.validator.setDefaults({
                        submitHandler: function () {
                            $form.submit(); 
                        }
                    });
                    $('#validateForm').validate({
                        rules: {
                            nameEn: {
                                required: true,
                                minlength: 5
                            },
                            nameFr: {
                                required: true,
                                minlength: 5
                            } 
                        },
                        messages: {
                            nameEn: {
                                required: "Please enter a name in English",
                                minlength: "Your name must be at least 5 characters long"
                            },
                            nameFr: {
                                required: "Please provide a name in French ",
                                minlength: "Your name must be at least 5 characters long"
                            } 
                        },
                        errorElement: 'span',
                        errorPlacement: function (error, element) {
                            error.addClass('invalid-feedback');
                            element.closest('.form-group').append(error);
                        },
                        highlight: function (element, errorClass, validClass) {
                            $(element).addClass('is-invalid');
                        },
                        unhighlight: function (element, errorClass, validClass) {
                            $(element).removeClass('is-invalid');
                        }
                    });
                }); /* /function  */
                
            });
        </script>

        
    </body>
</html>