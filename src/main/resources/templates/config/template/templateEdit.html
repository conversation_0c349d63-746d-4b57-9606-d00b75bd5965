<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org"
      lang="en">
<head>
    <title th:text="#{view.index.title}">Welcome!</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta th:name="_csrf" th:content="${_csrf.token}"/>
    <meta th:name="_csrf_header" th:content="${_csrf.headerName}"/>
    <style>
        input[type="file"]::file-selector-button {
            border-radius: 4px;
            padding: 0 16px;
            height: 40px;
            cursor: pointer;
            background-color: gray;
            border: 1px solid rgba(0, 0, 0, 0.16);
            box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.05);
            margin-right: 16px;
            transition: background-color 200ms;
        }

        /* file upload button hover state */
        input[type="file"]::file-selector-button:hover {
            background-color: #f3f4f6;
        }

        /* file upload button active state */
        input[type="file"]::file-selector-button:active {
            background-color: #e5e7eb;
        }

        #txtFile {
            vertical-align: middle;
        }

        .switch.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .switch.disabled .slider {
            background-color: #ccc !important;
            cursor: not-allowed;
        }

        .switch.disabled input:checked + .slider {
            background-color: #ccc !important;
        }

    </style>
</head>
<body class="skin-blue sidebar-mini">
<div th:include="fragments/header::head"></div>
<div class="wrapper">
    <!-- Header Container -->
    <span th:replace="~{fragments/layout :: oword-header}"></span>
    <!-- Main Sidebar Container -->
    <div th:replace="~{fragments/layout :: oword-leftSide}"></div>

    <div class="content-wrapper">
        <section class="content-header">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-6">
                        <h1 th:if="${templateForm.templateId > 0 && #locale.language == 'en'}"
                            data-th-text="#{lang.tp.edittemp} + ' : ' + ${docTypeDescEn}"><small></small></h1>
                        <h1 th:if="${templateForm.templateId > 0 && #locale.language == 'fr'}"
                            data-th-text="#{lang.tp.edittemp} + ' : ' + ${docTypeDescFr}"><small></small></h1>
                        <h1 th:if="${templateForm.templateId <= 0}" data-th-text="#{lang.tp.newtemp}"><small></small>
                        </h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a th:href="@{/}" data-th-text="#{lang.home}"></a></li>
                            <li class="breadcrumb-item " data-th-text="#{lang.configuration}"></li>
                            <li class="breadcrumb-item active" data-th-text="#{lang.templates}"></li>
                        </ol>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>
        <div class="content">
            <!-- /* Handle the flash message */-->
            <th:block th:if="${message != null}">
                <!-- /* The message code is returned from the @Controller */ -->
                <div th:replace="~{fragments/components :: alert (type=${#strings.toLowerCase(message.type)}, message=#{${message.message}(${#authentication.name})})}">
                    &nbsp;
                </div>
            </th:block>

            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">

                        <form role="form" id="validateForm" method="post" th:object="${templateForm}"
                              enctype="multipart/form-data" th:action="@{/template/save}"
                              onSubmit="return validateTemplateForm()">
                            <th:block th:if="${param.error != null}">
                                <div th:replace="~{fragments/components :: alert (type='danger', message='Sign in error. Please try again.')}">
                                    Alert
                                </div>
                            </th:block>
                            <div class="row">
                                <div th:if="${templateForm.templateId <= 0}" class="form-group col-md-4 col-sm-12"
                                     th:classappend="${#fields.hasErrors('docType')}? 'has-error'">
                                    <label for="selDocType" class="control-label"
                                           data-th-text="#{lang.tp.doctype}"></label>
                                    <div th:if="${#locale.language == 'en'}">
                                        <select class="form-control" th:field="*{docType}" name="selectDocType"
                                                id="selectDocTypeId" th:data-msg="#{validate.select.doctype}" required>
                                            <option th:each="element, rowStat: ${docTypeList}"
                                                    th:value="${element.docTypeCode}"
                                                    th:text="${element.docTypeCode + ' - ' + element.nameEn}"
                                                    th:selected="${element.docTypeCode == docType}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('docType')}"
                                              th:errors="*{docType}">DocType should not be empty</span>
                                    </div>
                                    <div th:if="${#locale.language == 'fr'}">
                                        <select class="form-control" th:field="*{docType}" name="selectDocType"
                                                id="selectDocTypeId" th:data-msg="#{validate.select.doctype}" required>
                                            <option th:each="element, rowStat: ${docTypeList}"
                                                    th:value="${element.docTypeCode}"
                                                    th:text="${element.docTypeCode + ' - ' + element.nameFr}"
                                                    th:selected="${element.docTypeCode == docType}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('docType')}"
                                              th:errors="*{docType}">DocType should not be empty</span>
                                    </div>
                                </div>
                                <div th:if="${templateForm.templateId > 0}">
                                    <input type="hidden" class="form-control" id="docType" name="docType"
                                           th:field="*{docType}"/>
                                </div>

                                <div class="form-group col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('classification')}? 'has-error'">
                                    <label for="txtClassification" class="control-label"
                                           data-th-text="#{lang.tp.classification}">Classification</label>
                                    <div th:if="${#locale.language == 'en'}">
                                        <select class="form-control" th:field="*{classification}"
                                                name="selectClassification"
                                                th:data-msg="#{validate.select.classification}">
                                            <option value="" th:text="#{lang.select.choose}">-- Choose --</option>
                                            <option th:each="element : ${classificationListEn}"
                                                    th:value="${element.code}"
                                                    th:text="${element.code + ' - ' + element.nameEn}"
                                                    th:selected="${element.code == classification}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('classification')}"
                                              th:errors="*{classification}">Classification should not be empty</span>
                                    </div>
                                    <div th:if="${#locale.language == 'fr'}">
                                        <select class="form-control" th:field="*{classification}"
                                                name="selectClassification"
                                                th:data-msg="#{validate.select.classification}" >
                                            <option value="" th:text="#{lang.select.choose}">-- Choisir --</option>
                                            <option th:each="element : ${classificationListFr}"
                                                    th:value="${element.code}"
                                                    th:text="${element.code + ' - ' + element.nameFr}"
                                                    th:selected="${element.code == classification}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('classification')}"
                                              th:errors="*{classification}">Classification should not be empty</span>
                                    </div>
                                </div>


                                <div class="form-group col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('documentInstance')}? 'has-error'">
                                    <label for="txtDocumentInstance" class="control-label"
                                           data-th-text="#{lang.tp.di}"></label>
                                    <div th:if="${#locale.language == 'en'}">
                                        <select class="form-control" th:field="*{documentInstance}"
                                                th:data-msg="#{validate.select.documentInstance}">
                                            <option th:each="element : ${documentInstanceList}"
                                                    th:value="${element.instanceCode}"
                                                    th:text="${element.nameEn}"
                                                    th:selected="${element.instanceCode == documentInstance}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('documentInstance')}"
                                              th:errors="*{documentInstance}">Instance Code should not be empty</span>
                                    </div>
                                    <div th:if="${#locale.language == 'fr'}">
                                        <select class="form-control" th:field="*{documentInstance}"
                                                th:data-msg="#{validate.select.documentInstance}">
                                            <option th:each="element : ${documentInstanceList}"
                                                    th:value="${element.instanceCode}"
                                                    th:text="${element.nameFr}"
                                                    th:selected="${element.instanceCode == documentInstance}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('documentInstance')}"
                                              th:errors="*{documentInstance}">Instance Code should not be empty</span>
                                    </div>
                                </div>
                                <div class="form-group col-md-2 col-sm-12"
                                     th:classappend="${#fields.hasErrors('permission')}? 'has-error'">
                                    <label for="txtPermission" class="control-label"
                                           data-th-text="#{lang.tp.up}"></label>
                                    <div>
                                        <select class="form-control" th:field="*{permission}">
                                            <option th:value="A" th:text="A"
                                                    th:selected="${permission == 'A'}"></option>
                                            <option th:value="B" th:text="B"
                                                    th:selected="${permission == 'B'}"></option>
                                            <option th:value="C" th:text="C"
                                                    th:selected="${permission == 'C'}"></option>
                                            <option th:value="D" th:text="D"
                                                    th:selected="${permission == 'D'}"></option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('permission')}"
                                              th:errors="*{permission}">Permission should not be empty</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-md-4 col-sm-12"
                                     th:classappend="${#fields.hasErrors('serviceCode')}? 'has-error'">
                                    <label for="txtClassification" class="control-label"
                                           data-th-text="#{lang.tp.service}"></label>
                                    <div th:if="${#locale.language == 'en'}">
                                        <select class="form-control" th:field="*{serviceCode}"
                                                th:data-msg="#{validate.select.serviceCode}">
                                            <option th:value="NULL" th:text="NULL"></option>
                                            <option th:each="element : ${serviceCodeList}"
                                                    th:value="${element.service}"
                                                    th:text="${element.service + ' - ' + element.descriptionEn}"
                                                    th:selected="${element.service == serviceCode}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('serviceCode')}"
                                              th:errors="*{serviceCode}">Service Code should not be empty</span>
                                    </div>
                                    <div th:if="${#locale.language == 'fr'}">
                                        <select class="form-control" th:field="*{serviceCode}"
                                                th:data-msg="#{validate.select.serviceCode}">
                                            <option th:value="NULL" th:text="NULL"></option>
                                            <option th:each="element : ${serviceCodeList}"
                                                    th:value="${element.service}"
                                                    th:text="${element.service + ' - ' + element.descriptionFr}"
                                                    th:selected="${element.service == serviceCode}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('serviceCode')}"
                                              th:errors="*{serviceCode}">Service Code should not be empty</span>
                                    </div>
                                </div>

                                <div class="form-group col-md-2 col-sm-12"
                                     th:classappend="${#fields.hasErrors('encounterType')}? 'has-error'">
                                    <label for="txtEncounterType" class="control-label"
                                           data-th-text="#{lang.tp.et}"></label>
                                    <div th:if="${#locale.language == 'en'}">
                                        <select class="form-control" th:field="*{encounterType}"
                                                th:data-msg="#{validate.select.encounterType}">
                                            <option th:value="NULL" th:text="NULL"></option>
                                            <option th:each="element : ${encounterTypeCodeList}"
                                                    th:value="${element.encounterType}"
                                                    th:text="${element.descriptionEn}"
                                                    th:selected="${element.encounterType == encounterType}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('encounterType')}"
                                              th:errors="*{encounterType}">Encounter Type should not be empty</span>
                                    </div>
                                    <div th:if="${#locale.language == 'fr'}">
                                        <select class="form-control" th:field="*{encounterType}"
                                                th:data-msg="#{validate.select.encounterType}">
                                            <option th:value="NULL" th:text="NULL"></option>
                                            <option th:each="element : ${encounterTypeCodeList}"
                                                    th:value="${element.encounterType}"
                                                    th:text="${element.descriptionFr}"
                                                    th:selected="${element.encounterType == encounterType}">
                                            </option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('encounterType')}"
                                              th:errors="*{encounterType}">Encounter Type should not be empty</span>
                                    </div>
                                </div>

                                <div class="form-group col-md-4 col-sm-12"
                                     th:classappend="${#fields.hasErrors('alias')}? 'has-error'">
                                    <label for="txtNameFr" class="control-label" data-th-text="#{lang.tp.al}"></label>
                                    <div>
                                        <input type="text" id="txtAlias" class="form-control" name="alias"
                                               th:field="*{alias}" th:value="${templateForm.alias}"
                                               placeholder="ALIAS"/>
                                        <span class="help-block" th:if="${#fields.hasErrors('alias')}"
                                              th:errors="*{alias}">Alias name should not be empty</span>
                                    </div>
                                </div>
                                <div class="form-group col-md-2 col-sm-12"
                                     th:classappend="${#fields.hasErrors('templateStatus')}? 'has-error'">
                                    <label for="txtStatus" class="control-label" data-th-text="#{lang.tp.st}"></label>
                                    <div>
                                        <select class="form-control" th:field="*{templateStatus}">
                                            <option th:value="ACTIVE" th:text="ACTIVE"
                                                    th:selected="${templateStatus == 'ACTIVE'}"></option>
                                            <option th:value="INACTIVE" th:text="INACTIVE"
                                                    th:selected="${templateStatus == 'INACTIVE'}"></option>
                                        </select>
                                        <span class="help-block" th:if="${#fields.hasErrors('templateStatus')}"
                                              th:errors="*{templateStatus}">Status should not be empty</span>
                                    </div>
                                </div>
                                <div class="form-group col-md-4 col-sm-12"
                                     th:classappend="${#fields.hasErrors('aliasDescription')}? 'has-error'">
                                    <label for="txtNameFr" class="control-label" data-th-text="#{lang.tp.desc}"></label>
                                    <div>
                                        <input type="text" id="txtAlias" class="form-control" name="comment"
                                               th:field="*{aliasDescription}"
                                               th:value="${templateForm.aliasDescription}"
                                               placeholder="ALIAS DESCRIPTION"/>
                                        <span class="help-block" th:if="${#fields.hasErrors('aliasDescription')}"
                                              th:errors="*{aliasDescription}">Comment name should not be empty</span>
                                    </div>
                                </div>
                                <div class="form-group col-md-4 col-sm-12"
                                     th:classappend="${#fields.hasErrors('comment')}? 'has-error'">
                                    <label for="txtNameFr" class="control-label"
                                           data-th-text="#{lang.tp.comment}"></label>
                                    <div>
                                        <input type="text" id="txtAlias" class="form-control" name="comment"
                                               th:field="*{comment}" th:value="${templateForm.alias}"/>
                                        <span class="help-block" th:if="${#fields.hasErrors('comment')}"
                                              th:errors="*{comment}">Comment name should not be empty</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">


                            </div>
                            <hr>
                            <div class="row">
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('allowDraft')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.ad}"></span>
                                        <label for="allowDraft" class="switch" th:field="*{allowDraft}">
                                            <input type="checkbox" id="allowDraft" name="allowDraft"
                                                   th:checked="${templateForm.allowDraft == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('allowCopyPrevious')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.cp}"></span>
                                        <label for="allowCopyPrevious" class="switch" th:field="*{allowCopyPrevious}">
                                            <input type="checkbox" id="allowCopyPrevious" name="allowCopyPrevious"
                                                   th:checked="${templateForm.allowCopyPrevious == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('reviewable')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.rv}"></span>
                                        <label for="reviewable" class="switch" th:field="*{reviewable}">
                                            <input type="checkbox" id="reviewable" name="reviewable"
                                                   th:checked="${templateForm.reviewable == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('disablePrintingDraft')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.pd}"></span>
                                        <label for="disablePrintingDraft" class="switch"
                                               th:field="*{disablePrintingDraft}">
                                            <input type="checkbox" id="disablePrintingDraft" name="disablePrintingDraft"
                                                   th:checked="${templateForm.disablePrintingDraft == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('allowCopyPreviousUser')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.cpu}"></span>
                                        <label for="allowCopyPreviousUser" class="switch"
                                               th:field="*{allowCopyPreviousUser}">
                                            <input type="checkbox" id="allowCopyPreviousUser"
                                                   name="allowCopyPreviousUser"
                                                   th:checked="${templateForm.allowCopyPreviousUser == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('editableByArchivist')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.ea}"></span>
                                        <label for="editableByArchivist" class="switch"
                                               th:field="*{editableByArchivist}">
                                            <input type="checkbox" id="editableByArchivist" name="editableByArchivist"
                                                   th:checked="${templateForm.editableByArchivist == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('allowPreliminary')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.ap}"></span>
                                        <label for="allowPreliminary" class="switch" th:field="*{allowPreliminary}">
                                            <input type="checkbox" id="allowPreliminary" name="allowPreliminary"
                                                   th:checked="${templateForm.allowPreliminary == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('copyPreviousByDefault')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.cpd}"></span>
                                        <label for="copyPreviousByDefault" class="switch"
                                               th:field="*{copyPreviousByDefault}">
                                            <input type="checkbox" id="copyPreviousByDefault"
                                                   name="copyPreviousByDefault"
                                                   th:checked="${templateForm.copyPreviousByDefault == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('allowNoVisit')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.nv}"></span>
                                        <label for="allowNoVisit" class="switch" th:field="*{allowNoVisit}">
                                            <input type="checkbox" id="allowNoVisit" name="allowNoVisit"
                                                   th:checked="${templateForm.allowNoVisit == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('allowPrinting')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.pt}"></span>
                                        <label for="allowPrinting" class="switch" th:field="*{allowPrinting}">
                                            <input type="checkbox" id="allowPrinting" name="allowPrinting"
                                                   th:checked="${templateForm.allowPrinting == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('copyPreviousMandatory')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.cpm}"></span>
                                        <label for="copyPreviousMandatory" class="switch"
                                               th:field="*{copyPreviousMandatory}">
                                            <input type="checkbox" id="copyPreviousMandatory"
                                                   name="copyPreviousMandatory"
                                                   th:checked="${templateForm.copyPreviousMandatory == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-3 col-sm-12"
                                     th:classappend="${#fields.hasErrors('printOnly')}? 'has-error'">
                                    <div class="form-check">
                                        <span class="o-font-bold" data-th-text="#{lang.tp.po}"></span>
                                        <label for="printOnly" class="switch" th:field="*{printOnly}">
                                            <input type="checkbox" id="printOnly" name="printOnly"
                                                   th:checked="${templateForm.printOnly == '1'}">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="form-group col-lg-4 col-md-4 col-sm-12">
                                    <span class="o-font-bold" data-th-text="#{lang.tp.rt}"></span>
                                    <label for="replaceTemplate" class="switch" th:field="*{replaceTemplate}">
                                        <input type="checkbox" id="replaceTemplate" name="replaceTemplate"
                                               th:checked="${templateForm.replaceTemplate == '1'}">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                                <div class="form-group col-lg-4 col-md-4 col-sm-12">
                                    <label for="txtFile" id="txtFile" class="control-label"
                                           data-th-text="#{lang.tp.file}"></label>
                                    <input type="file" id="template" name="template" th:field="*{template}" 
                                           th:required="${templateForm.templateId <= 0 || templateForm.replaceTemplate == '1'}"
                                           data-msg="Please select a template file before saving"/>
                                </div>
                            </div>
                            <hr>
                            <div class="form-group col-lg-4 col-md-4 col-sm-12">
                                <div class="col-lg-offset-6 col-lg-7">
                                    <input type="hidden" class="form-control" id="id" name="templateId"
                                           th:field="*{templateId}" placeholder="templateId"/>
                                </div>
                            </div>
                            <a th:href="@{/templates}" class="btn btn-primary" data-th-text="#{lang.cancel}"
                               onClick="return confirm('Do you want to Cancel Edit Template ?') "> <i class="fa"></i>
                            </a>
                            <button type="submit" class="btn btn-primary btn-group-lg"
                                    data-th-text="#{lang.save}"></button>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div th:replace="~{fragments/layout :: footer}"></div>
<div th:replace="~{fragments/components :: modal(id='signup-modal', title='Signup')}"></div>
<!-- /wrapper -->


<!-- jQuery -->
<script th:src="@{/plugins/jquery/jquery.min.js}"></script>
<!-- Bootstrap 4 -->
<script th:src="@{/plugins/bootstrap/js/bootstrap.bundle.min.js}"></script>
<!-- AdminLTE App -->
<script th:src="@{/dist/js/adminlte.min.js}"></script>
<!-- AdminLTE for demo purposes -->
<script th:src="@{/dist/js/demo.js}"></script>
<!-- Bootstrap Switch -->
<script th:src="@{/plugins/bootstrap-switch/js/bootstrap-switch.min.js}"></script>

<!-- jquery-validation -->
<script th:src="@{/plugins/jquery-validation/jquery.validate.min.js}"></script>
<script th:src="@{/plugins/jquery-validation/additional-methods.min.js}"></script>

<script th:inline="javascript" type="text/javascript">
    /* Fill in modal with content loaded with Ajax */
    $(document).ready(function () {
        //console.log ("selected : " +  $('#selectDocTypeId').val()) ; // ''

        $('#signup').on('click', function () {
            $("#signup-modal").modal();
            $("#signup-modal-body").text("");
            $.ajax({
                url: "signup",
                cache: false
            }).done(function (html) {
                $("#signup-modal-body").append(html);
            });
        })


            // Fonction pour gérer l'état de disablePrintingDraft
            function toggleDisablePrintingDraft() {
                var allowDraftChecked = $('#allowDraft').is(':checked');
                var disablePrintingDraftInput = $('#disablePrintingDraft');
                var disablePrintingDraftLabel = $('label[for="disablePrintingDraft"]');

                if (allowDraftChecked) {
                    // Si allowDraft est activé, permettre la modification de allowCopyPrevious
                    disablePrintingDraftInput.prop('disabled', false);
                    disablePrintingDraftLabel.removeClass('disabled');
                } else {
                    // Si allowDraft est désactivé, désactiver allowCopyPrevious
                    disablePrintingDraftInput.prop('checked', false);
                    disablePrintingDraftInput.prop('disabled', true);
                    disablePrintingDraftLabel.addClass('disabled');
                }
            }

            // Appliquer la logique au chargement de la page
            toggleDisablePrintingDraft();

            // Écouter les changements sur allowDraft
            $('#allowDraft').on('change', function () {
                toggleDisablePrintingDraft();
            });


    }); /* /document */

$(function () {
$("input[data-bootstrap-switch]").each(function () {
$(this).bootstrapSwitch('state', $(this).prop('checked'));
})
})

function validateTemplateForm() {
// Check if template is required (new template or replace checked)
var isTemplateRequired = $('#templateId').val() <= 0 || $('#replaceTemplate').is(':checked');

if (isTemplateRequired && !$('#template').val()) {
// Show error message
alert("Please select a template file before saving");
return false;
}

// Confirm save
return confirm('Do you want to save Template?');
}

$(function () {
// Add custom validation method for file inputs
$.validator.addMethod('fileRequired', function(value, element) {
// Check if we need to validate (new template or replace checked)
var isRequired = $('#templateId').val() <= 0 || $('#replaceTemplate').is(':checked');
if (!isRequired) return true;
            
            // Check if file is selected
            return element.files && element.files.length > 0;
        }, 'Please select a template file before saving');

        // Initialize form validation
        $('#validateForm').validate({
            submitHandler: function(form) {
                if (confirm('Do you want to save Template?')) {
                    form.submit();
                }
                return false;
            },
            rules: {
                template: {
                    fileRequired: true
                }
            },
            errorElement: 'span',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid');
            }
        });

        // Update validation when replace checkbox changes
        $('#replaceTemplate').change(function() {
            $('#validateForm').validate().element('#template');
        });
    }); /* /function  */
</script>
<!-- Signup modal -->
</body>
</html>