package ca.muhc.scs.owordtools.utils;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */


import com.aspose.words.SdtType;
import com.aspose.words.TextFormFieldType;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
public class StringUtils
{

    
    private static final Charset utf8charset = Charset.forName(StandardCharsets.UTF_8.toString());
    private static final Charset iso88591charset = Charset.forName(StandardCharsets.ISO_8859_1.toString());
    private static final Pattern NUMBEREXTACTPATTERN = Pattern.compile("[^\\d]*([0-9]+[\\s]*[.,]{0,1}[\\s]*[0-9]*)");
    private static final Map<Character, Character> MAP_NORM = new HashMap<>();

    static
    {
        MAP_NORM.put('À', 'A');
        MAP_NORM.put('Á', 'A');
        MAP_NORM.put('Â', 'A');
        MAP_NORM.put('Ã', 'A');
        MAP_NORM.put('Ä', 'A');
        MAP_NORM.put('È', 'E');
        MAP_NORM.put('É', 'E');
        MAP_NORM.put('Ê', 'E');
        MAP_NORM.put('Ë', 'E');
        MAP_NORM.put('Í', 'I');
        MAP_NORM.put('Ì', 'I');
        MAP_NORM.put('Î', 'I');
        MAP_NORM.put('Ï', 'I');
        MAP_NORM.put('Ù', 'U');
        MAP_NORM.put('Ú', 'U');
        MAP_NORM.put('Û', 'U');
        MAP_NORM.put('Ü', 'U');
        MAP_NORM.put('Ò', 'O');
        MAP_NORM.put('Ó', 'O');
        MAP_NORM.put('Ô', 'O');
        MAP_NORM.put('Õ', 'O');
        MAP_NORM.put('Ö', 'O');
        MAP_NORM.put('Ñ', 'N');
        MAP_NORM.put('Ç', 'C');
        MAP_NORM.put('ª', 'A');
        MAP_NORM.put('º', 'O');
        MAP_NORM.put('§', 'S');
        MAP_NORM.put('³', '3');
        MAP_NORM.put('²', '2');
        MAP_NORM.put('¹', '1');
        MAP_NORM.put('à', 'a');
        MAP_NORM.put('á', 'a');
        MAP_NORM.put('â', 'a');
        MAP_NORM.put('ã', 'a');
        MAP_NORM.put('ä', 'a');
        MAP_NORM.put('è', 'e');
        MAP_NORM.put('é', 'e');
        MAP_NORM.put('ê', 'e');
        MAP_NORM.put('ë', 'e');
        MAP_NORM.put('í', 'i');
        MAP_NORM.put('ì', 'i');
        MAP_NORM.put('î', 'i');
        MAP_NORM.put('ï', 'i');
        MAP_NORM.put('ù', 'u');
        MAP_NORM.put('ú', 'u');
        MAP_NORM.put('û', 'u');
        MAP_NORM.put('ü', 'u');
        MAP_NORM.put('ò', 'o');
        MAP_NORM.put('ó', 'o');
        MAP_NORM.put('ô', 'o');
        MAP_NORM.put('õ', 'o');
        MAP_NORM.put('ö', 'o');
        MAP_NORM.put('ñ', 'n');
        MAP_NORM.put('ç', 'c');

    }

    /**
     * Buil an Acronym of the input string. Rule: get the first letter of each
     * words(max of numofchar), if not enough words, extract the following
     * consones from each words (max: numofchar)
     *
     * @param input
     * @param numofchar
     * @return
     */
    /* WORK IN PROGRESS
     
    /**
     * Are all the wortds in teh array present in the given phrase
     *
     * @param theWords
     * @param thePhrase
     * @return
     */
    public static boolean isAllWordPresentInPhrase(String[] theWords, String thePhrase)
    {
        boolean output = true;
        for (int i = 0; i < theWords.length; i++)
        {
            String aWord = theWords[i];
            if (!thePhrase.contains(aWord))
            {
                output = false;
                break;
            }
        }
        return output;
    }

    public static String removeAccents(String value)
    {
        if (value == null)
        {
            return "";
        }
        StringBuilder sb = new StringBuilder(value);
        for (int i = 0; i < value.length(); i++)
        {
            Character c = MAP_NORM.get(sb.charAt(i));
            if (c != null)
            {
                sb.setCharAt(i, c.charValue());
            }
        }

        return sb.toString();

    }

    /**
     * Escape the string as in " becomne \" and \r\n become \\r etc -
     *
     * @param input
     * @return
     */
    public static String escapeThis(String input)
    {
        input = input.replaceAll("['\"\\\\]", "\\\\$0");
        input = input.replaceAll("\n", "\\\\n");
        input = input.replaceAll("\r", "\\\\r");
        return input;
    }

    /**
     * Count specific charatcter in a string
     *
     * @param s
     * @param toCheck
     * @return
     */
    public static int countChar(String s, char toCheck)
    {
        int counter = 0;
        for (int i = 0; i < s.length(); i++)
        {
            if (s.charAt(i) == toCheck)
            {
                counter++;
            }
        }
        return counter;
    }

    /**
     * Inpout a string and get a lowercase with fist char in caps - basically
     * convert a string to a phrase
     *
     * @param input
     * @param allFirstCap - Mostly use for english where all first letters are
     * set to capitals
     * @param keepUnits - If there is a number the next word will not be change
     * if keepUnits is set to true
     * @return
     */
    public static String toPhrase(String input, boolean allFirstCap, boolean keepUnits, Locale thelocal)
    {
        try
        {
            StringBuffer output = new StringBuffer();
            String[] theInputPass = input.split("[ ]");
            int wordCounter = 0;
            boolean wasNumber = false;
            for (int i = 0; i < theInputPass.length; i++)
            {
                String aWord = theInputPass[i];
                boolean isNumber = isNumber(aWord);
                aWord = aWord.toLowerCase(thelocal);
                if ((!isNumber && aWord.length() > 0 && (allFirstCap || wordCounter == 0)) && (!keepUnits || !wasNumber))
                {
                    aWord = aWord.substring(0, 1).toUpperCase(thelocal) + aWord.substring(1);
                }
                wasNumber = isNumber;
                wordCounter++;
                output.append(aWord + " ");
            }
            return output.toString().trim();
        }
        catch (Exception e)
        {
            e.printStackTrace();
            return input;
        }
    }

    /**
     * This method will return an array of BigDecimal of all the numbers find in
     * a given string (ordered in sequencial appearance in string)
     *
     * @param theValue
     * @return BigDecimal array of all the numbers find in a given string
     */
    public static BigDecimal[] extractAllNumbers(String theValue)
    {

        BigDecimal[] output = null;
        try
        {

            Matcher matcher = NUMBEREXTACTPATTERN.matcher(theValue);
            int count = 0;
            while (matcher.find())
                count++;

            if (count > 0)
            {
                output = new BigDecimal[count];
                matcher.reset();
                count = 0;
                while (matcher.find())
                {
                    String someNumberStr = matcher.group(1);     // if you need this to be an int:
                    output[count++] = new BigDecimal(someNumberStr);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return output;
    }

    public static boolean isNumber(String num)
    {
        try
        {
            Double.parseDouble(num);
            return true;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    /*
     * Can go beyong the double limitation of isNumber above
     * by going through all the charaters
     */
    public static boolean isNumberSafe(String num)
    {
        boolean output = true;
        for (int i = 0; i < num.length(); i++)
        {
            char zz = num.charAt(i);
            if (zz < 48 || zz > 57)
            {
                output = false;
                break;
            }
        }
        return output;
    }

    public static String encodeXML(String s)
    {
        StringBuffer out = new StringBuffer();
        if (s != null)
        {
            for (int i = 0; i < s.length(); i++)
            {
                char c = s.charAt(i);
                if (c != 32 && (c > 122 || c < 48 || (c > 57 && c < 65) || (c > 90 && c < 97)))
                {
                    out.append("&#" + (int) c + ";");
                }
                else
                {
                    out.append(c);
                }
            }
        }
        return out.toString();
    }

    public static String encodeHTML(String s, boolean forceit)
    {
        StringBuffer out = new StringBuffer();
        if (s != null)
        {
            for (int i = 0; i < s.length(); i++)
            {
                char c = s.charAt(i);
                if (forceit || (c > 127 || c == '"' || c == '<' || c == '>'))
                {
                    out.append("&#" + (int) c + ";");
                }
                else
                {
                    out.append(c);
                }
            }
        }
        return out.toString();
    }

    public static String encodeHTML(String s)
    {
        return encodeHTML(s, false);
    }

    /**
     * Given a String as input this method will return a CRC value composed by
     * XORing each 4 byte to get a final double work (int) CRC values
     */
    public static int computeCRC(String body)
    {
        int xorValue = 0;

        int i = 0;
        while ((body != null) && (i + 4) < body.length())
        {

            int intVavlue = (((body.charAt(i)) & 0xFF) << 24) | (((body.charAt(i + 1)) & 0xFF) << 16) | (((body.charAt(i + 2)) & 0xFF) << 8) | ((body.charAt(i + 3)) & 0xFF);

            xorValue ^= intVavlue;

            i = i + 4;
        }

        return xorValue;

    }

    public static String decodeSafeUTF8(String content)
    {
        try
        {
            return URLDecoder.decode(content, "UTF-8");

        }
        catch (IllegalArgumentException ile)
        {
            if (content.endsWith("%"))
            {
                content = content.substring(0, content.lastIndexOf("%"));
                try
                {
                    return URLDecoder.decode(content, "UTF-8");
                }
                catch (Exception e2)
                {
                    e2.printStackTrace();
                }
            }
           
            return HtmlEncoder.UUDecodeFormal(content);

        }
        catch (Exception e)
        {
            e.printStackTrace();
            return HtmlEncoder.UUDecodeFormal(content);
        }
    }

    /**
     * This method returns a UUDEcoded string
     *
     * @param s The input string to decode
     * @return A decoded String
     */
    public static String UUDecode(String str)
    {

        StringBuffer result = new StringBuffer(str.length());
        int l = str.length();
        for (int i = 0; i < l; ++i)
        {
            char c = str.charAt(i);
            if (c == '$' && i + 4 < l)
            {

                char c1 = str.charAt(i + 1);
                char c2 = str.charAt(i + 2);
                char c3 = str.charAt(i + 3);
                char c4 = str.charAt(i + 4);

                if (isHexit(c1) && isHexit(c2) && isHexit(c3) && isHexit(c4))
                {
                    int tempInt = (hexit(c1) << 12) | (hexit(c2) << 8) | (hexit(c3) << 4) | hexit(c4);
                    char tempChar = (char) tempInt;
                    result.append(tempChar);
                    i += 4;
                }
                else result.append(c);

            }
            else if (c == '^' && i + 3 < l)
            {
                char c1 = str.charAt(i + 1);
                char c2 = str.charAt(i + 2);
                char c3 = str.charAt(i + 3);

                if (isHexit(c1) && isHexit(c2) && isHexit(c3))
                {
                    char tempChar = (char) ((hexit(c1) << 8) | (hexit(c2) << 4) | hexit(c3));
                    result.append(tempChar);
                    i += 3;
                }
                else result.append(c);

            }
            else if (c == '%' && i + 2 < l)
            {
                char c1 = str.charAt(i + 1);
                char c2 = str.charAt(i + 2);
                if (isHexit(c1) && isHexit(c2))
                {
                    result.append((char) ((hexit(c1) << 4) | hexit(c2)));
                    i += 2;
                }
                else result.append(c);
            }
            else if (c == '+') result.append(' ');
            else result.append(c);
        }

        return result.toString();
    }

    //Is it an hex number
    private static boolean isHexit(char c)
    {
        String legalChars = "0123456789abcdefABCDEF";
        return (legalChars.indexOf(c) != -1);
    }

    //Yes than put it in number format
    private static int hexit(char c)
    {
        if (c >= '0' && c <= '9') return c - '0';
        if (c >= 'a' && c <= 'f') return c - 'a' + 10;
        if (c >= 'A' && c <= 'F') return c - 'A' + 10;
        return 0;	// shouldn't happen, we're guarded by isHexit()
    }

    /**
     * This method returns a byte array uuencoded instead
     *
     * @param str The input string to decode
     * @return A byte array
     */
    public static byte[] UUDecodeByte(String str)
    {

        byte[] outByte = new byte[str.length()];

        int nIndex = 0;

        int l = str.length();

        for (int i = 0; i < l; ++i)
        {

            char c = str.charAt(i);

            if (c == '%' && i + 2 < l)
            {

                char c1 = str.charAt(i + 1);
                char c2 = str.charAt(i + 2);

                if (isHexit(c1) && isHexit(c2))
                {

                    outByte[nIndex++] = (byte) (hexit(c1) * 16 + hexit(c2));
                    i += 2;
                }
                else outByte[nIndex++] = (byte) c;
            }
            else if (c == '+') outByte[nIndex++] = 0x20;
            else outByte[nIndex++] = (byte) c;
        }

        byte[] finalByte = new byte[nIndex];

        for (int i = 0; i < nIndex; i++)
        {
            finalByte[i] = outByte[i];

        }

        return finalByte;
    }

    
    /**
     * This method returns a string uuencoded
     *
     * @param s The input string to encode
     * @return A UUEncoded String
     */
    public static String UUEncode(String s)
    {

        StringBuffer outString = new StringBuffer(s.length() * 2);

        if ((s != null) && (s.length() > 0))
        {
            for (int i = 0; i < s.length(); i++)
            {
                int c = (int) s.charAt(i);

                if ((c >= '0' && c <= '9')
                        || (c >= 'a' && c <= 'z')
                        || (c >= 'A' && c <= 'Z')) outString.append((char) c);

                else
                {
                    if ((c & 0xF000) != 0)
                    {
                        outString.append('$');
                        outString.append(Character.forDigit((c >> 12) & 0xF, 16));
                        outString.append(Character.forDigit((c >> 8) & 0xF, 16));
                    }
                    else if ((c & 0xF00) != 0)
                    {
                        outString.append('^');
                        outString.append(Character.forDigit((c >> 8) & 0xF, 16));
                    }
                    else outString.append('%');

                    outString.append(Character.forDigit((c >> 4) & 0xF, 16));
                    outString.append(Character.forDigit(c & 0xF, 16));

                }
            }
        }

        return outString.toString();
    }

    /**
     * This method returns an UUEncoded byte array
     *
     * @param s The input byte array to code
     * @return A string UUEncoded
     */
    public static String UUEncodeByte(byte[] s)
    {

        StringBuffer outString = new StringBuffer(s.length);

        if ((s != null) && (s.length > 0))
        {
            for (int i = 0; i < s.length; i++)
            {
                int c = (int) s[i] & 0xff;

                if ((c >= '0' && c <= '9')
                        || (c >= 'a' && c <= 'z')
                        || (c >= 'A' && c <= 'Z')) outString.append((char) c);
                else if (c == ' ') outString.append('+');
                else
                {
                    outString.append('%');
                    outString.append(Character.forDigit((c >> 4) & 0xF, 16));
                    outString.append(Character.forDigit(c & 0xF, 16));
                }
            }
        }

        return outString.toString();
    }

    public String toHexValue(byte[] byteData)
    {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < byteData.length; i++)
        {
            sb.append(Integer.toString((byteData[i] & 0xff) + 0x100, 16).substring(1));
        }
        return sb.toString();

    }

    /**
     * Simple encryption with a fix key - not powerfull to be use for lazy
     * encryption where high security is not important
     * @param toEncrypt
     * @return
     */
    public static String exorEncrypt(String toEncrypt)
    {
        return null;
    }

    /**
     * This method converts a string to hex with std encoding.
     * @param input, String, the string to be encoded
     * @return hex string
     */
    public static String toHexString(String input) throws IOException
    {
        StringBuffer result;
        int i;

        // alllocate result
        result
                = new StringBuffer();

        try
        {
            if (input != null)
            {
                byte[] bytes;

                i
                        = 0;

                bytes
                        = input.getBytes(StandardCharsets.ISO_8859_1.toString());

                // walk string
                while (i < bytes.length)
                {
                    String hex;

                    hex
                            = Integer.toHexString(bytes[i] & 0xff);

                    if (hex.length() < 2)
                    {
                        result.append('0');
                        result.append(hex);
                    }
                    else
                    {
                        result.append(hex);
                    }

                    i++;
                }

            }
        }
        catch (UnsupportedEncodingException unsupportedEncoding)
        {
            unsupportedEncoding.printStackTrace();
            throw new IOException(unsupportedEncoding.getMessage());
        }

        return result.toString();
    }

    /**
     * Another implementation of replace that doenst care about case sensitivity
     *
     * @param replaceThis The String to search for
     * @param withThis The string to replace with
     * @param inThis The input text.
     * @return The result of the replace
     */
    public static String replaceCaseInsensitive(String replaceThis, String withThis, String inThis)
    {

        int startIndex = 0;
        String firstPart;

        String lastPart;

        String result = inThis;

        String replaceThisLower = new String(replaceThis.toLowerCase());


        startIndex
                = result.toLowerCase().indexOf(replaceThisLower);

        while (startIndex != -1)
        {
            firstPart = result.substring(0, startIndex);
            lastPart
                    = result.substring(startIndex + replaceThisLower.length());
            result
                    = firstPart + withThis + lastPart;
            //lets not restart from beginning of string in case replaceThis = withThis.
            startIndex
                    = result.toLowerCase().indexOf(replaceThisLower, startIndex + withThis.length());
        }

        return result;
    }

// replace occurances of a string with a string within another string
// case IS significant
// sdb 000106
// needs to respect case!
// public static String replace( String replaceThis, String withThis, String inThis, boolean resepctCase )
    public static String replace(String replaceThis, String withThis, String inThis)
    {
        return replace(replaceThis, withThis, inThis, true);
    }

// replaces properties with values in text inThis
    public static String replace(HashMap wildcards, String inThis, boolean respectCase)
    {
        Iterator anIterator;
        String result;

        result
                = inThis;

        anIterator
                = wildcards.keySet().iterator();
        while (anIterator.hasNext())
        {
            String aWildcard;
            String aValue;

            aWildcard
                    = (String) anIterator.next();
            aValue
                    = (String) wildcards.get(aWildcard);

            result
                    = replace(aWildcard, aValue, result, respectCase);
        }

        return result;
    }

// handles case sensitivity
    public static String replace(String replaceThis, String withThis, String inThis, boolean respectCase)
    {
        return replace(replaceThis, withThis, inThis, respectCase, null);
    }

// this will do teh replacement, but only if
//	1) the character after the target string is the end of the string
//	2) the character after the target string is one of the characters in terminatingChars
    public static String replace(String replaceThis, String withThis, String inThis, boolean respectCase, String terminatingChars)
    {
        String result;
        int startIndex = 0;
        String targetText;


// check on case
        if (!respectCase)
        {
            replaceThis = replaceThis.toLowerCase();
            targetText
                    = inThis.toLowerCase();
        }
        else
        {
            targetText = inThis;
        }

// default result
        result = inThis;

        // while there are occurance, replace 'em
        // note this can really recurse if the replaceThis is in withThis!!!
        while (replaceFind(replaceThis, targetText, startIndex, terminatingChars) > -1)
        {
            String firstPart;
            String lastPart;

            int indexOfReplaceThis;

            // modifed to start next search at startIndex
            indexOfReplaceThis
                    = targetText.indexOf(replaceThis, startIndex);
            
            firstPart
                    = result.substring(0, indexOfReplaceThis);
            lastPart
                    = result.substring(indexOfReplaceThis + replaceThis.length());


            result
                    = firstPart + withThis + lastPart;

            startIndex
                    = firstPart.length() + withThis.length();

            // reset targetText, check on case
            if (!respectCase)
            {
                targetText = result.toLowerCase();
            }
            else
            {
                targetText = result;
            }

        }

        return result;
    }

// sdb 2002-02-13
// look for occurance of string findThis within inThis at startIndex
// optional ifFollowedByThis contains the charater that must follow findThis in inThis
    private static int replaceFind(String findThis, String inThis, int startIndex, String ifFollowedByThis)
    {
        int result = -1;

        // look for substring
        result
                = inThis.indexOf(findThis, startIndex);
        
        if ((result != -1) && (ifFollowedByThis != null))
        {
            int trailingCharPosition;

            // test length, if there is no next char, all is well
            trailingCharPosition
                    = result + findThis.length();

            // is it in range?
            if (trailingCharPosition < inThis.length())
            {
                char nextChar;

                // get the char
                nextChar
                        = inThis.charAt(trailingCharPosition);

                result
                        = ifFollowedByThis.indexOf(nextChar);
                
            }
            
        }

        return result;
    }

    public static String replaceLast(String input, String searchFor, String replaceWith)
    {
        int lastIndex = input.lastIndexOf(searchFor);
        if (lastIndex < 0) return input;
        String tail = input.substring(lastIndex).replaceFirst(searchFor, replaceWith);
        return input.substring(0, lastIndex) + tail;
    }

    /**
     * Return the CSS style value find in the CSS input given a param to parse.
     * Will return null if not present
     *
     * @param input A acess style
     * @param searchFor a style to search for
     * @return
     */
    public static String extractStyleValue(String cssStyle, String searchFor)
    {
        String output = null;
        if (cssStyle != null && searchFor != null)
        {
            cssStyle = cssStyle.toLowerCase();
            searchFor = searchFor.toLowerCase();
            int index1 = cssStyle.indexOf(searchFor);
            if (index1 > -1)
            {
                int index2 = cssStyle.indexOf(";", index1);
                if (index2 == -1) index2 = cssStyle.length();
                output = cssStyle.substring(index1 + searchFor.length(), index2).trim();
                if (output.endsWith(";")) output = output.substring(0, output.length() - 1).trim();
                if (output.startsWith(":")) output = output.substring(1, output.length()).trim();
            }
        }
        return output;
    }

    /**
     * This will remove all none alphabetic and nono-numerical characters in the
     * unput string
     *
     * @param input
     * @return
     */
    public static String filterOutAllNoneAlphNumeric(String s)
    {
        StringBuffer out = null;
        if (s != null)
        {
            out = new StringBuffer();
            for (int i = 0; i < s.length(); i++)
            {
                char c = s.charAt(i);
                if ((c >= '0' && c <= '9') || (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z'))
                {
                    out.append(c);
                }
            }
        }
        return out.toString();
    }

    /**
     * On a URL or POST value extract what is needed
     *
     * @param whichIs
     * @param fromHere
     * @return
     */
    public static String getLineArgument(String whichIs, String fromHere)
    {
        String output = null;
        int nIndex = fromHere.indexOf(whichIs);
        if (nIndex != -1)
        {
            int nIndex2 = fromHere.indexOf("&", nIndex + 1);
            int nIndex3 = fromHere.indexOf("?", nIndex + 1);
            if ((nIndex3 < nIndex2) && (nIndex3 != -1)) nIndex2 = nIndex3;
            if (nIndex2 == -1) nIndex2 = fromHere.length();
            if (nIndex2 != -1) output = fromHere.substring(nIndex + whichIs.length() + 1, nIndex2);
        }
        return output;

    }

    public static String replaceFirstOccurence(String replaceThis, String withThis, String inThis)
    {

        int startIndex = 0;
        String firstPart;
        String lastPart;
        String result = inThis;

        startIndex = result.indexOf(replaceThis);

        if (startIndex != -1)
        {
            firstPart = result.substring(0, startIndex);
            lastPart = result.substring(startIndex + replaceThis.length());
            result = firstPart + withThis + lastPart;
        }

        return result;
    }

    /**
     * This method is use to count the number of occurence of a String within
     * another string. This methos is case sensitive
     *
     * @param checkThis The string to count in inThis
     * @param inThis The string to search
     * @return The number of occurence of checkThis in inThis
     */
    public static int countStringInString(String checkThis, String inThis)
    {
        int result = 0;

        int nIndex = inThis.indexOf(checkThis);

        while (nIndex != -1)
        {
            result++;
            nIndex = inThis.indexOf(checkThis, nIndex + 1);
        }

        return result;
    }

    /**
     * Generate a random alpha value as a string
     *
     * @param length
     * @return
     */
    public static String generateRandomAlpha(int targetStringLength)
    {
        int leftLimit = 97; // letter 'a'
        int rightLimit = 122; // letter 'z'
        StringBuilder buffer = new StringBuilder(targetStringLength);
        for (int i = 0; i < targetStringLength; i++)
        {
            int randomLimitedInt = leftLimit + (int) (new Random().nextFloat() * (rightLimit - leftLimit));
            buffer.append((char) randomLimitedInt);
        }
        String generatedString = buffer.toString();

        return generatedString;
    }

    public static String UTF8toISO8859_1(String text)
    {
        try
        {

            ByteBuffer inputBuffer = ByteBuffer.wrap(text.getBytes(utf8charset));
            // decode UTF-8
            CharBuffer data = utf8charset.decode(inputBuffer);
            // encode ISO-8559-1
            ByteBuffer outputBuffer = iso88591charset.encode(data);
            byte[] outputData = outputBuffer.array();

            return new String(outputData, iso88591charset);

        }
        catch (Exception e)
        {
            throw new IllegalStateException(e);
        }
    }

    public static String ISO8859_1ToUTF8(String text)
    {
        try
        {

            ByteBuffer inputBuffer = ByteBuffer.wrap(text.getBytes(iso88591charset));
            // decode UTF-8
            CharBuffer data = iso88591charset.decode(inputBuffer);
            // encode ISO-8559-1
            ByteBuffer outputBuffer = utf8charset.encode(data);
            byte[] outputData = outputBuffer.array();

            return new String(outputData, utf8charset);

        }
        catch (Exception e)
        {
            throw new IllegalStateException(e);
        }
    }

    /**
     * Convert integer represented as a String to a String without heading zeros
     *
     * @param input
     * @return
     */
    public static String convertIntString2String(String input)
    {
        String output;
        if (input != null)
        {
            try
            {
                output = String.valueOf(Integer.parseInt(input));
            }
            catch (NumberFormatException e)
            {
                //dont care just return the value
                output = input;
            }
        }
        else output = null;
        return output;
    }
    
    public static String  getBookMarkType(int bmType) {
        switch (bmType) {
            case TextFormFieldType.CALCULATED:
                return "CALCULATED";
                
            case TextFormFieldType.CURRENT_DATE:
                return "CURRENT_DATE";
            
            case TextFormFieldType.CURRENT_TIME:
                return "CURRENT_TIME";
               
            case TextFormFieldType.DATE:
                return "DATE";
                
            case TextFormFieldType.NUMBER:
                return "NUMBER";   
            
            case TextFormFieldType.REGULAR:
                return "REGULAR";

            default:
                return "NO_TYPE";
        }
    }
        
    public static String  getContentControlType(int sdtType) {
        switch (sdtType) {
            case SdtType.BIBLIOGRAPHY:
                return "BIBLIOGRAPHY";
                
            case SdtType.BUILDING_BLOCK_GALLERY:
                return "BUILDING_BLOCK_GALLERY";
               
            case SdtType.CHECKBOX:
                return "CHECKBOX";
               
            case SdtType.CITATION:
                return "CITATION";
                
            case SdtType.COMBO_BOX:
                return "COMBO_BOX";
                
            case SdtType.DATE:
                return "DATE";
                
            case SdtType.DOC_PART_OBJ:
                return "DOC_PART_OBJ";
                
            case SdtType.DROP_DOWN_LIST:
                return "DROP_DOWN_LIST";
                
            case SdtType.EQUATION:
                return "EQUATION";
               
            case SdtType.GROUP:
                return "GROUP";
                
            case SdtType.NONE:
                return "NONE";
                
            case SdtType.PICTURE:
                return "PICTURE";
                
            case SdtType.PLAIN_TEXT:
                return "PLAIN_TEXT";
                
            case SdtType.RICH_TEXT:
                return "RICH_TEXT";
            default:
                return "NO_TYPE";
        }

    }
}
