package ca.muhc.scs.owordtools.service;

import ca.muhc.scs.owordtools.repository.AccountRepository;
import ca.muhc.scs.owordtools.model.Account;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class AccountService implements UserDetailsService {

    private final AccountRepository accountRepository;

    private final PasswordEncoder passwordEncoder;

    @Autowired
    public AccountService(AccountRepository accountRepository, PasswordEncoder passwordEncoder) {
        this.accountRepository = accountRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public Account save(Account account) {
        if (account.getId() <= 0) {
           // new User
            account.setPassword(passwordEncoder.encode(account.getPassword()));
        }
        accountRepository.save(account);
        return account;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        Account account = accountRepository.findOneByUserName(username);
        if (account == null) { 
            throw new UsernameNotFoundException("user not found");
        } else if (! account.getStatus().equals("ACTIVE")) {
            throw new UsernameNotFoundException("User account desactivated, please contact your manager for more details");
        }
        return createUser(account);
    }

    public Account getUserByUsername(String username) {
        return accountRepository.findOneByUserName(username);
    }

    public void signin(Account account) {
        SecurityContextHolder.getContext().setAuthentication(authenticate(account));
    }

    public Account get(String username) throws UsernameNotFoundException {
        Account account = accountRepository.findOneByUserName(username);
        if (account == null) {
            throw new UsernameNotFoundException("user not found");
        }
        return account;
    }
    
    @Transactional
    public Account get(long id) {
        return accountRepository.get(id);
    }

    public Account findOneByEmail(String email) {
        return accountRepository.findOneByEmail(email);
    }
    
    public boolean exists(String email) {
        return accountRepository.exists(email);
    }
    
    public List<Account> getAllByEmail(String email) {
        return accountRepository.getAllByEmail(email);
    }
    
    public List<Account> getAllByUserName(String userName) {
        return accountRepository.getAllByUserName(userName);
    }
    
    public Optional<Account> findById(int id) {
        return accountRepository.findById(id);
    }
    
    /* Private section*/
    private Authentication authenticate(Account account) {
        return new UsernamePasswordAuthenticationToken(createUser(account), null, Collections.singleton(createAuthority(account)));
    }

    private User createUser(Account account) {
        return new User(account.getUserName(), account.getPassword(), Collections.singleton(createAuthority(account)));
    }

    private GrantedAuthority createAuthority(Account account) {
        return new SimpleGrantedAuthority(account.getRole());
    }
    
    public DataTablesOutput<Account> findAll(DataTablesInput input) {
        return accountRepository.findAll(input);
    }
    
    public Page<Account> findAll(Pageable pageable) {
        return accountRepository.findAll(pageable);
    }
}
