/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package ca.muhc.scs.owordtools.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "DOCUMENT_MNGT")
public class LogDocumentMngt {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)

    @Column(name = "DOCUMENT_MNGT_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "DOCUMENT_MNGT_ACTION", nullable = false)
    private String documentMngtAction;

    @Column(name = "DOCUMENT_MNGT_LAST_STATUS", nullable = false)
    private String documentMngtLastStatus;

    @Column(name = "DOCUMENT_MNGT_NEW_STATUS", nullable = false)
    private String documentMngtNewStatus;

    @Column(name = "DOCUMENT_MNGT_DOCUMENT_ID", nullable = false)
    private String documentMngtDocumentId;
    
    @Column(name = "DOCUMENT_MNGT_DOCUMENT_MRN", nullable = false)
    private String documentMngtDocumentMrn;
    
    @Column(name = "DOCUMENT_MNGT_DOCUMENT_SITE", nullable = false)
    private String documentMngtDocumentSite;

    @Column(name = "DOCUMENT_MNGT_USER", nullable = false)
    private String documentMngtUser;

    @Column(name = "DOCUMENT_MNGT_DATE", nullable = false)
    private LocalDateTime documentMngtDate;

    @Column(name = "DOCUMENT_MNGT_DESCRIPTION", nullable = false)
    private String documentMngtDesc;

    public LogDocumentMngt(Integer id, String documentMngtAction, String documentMngtLastStatus, String documentMngtNewStatus, String documentMngtDocumentId, String documentMngtDocumentMrn, String documentMngtDocumentSite, String documentMngtUser, LocalDateTime documentMngtDate, String documentMngtDesc) {
        this.id = id;
        this.documentMngtAction = documentMngtAction;
        this.documentMngtLastStatus = documentMngtLastStatus;
        this.documentMngtNewStatus = documentMngtNewStatus;
        this.documentMngtDocumentId = documentMngtDocumentId;
        this.documentMngtDocumentMrn = documentMngtDocumentMrn;
        this.documentMngtDocumentSite = documentMngtDocumentSite;
        this.documentMngtUser = documentMngtUser;
        this.documentMngtDate = documentMngtDate;
        this.documentMngtDesc = documentMngtDesc;
    }

    public LogDocumentMngt() {
        this(null, "", "", "", "", "", "", "", null, "");
    }
            

}
