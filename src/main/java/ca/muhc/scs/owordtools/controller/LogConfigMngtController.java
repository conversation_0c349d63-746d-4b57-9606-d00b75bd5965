package ca.muhc.scs.owordtools.controller;

import ca.muhc.scs.owordtools.model.LogConfigMngt;
import ca.muhc.scs.owordtools.service.LogConfigMngtService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;

import java.security.Principal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Controller
public class LogConfigMngtController {

    private static final String RETURN_SIGN_IN_PAGE = "signin/signin";


    private final LogConfigMngtService logConfigMngtService;

    public LogConfigMngtController(LogConfigMngtService logConfigMngtService) {
        this.logConfigMngtService = logConfigMngtService;
    }

    @ModelAttribute("module")
    String module() {
        return "adminactivities";
    }


    @PreAuthorize("hasAnyRole('ROLE_SUP_ADMIN', 'ROLE_ADMIN')")
    @GetMapping("configactivities")
    String getLastActivities(Principal principal, Model model, @Valid LogConfigMngt logConfigMngt, @RequestParam("page")
                             Optional<Integer> page,
                             @RequestParam("size") Optional<Integer> size) {
        int currentPage = page.orElse(1);
        int pageSize = size.orElse(1000);

        long start = System.currentTimeMillis();
        log.info("Begin get last activité");
        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        Page<LogConfigMngt> logConfigMngtPage = logConfigMngtService.getLastActivitiesPages(PageRequest.of(currentPage - 1, pageSize));
        model.addAttribute("logConfigMngtList", logConfigMngtPage);
        log.info("--> Return all LogConfigMngt(size:{}).", logConfigMngtPage.getContent().size());

        int totalPages = logConfigMngtPage.getTotalPages();
        if (totalPages > 0) {
            List<Integer> pageNumbers = IntStream.rangeClosed(1, totalPages)
                    .boxed()
                    .collect(Collectors.toList());
            model.addAttribute("totalPages", pageNumbers);
        }

        return "activities/configActivitiesList";
    }


}
