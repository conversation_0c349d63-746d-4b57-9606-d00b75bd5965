package ca.muhc.scs.owordtools.controller;

import ca.muhc.scs.osearch.dto.TemplateDTO;
import ca.muhc.scs.osearch.model.Template;
import ca.muhc.scs.osearch.service.TemplateService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

//@Controller
@RestController
@RequestMapping("api/templates")
public class TemplateRestController {

    private static final Logger logger = LoggerFactory.getLogger(TemplateRestController.class);

    @Autowired
    private TemplateService templateService;

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @PostMapping(value = "/page", consumes = {"application/json"})
    public ResponseEntity<DataTablesOutput> getPage(@RequestBody JsonNode jsonPagination) {    //DataTablesInput input, //,Column[] columns

        long start = System.currentTimeMillis();
        logger.info("Begin get page templates");
        
        logger.info("-> Templates request: as Json:{}:", jsonPagination.toString());
        ObjectMapper jsonObjectMapper = new ObjectMapper();
        DataTablesInput pageCriteriaReq = null;
        try {
            pageCriteriaReq = jsonObjectMapper.treeToValue(jsonPagination, DataTablesInput.class);
            logger.info("-- Request: Draw:{}, start:{}, length:{}.",
                    pageCriteriaReq.getDraw(), pageCriteriaReq.getStart(), pageCriteriaReq.getLength());

        } catch (JsonProcessingException jpException) {
            logger.error("Could not convert json request body josn into paginationObject; {}", jpException);
        }
    
        DataTablesOutput<Template> aPage = templateService.findAll(pageCriteriaReq);

        logger.info("-- RecordsTotal (templates) set to {}", aPage.getRecordsTotal());
        
        List<TemplateDTO> templateList = new ArrayList<>();
        for (Template t : aPage.getData()) {

            TemplateDTO templateDto = new TemplateDTO(t.getTemplateId(), t.getOwordDocType().getDocTypeCode(), null, null, t.getTemplateStatus(), 
                    t.getAllowDraft(), t.getAllowPreliminary(), t.getAllowPrinting(), 
                    t.getAlias(), t.getAliasDescription(), t.getComments(), null, 
                    t.getPrintOnly(), t.getDisablePrintingDraft(), t.getAllowCopyPrevious(), 
                    t.getPermission(), t.getAllowWorkgroup(), t.getCopyPreviousByDefault(), t.getCopyPreviousMandatory(), t.getReviewable(), 
                    t.getEditableByArchivist(), null, t.getAllowNoVisitID(), t.getAllowCopyFromPreviousForUser()) ;
            templateList.add(templateDto) ; 
        }
        
        DataTablesOutput<TemplateDTO> aPageDTO = new DataTablesOutput<>();
        aPageDTO.setData(templateList);
        
        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;
        
        logger.info("End get page templates [" + timeElapsed + "]");

        return new ResponseEntity(aPageDTO, HttpStatus.OK);
    }

}
