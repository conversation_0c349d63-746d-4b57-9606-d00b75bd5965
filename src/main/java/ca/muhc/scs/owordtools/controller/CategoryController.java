package ca.muhc.scs.owordtools.controller;

import ca.muhc.scs.owordtools.model.LogConfigMngt;
import ca.muhc.scs.owordtools.service.LogConfigMngtService;
import ca.muhc.scs.osearch.service.CategoryService;
import ca.muhc.scs.osearch.model.Category;
import org.springframework.web.bind.annotation.*;
import java.security.Principal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.Errors;

/**
 *
 * <AUTHOR>
 */
@Controller
public class CategoryController {
    
    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);

    private static final String TABLE = "CATEGORY";
    private static final String RETURN_SIGN_IN_PAGE = "signin/signin";
    private static final String RETURN_CATEGORIE_EDIT_PAGE = "config/category/categoryEdit";

    private final CategoryService categoryService;
    private final LogConfigMngtService logConfigMngtService;

    public CategoryController(CategoryService categoryService, LogConfigMngtService logConfigMngtService) {
        this.categoryService = categoryService;
        this.logConfigMngtService = logConfigMngtService;
    }

    @ModelAttribute("module")
    String module() {
        return "categories";
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("category/new")
    String addNew(Principal principal, Model model) {
        long start = System.currentTimeMillis();
        logger.info("Begin add new category");
        
        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        model.addAttribute("category", new Category());
        
        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;
        
        logger.info("End add new category [" + timeElapsed + "]");
        
        return RETURN_CATEGORIE_EDIT_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("category/edit")
    String get(Principal principal, Model model, @RequestParam("id") Integer id) {
        
        long start = System.currentTimeMillis();
        logger.info("Begin Access to edit category : " + id);
        
        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        Category aCategory = categoryService.get(id).get();
        model.addAttribute("category", aCategory);
        
        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;
        
        logger.info("End Access to edit category : " + id + " [" + timeElapsed + "]");
        
        return RETURN_CATEGORIE_EDIT_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @PostMapping("category/save")
    String save(Principal principal, Model model, @Valid Category category, Errors errors) {
        
        long start = System.currentTimeMillis();
        logger.info("Begin save category : " + category.getId() + "-" + category.getNameEn());

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }
        
        if (errors.hasErrors()) {
            return RETURN_CATEGORIE_EDIT_PAGE;
        }

        Category newCategory = categoryService.save(category);
        
        logger.info("--> category ('{}') successfully saved", category.getNameEn());
        
        String description;
        String configMngtAction;
        if (category.getId() > 0)  {
            description = "[CAUP01] Update category (" + category.getId() + ")";
            configMngtAction = "CAUP01";
        }  else {
            description = "[CAIN01] Insert category (" + newCategory.getId() + ")";
            configMngtAction = "CAIN01";
        }     
        
        LogConfigMngt logConfigMngt = new LogConfigMngt(null, TABLE, configMngtAction, principal.getName(), LocalDateTime.now(), newCategory.getId().toString(), description);
        logConfigMngtService.save(logConfigMngt);
        
        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;
        
        logger.info("End save category : " + category.getId() + "-" + category.getNameFr() + "[" + timeElapsed + "]");
        
        return "redirect:../categories";
    }
    
    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("categories")
    String getAll(Principal principal, Model model, @Valid Category category,
    @RequestParam("page") Optional<Integer> page,
    @RequestParam("size") Optional<Integer> size) {

        int currentPage = page.orElse(1);
        int pageSize = size.orElse(1000);
        
        long start = System.currentTimeMillis();
        logger.info("Begin get all categories");
        
        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        Page<Category> categoryPage = categoryService.findCategoriesPageable(PageRequest.of(currentPage - 1, pageSize));
        //List<Category> categories = categoryService.getAll();
        model.addAttribute("categories", categoryPage);
        model.addAttribute("currentPage", currentPage);
        logger.info("--> Return all categories (size:{}).", categoryPage.getContent().size());
        
        int totalPages = categoryPage.getTotalPages();
        if (totalPages > 0) {
            List<Integer> pageNumbers = IntStream.rangeClosed(1, totalPages)
                    .boxed()
                    .collect(Collectors.toList());
            model.addAttribute("totalPages", pageNumbers);
        }



        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;
        
        logger.info("End get all categories [" + timeElapsed + "]");
        
        return "config/category/categoryList";
    }
    
}
