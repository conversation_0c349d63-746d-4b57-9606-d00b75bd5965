package ca.muhc.scs.owordtools.controller;

import ca.muhc.scs.owordtools.model.LogConfigMngt;
import ca.muhc.scs.owordtools.service.LogConfigMngtService;
import ca.muhc.scs.osearch.model.Category;
import ca.muhc.scs.osearch.model.Classification;
import ca.muhc.scs.osearch.model.DocType;
import ca.muhc.scs.osearch.model.DocTypeForm;
import ca.muhc.scs.osearch.service.CategoryService;
import ca.muhc.scs.osearch.service.ClassificationService;
import ca.muhc.scs.osearch.service.DocTypeService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.Errors;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Controller
public class DocTypeController {

    private static final String TABLE = "DOC_TYPE";
    private static final String RETURN_SIGN_IN_PAGE = "signin/signin";
    private static final String RETURN_DOCTYPE_EDIT_PAGE = "config/doctype/docTypeEdit";

    private static final Logger logger = LoggerFactory.getLogger(DocTypeController.class);

    private final DocTypeService docTypeService;
    private final CategoryService categoryService;
    private final ClassificationService classificationService;
    private final LogConfigMngtService logConfigMngtService;

    public DocTypeController(DocTypeService docTypeService, CategoryService categoryService, ClassificationService classificationService, LogConfigMngtService logConfigMngtService) {
        this.docTypeService = docTypeService;
        this.categoryService = categoryService;
        this.classificationService = classificationService;
        this.logConfigMngtService = logConfigMngtService;
    }

    @ModelAttribute("module")
    String module() {
        return "doctypes";
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("doctype/new")
    String addNew(Principal principal, Model model, final DocTypeForm docTypeForm) {

        long start = System.currentTimeMillis();
        logger.info("Begin add new doctype");

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        List<Category> categoriesFr = categoryService.getAllFr();
        List<Category> categoriesEn = categoryService.getAllEn();
        List<Classification> classificationsFr = classificationService.getAllFr();
        List<Classification> classificationsEn = classificationService.getAllEn();

        model.addAttribute("docTypeForm", docTypeForm);
        model.addAttribute("categoriesFr", categoriesFr);
        model.addAttribute("categoriesEn", categoriesEn);
        model.addAttribute("classificationsFr", classificationsFr);
        model.addAttribute("classificationsEn", classificationsEn);
        model.addAttribute("isnew", true);
        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End add new doctype [" + timeElapsed + "]");

        return RETURN_DOCTYPE_EDIT_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("doctype/edit")
    String get(Principal principal, Model model, @RequestParam("id") String id) {

        long start = System.currentTimeMillis();
        logger.info("Begin Access to edit docType : " + id);

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        DocType aDocType = docTypeService.get(id);
        DocTypeForm docTypeForm = new DocTypeForm(aDocType.getDocTypeCode(), aDocType.getNameEn(), aDocType.getDescriptionEn(),
                aDocType.getNameFr(), aDocType.getDescriptionFr(), null, null);
        docTypeForm.setCategoryId(aDocType.getOwordCategory().getId());
        docTypeForm.setClassificationCode(aDocType.getOwordClassification() !=null ? aDocType.getOwordClassification().getCode() : null);

        List<Category> categoriesFr = categoryService.getAllFr();
        List<Category> categoriesEn = categoryService.getAllEn();
        List<Classification> classificationsFr = classificationService.getAllFr();
        List<Classification> classificationsEn = classificationService.getAllEn();

        model.addAttribute("docTypeForm", docTypeForm);
        model.addAttribute("categoriesFr", categoriesFr);
        model.addAttribute("categoriesEn", categoriesEn);
        model.addAttribute("classificationsFr", classificationsFr);
        model.addAttribute("classificationsEn", classificationsEn);
        model.addAttribute("isnew", false);

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End Access to edit docType : " + id + "[" + timeElapsed + "]");

        return RETURN_DOCTYPE_EDIT_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @PostMapping("docType/save")
    String save(Principal principal, Model model, @Valid final DocTypeForm docTypeForm, Errors errors, final HttpServletRequest req) {
        long start = System.currentTimeMillis();
        logger.info("Begin save docType : " + docTypeForm.getIddoc() + "-" + docTypeForm.getNameEn());

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        // Récupérer la valeur de isnew depuis la requête
        boolean isNew = req.getParameter("isnew") != null && req.getParameter("isnew").equals("true");

        // Méthode utilitaire pour ajouter les attributs communs au modèle
        if (errors.hasErrors()) {
            addCommonModelAttributes(model, isNew);
            logger.error("Validation errors occurred while saving the docType.");
            return RETURN_DOCTYPE_EDIT_PAGE;
        }

        DocType existingDocType = docTypeService.get(docTypeForm.getIddoc());

        // Cas où on tente de créer un nouveau DocType avec un code qui existe déjà
        if (isNew && existingDocType != null && docTypeForm.getIddoc() != null && !docTypeForm.getIddoc().isEmpty()) {
            addCommonModelAttributes(model, true);
            model.addAttribute("docTypeExists", true);
            model.addAttribute("existingDocType", existingDocType);
            logger.warn("Attempted to create a DocType with an existing code: " + docTypeForm.getIddoc());
            return RETURN_DOCTYPE_EDIT_PAGE;
        }

        try {
            DocType docType;
            String logAction, logDescription;

            if (isNew) {
                // Cas de création - nouvelle entité
                docType = createNewDocType(docTypeForm);
                logAction = "DTIN01";
                logDescription = "[DTIN01] Insert document type (" + docType.getDocTypeCode() + ")";
            } else {
                // Cas de modification - entité existante
                if (existingDocType == null) {
                    logger.error("DocType with code " + docTypeForm.getIddoc() + " not found for update.");
                    model.addAttribute("error", "DocType not found. Please select a valid DocType.");
                    return RETURN_DOCTYPE_EDIT_PAGE;
                }

                docType = updateExistingDocType(existingDocType, docTypeForm, model);
                if (docType == null) {
                    return RETURN_DOCTYPE_EDIT_PAGE; // Une erreur s'est produite
                }

                logAction = "DTUP01";
                logDescription = "[DTUP01] Update document type (" + docType.getDocTypeCode() + ")";
            }

            // Enregistrer l'action dans les logs
            LogConfigMngt logConfigMngt = new LogConfigMngt(null, TABLE, logAction, principal.getName(),
                    LocalDateTime.now(), docType.getDocTypeCode(), logDescription);
            logConfigMngtService.save(logConfigMngt);

            logger.info("--> docType ('{}') successfully saved", docType.getNameEn());
        } catch (Exception e) {
            logger.error("Error saving docType: " + e.getMessage(), e);
            model.addAttribute("error", "An unexpected error occurred. Please try again.");
            addCommonModelAttributes(model, isNew);
            return RETURN_DOCTYPE_EDIT_PAGE;
        }

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;
        logger.info("End save docType : " + docTypeForm.getIddoc() + "-" + docTypeForm.getNameEn() + "[" + timeElapsed + "]");

        return "redirect:../doctypes";
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("doctypes")
    String getAll(Principal principal, Model model, @Valid DocType docType) {

        long start = System.currentTimeMillis();
        logger.info("Begin get all doctypes");

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End get all categories [" + timeElapsed + "]");

        return "config/doctype/docTypeList";
    }

    // Méthode utilitaire pour déterminer si c'est une nouvelle création
    private boolean isNew(DocTypeForm docTypeForm) {
        // Si on est en mode création, le formulaire est marqué comme nouveau
        return docTypeForm.getIddoc() != null && !docTypeForm.getIddoc().isEmpty() &&
                docTypeService.get(docTypeForm.getIddoc()) != null;
    }

    /**
     * Ajoute les attributs communs au modèle
     */
    private void addCommonModelAttributes(Model model, boolean isNew) {
        List<Category> categoriesFr = categoryService.getAllFr();
        List<Category> categoriesEn = categoryService.getAllEn();
        List<Classification> classificationsFr = classificationService.getAllFr();
        List<Classification> classificationsEn = classificationService.getAllEn();

        model.addAttribute("categoriesFr", categoriesFr);
        model.addAttribute("categoriesEn", categoriesEn);
        model.addAttribute("classificationsFr", classificationsFr);
        model.addAttribute("classificationsEn", classificationsEn);
        model.addAttribute("isnew", isNew);
    }

    /**
     * Crée un nouveau DocType à partir du formulaire
     */
    private DocType createNewDocType(DocTypeForm docTypeForm) {
        DocType newDocType = new DocType(
                docTypeForm.getIddoc(),
                docTypeForm.getNameEn(),
                docTypeForm.getDescriptionEn(),
                docTypeForm.getNameFr(),
                docTypeForm.getDescriptionFr()
        );

        // Ajouter la catégorie
        Optional<Category> category = categoryService.get(docTypeForm.getCategoryId());
        if (category.isPresent()) {
            newDocType.setOwordCategory(category.get());
        }

        // Ajouter la classification
        Classification classification = classificationService.get(docTypeForm.getClassificationCode());
       // if (classification != null) {
            newDocType.setOwordClassification(classification);
        //}else{
        //    newDocType.setOwordClassification(null);
      //  }

        return docTypeService.save(newDocType);
    }

    /**
     * Met à jour un DocType existant avec les données du formulaire
     * Retourne null en cas d'erreur
     */
    private DocType updateExistingDocType(DocType existingDocType, DocTypeForm docTypeForm, Model model) {
        // Mettre à jour les propriétés de base
        existingDocType.setNameEn(docTypeForm.getNameEn());
        existingDocType.setNameFr(docTypeForm.getNameFr());
        existingDocType.setDescriptionEn(docTypeForm.getDescriptionEn());
        existingDocType.setDescriptionFr(docTypeForm.getDescriptionFr());

        // Mettre à jour la catégorie
        Optional<Category> theCategory = categoryService.get(docTypeForm.getCategoryId());
        if (theCategory.isEmpty()) {
            logger.error("Category with ID " + docTypeForm.getCategoryId() + " not found.");
            model.addAttribute("error", "Category not found. Please select a valid category.");
            return null;
        }
        existingDocType.setOwordCategory(theCategory.get());

        // Mettre à jour la classification
        Classification theClassification = classificationService.get(docTypeForm.getClassificationCode());
        if (theClassification == null) {
            logger.error("Classification with code " + docTypeForm.getClassificationCode() + " not found.");
           // model.addAttribute("error", "Classification not found. Please select a valid classification.");
           // return null;
        }
        existingDocType.setOwordClassification(theClassification);

        return docTypeService.save(existingDocType);
    }

}
