package ca.muhc.scs.owordtools.controller;

import ca.muhc.scs.owordtools.model.LogConfigMngt;
import ca.muhc.scs.owordtools.service.LogConfigMngtService;
import ca.muhc.scs.owordtools.utils.BookMark;
import ca.muhc.scs.owordtools.utils.MSWordTool;
import ca.muhc.scs.osearch.dto.BookMarkDTO;
import ca.muhc.scs.osearch.dto.ContentControlDTO;
import ca.muhc.scs.osearch.dto.TemplateDTO;
import ca.muhc.scs.osearch.model.*;
import ca.muhc.scs.osearch.service.*;
import jakarta.validation.Valid;
import org.apache.poi.xwpf.usermodel.XWPFAbstractSDT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.Errors;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.Principal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

@Controller
public class TemplateController {

    @Value("${app.site}")
    private String appSite;

    private static final Logger logger = LoggerFactory.getLogger(TemplateController.class);

    private static final String TABLE = "TEMPLATE";
    private static final String FORMAT_DATE = "yyyyMMddhhmmss";
    private static final String SIGNIN_PAGE = "signin/signin";
    private static final String TEMPLATE_EDIT_PAGE = "config/template/templateEdit";
    private static final String TEMPLATE_HISTORY_PAGE = "config/template/templateHistory";
    private static final String TEMPLATE_CHECK_PAGE = "config/template/templateCheck";
    private static final String LIST_ALL_BOOKMARKS = "bookmarks";
    private static final String LIST_DOUBLE_BOOKMARKS = "doubleBookmarks";
    private static final String LIST_ALL_CONTENTCONTORLS = "contentcontrols";
    private static final String TEMPLATE_ID = "templateid";
    private static final String TEMPLATE = "templateobj";
    private static final String RETURN_SIGN_IN_PAGE = "signin/signin";
    private static final String LIST_TEMPLATES = "listtemplates";
    private static final String ERROR_MESSAGE = "error_message";
    private static final String TEMPLATE_ACTIVE = "ACTIVE";
    private static final String TEMPLATE_DELETED = "DELETED";
    private static final String TEMPLATE_UPDATE_01 = "TMPUP01";
    private static final String TEMPLATE_UPDATE_02 = "TMPUP02";
    private static final String TEMPLATE_UPDATE_03 = "TMPUP03";
    private static final String TEMPLATE_NEW_01 = "TMPNW01";


    private final DocTypeService docTypeService;
    private final ClassificationService classificationService;
    private final TemplateService templateService;
    private final ServiceCodeService serviceCodeService;
    private final DocumentInstanceService documentInstanceService;
    private final EncounterTypeCodeService encounterTypeCodeService;
    private final LogConfigMngtService logConfigMngtService;

    public TemplateController(TemplateService templateService,
                              DocTypeService docTypeService,
                              ClassificationService classificationService,
                              ServiceCodeService serviceCodeService,
                              DocumentInstanceService documentInstanceService,
                              EncounterTypeCodeService encounterTypeCodeService,
                              LogConfigMngtService logConfigMngtService
    ) {
        this.templateService = templateService;
        this.docTypeService = docTypeService;
        this.classificationService = classificationService;
        this.serviceCodeService = serviceCodeService;
        this.documentInstanceService = documentInstanceService;
        this.encounterTypeCodeService = encounterTypeCodeService;
        this.logConfigMngtService = logConfigMngtService;
    }

    @ModelAttribute("module")
    String module() {
        return "templates";
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/new")
    String addNew(Principal principal, Model model) {

        long start = System.currentTimeMillis();
        logger.info("Begin add new template");

        if (principal == null) {
            return SIGNIN_PAGE;
        }

        /* test UserDetails */
        UserDetails userDetails = (UserDetails) SecurityContextHolder.getContext().
                getAuthentication().getPrincipal();

        userDetails.getAuthorities();
        List<DocType> docTypeList= new ArrayList<>();
  if(appSite.equals("CUSM")){
      docTypeList = templateService.getListOfOrphenDocType();
  }else{
       docTypeList = docTypeService.getAll();
  }

        List<Classification> classificationListFr = classificationService.getAllFr();
        List<Classification> classificationListEn = classificationService.getAllEn();
        List<ServiceCode> serviceCodeList = serviceCodeService.getAll();
        List<EncounterTypeCode> encounterTypeCodeList = encounterTypeCodeService.getAll();
        List<DocumentInstance> documentInstanceList = documentInstanceService.getAll();

        model.addAttribute("templateForm", new TemplateForm());
        model.addAttribute("documentInstanceList", documentInstanceList);
        model.addAttribute("encounterTypeCodeList", encounterTypeCodeList);
        model.addAttribute("docTypeList", docTypeList);
        model.addAttribute("classificationListFr", classificationListFr);
        model.addAttribute("classificationListEn", classificationListEn);
        model.addAttribute("serviceCodeList", serviceCodeList);

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End add new template [" + timeElapsed + "]");

        return TEMPLATE_EDIT_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/edit")
    String get(Principal principal, Model model, @RequestParam("id") Long id) {

        long start = System.currentTimeMillis();
        logger.info("Begin Access to edit template : " + id);

        if (principal == null) {
            return SIGNIN_PAGE;
        }

        List<DocType> docTypeList = docTypeService.getAll();
        List<Classification> classificationListFr = classificationService.getAllFr();
        List<Classification> classificationListEn = classificationService.getAllEn();
        List<ServiceCode> serviceCodeList = serviceCodeService.getAll();
        List<EncounterTypeCode> encounterTypeCodeList = encounterTypeCodeService.getAll();
        List<DocumentInstance> documentInstanceList = documentInstanceService.getAll();

        Template aTemplate = templateService.get(id);

        TemplateForm templateForm = new TemplateForm();
        templateForm.setTemplateId(aTemplate.getTemplateId());
        templateForm.setTemplateStatus(aTemplate.getTemplateStatus());
        templateForm.setPermission(aTemplate.getPermission());
        templateForm.setDocType(aTemplate.getOwordDocType().getDocTypeCode());
        templateForm.setClassification(aTemplate.getOwordClassification() != null ? aTemplate.getOwordClassification().getCode() : null);

        if (null != aTemplate.getOwordServiceCode()) {
            templateForm.setServiceCode(aTemplate.getOwordServiceCode().getService());
        } else {
            templateForm.setServiceCode("");
        }

        templateForm.setAllowDraft(aTemplate.getAllowDraft());
        templateForm.setAllowPreliminary(aTemplate.getAllowPreliminary());
        templateForm.setAllowPrinting(aTemplate.getAllowPrinting());
        templateForm.setComment(aTemplate.getComments());
        templateForm.setAlias(aTemplate.getAlias());
        templateForm.setAliasDescription(aTemplate.getAliasDescription());
        templateForm.setCopyPreviousByDefault(aTemplate.getCopyPreviousByDefault());
        templateForm.setCopyPreviousMandatory(aTemplate.getCopyPreviousMandatory());
        templateForm.setAllowCopyPrevious(aTemplate.getAllowCopyPrevious());
        templateForm.setAllowCopyPreviousUser(aTemplate.getAllowCopyFromPreviousForUser());
        templateForm.setPrintOnly(aTemplate.getPrintOnly());
        templateForm.setDisablePrintingDraft(aTemplate.getDisablePrintingDraft());
        templateForm.setAllowWorkGroup(aTemplate.getAllowWorkgroup());
        templateForm.setReviewable(aTemplate.getReviewable());
        templateForm.setAllowNoVisit(aTemplate.getAllowNoVisitID());
        templateForm.setEditableByArchivist(aTemplate.getEditableByArchivist());
        templateForm.setDocumentInstance(aTemplate.getOwordDocumentInstance().getInstanceCode());
        templateForm.setReplaceTemplate("0");
        model.addAttribute("documentInstanceList", documentInstanceList);
        model.addAttribute("templateForm", templateForm);
        model.addAttribute("docTypeDescFr", aTemplate.getOwordDocType().getDocTypeCode() + '-' + aTemplate.getOwordDocType().getDescriptionFr());
        model.addAttribute("docTypeDescEn", aTemplate.getOwordDocType().getDocTypeCode() + '-' + aTemplate.getOwordDocType().getDescriptionEn());
        model.addAttribute("encounterTypeCodeList", encounterTypeCodeList);
        model.addAttribute("docTypeList", docTypeList);
        model.addAttribute("classificationListEn", classificationListEn);
        model.addAttribute("classificationListFr", classificationListFr);
        model.addAttribute("serviceCodeList", serviceCodeList);

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End Access to edit template : " + id + " [" + timeElapsed + "]");

        return TEMPLATE_EDIT_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/delete")
    String deleteTemplateById(Principal principal, Model model,
                              @RequestParam("id") String id, @RequestParam("docType") String docType) {

        long start = System.currentTimeMillis();
        logger.info("Begin Activate template : " + id + " [" + docType + "]");

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        try {
            Template template = templateService.get(Long.parseLong(id));
            if (template != null) {
                template.setTemplateStatus(TEMPLATE_DELETED);
                templateService.save(template);
                String description = "Desactivate template " + template.getOwordDocType().getDocTypeCode() + " [ID:" + template.getTemplateId() + "]";
                String configMngtAction = "tmpDeact";
                LogConfigMngt logConfigMngt;
                logConfigMngt = new LogConfigMngt(null, TABLE, configMngtAction, principal.getName(), LocalDateTime.now(), Long.toString(template.getTemplateId()), description);
                logConfigMngtService.save(logConfigMngt);
            }

            // check if exist active template
            List<Template> activeTemplates = templateService.getActiveTemplateBy(docType);
            if (activeTemplates.size() == 1) {
                List<TemplateDTO> templates;

                templates = templateService.findTemplateVersion(docType);

                model.addAttribute("doctype", docType);
                model.addAttribute(LIST_TEMPLATES, templates);
                model.addAttribute("nbrActiveTemplate", templateService.countActiveTemplateBy(docType));

                long finish = System.currentTimeMillis();
                long timeElapsed = finish - start;

                logger.info("End delete template : " + id + " [" + docType + "]" + "[" + timeElapsed + "]");
                return TEMPLATE_HISTORY_PAGE;
            }
            List<TemplateDTO> templates;

            templates = templateService.getActiveTemplate();

            model.addAttribute(LIST_TEMPLATES, templates);

            long finish = System.currentTimeMillis();
            long timeElapsed = finish - start;

            logger.info("End delete template : " + id + " [" + docType + "]" + "[" + timeElapsed + "]");

        } catch (NumberFormatException e) {
            model.addAttribute(ERROR_MESSAGE, e.getMessage());
        }
        return "config/template/templateList";
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/activate")
    String activateTemplateById(Principal principal, Model model,
                                @RequestParam("id") String id,
                                @RequestParam("docType") String docType,
                                @RequestParam("status") String status) {

        long start = System.currentTimeMillis();
        logger.info("Begin Activate template : " + id + " [" + docType + "|" + status + "]");

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        try {
            if (status.equals(TEMPLATE_DELETED)) {
                // Activate Template
                List<Template> activeTemplates = templateService.getActiveTemplateBy(docType);
                if (activeTemplates.size() == 1) {
                    // Desactivate the tempplate
                    Template template = activeTemplates.get(0);
                    template.setTemplateStatus(TEMPLATE_DELETED);
                    templateService.save(template);
                    String description = "Desactivate template " + template.getOwordDocType().getDocTypeCode() + " [ID:" + template.getTemplateId() + "]";
                    String configMngtAction = "tmpDeact";
                    LogConfigMngt logConfigMngt;
                    logConfigMngt = new LogConfigMngt(null, TABLE, configMngtAction, principal.getName(), LocalDateTime.now(), Long.toString(template.getTemplateId()), description);
                    logConfigMngtService.save(logConfigMngt);

                    template = templateService.get(Long.parseLong(id));
                    template.setTemplateStatus(TEMPLATE_ACTIVE); // Activate the 
                    templateService.save(template);

                    description = "Activate template (" + template.getOwordDocType().getDocTypeCode() + ") : " + template.getTemplateId();
                    configMngtAction = "tmpAct";

                    logConfigMngt = new LogConfigMngt(null, TABLE, configMngtAction, principal.getName(), LocalDateTime.now(), Long.toString(template.getTemplateId()), description);
                    logConfigMngtService.save(logConfigMngt);


                }
            }

            List<TemplateDTO> templates;

            templates = templateService.findTemplateVersion(docType);

            model.addAttribute("doctype", docType);
            model.addAttribute(LIST_TEMPLATES, templates);
            model.addAttribute("nbrActiveTemplate", templateService.countActiveTemplateBy(docType));

        } catch (NumberFormatException e) {
            model.addAttribute(ERROR_MESSAGE, e.getMessage());
        }

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End activate template : " + id + " [" + docType + "|" + status + "]" + "[" + timeElapsed + "]");

        return TEMPLATE_HISTORY_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/download")
    HttpEntity<ByteArrayResource> getTemplateContent(Principal principal, Model model, @RequestParam("id") Long id) {

        long start = System.currentTimeMillis();
        logger.info("Begin download template : " + id);

        HttpHeaders header;
        Template template = templateService.get(id);
        SimpleDateFormat inputFormat = new SimpleDateFormat(FORMAT_DATE);
        String finalDateStr = inputFormat.format(Date.from(template.getCreateDate().toInstant()));
        String outPutFileName = "Template_" + template.getTemplateId() + "_" + template.getOwordDocType().getDocTypeCode() + "_" + finalDateStr + ".doc";
        header = new HttpHeaders();
        header.setContentType(new MediaType("application", "force-download"));
        header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + outPutFileName);

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End download template : " + id + " [" + timeElapsed + "]");

        return new HttpEntity<>(new ByteArrayResource(template.getTemplate()), header);
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/valider")
    String validerTemplate(Principal principal, Model model, @RequestParam("id") Long id) {

        long start = System.currentTimeMillis();
        logger.info("Begin validate template : " + id);

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        Template template = templateService.get(id);
        try {
            MSWordTool msWordTool = new MSWordTool();
            msWordTool.setTemplate(new ByteArrayInputStream(template.getTemplate()));
            Collection<BookMark> bookMarkList = msWordTool.getBookMarks().getBookmarkList().stream().filter(bookMark -> !bookMark.getBookmarkName().contains("_GoBack")).collect(Collectors.toList());
            List<XWPFAbstractSDT> contentControlList = msWordTool.getContentsControl();

            List<BookMarkDTO> bookmarks = new ArrayList<>();
            List<ContentControlDTO> contentControls = new ArrayList<>();

            LinkedHashMap<String, Integer> doubleBookmarkList = new LinkedHashMap<>();

            for (BookMark f : bookMarkList) {
                bookmarks.add(new BookMarkDTO(f.getBookmarkName(), f.getClass().getTypeName(), f.getBookmarkText()));
                // check double 
                if (!f.getBookmarkName().isEmpty()) {
                    if (doubleBookmarkList.containsKey(f.getBookmarkName())) {
                        doubleBookmarkList.put(f.getBookmarkName(), doubleBookmarkList.get(f.getBookmarkName()) + 1);
                    } else {
                        doubleBookmarkList.put(f.getBookmarkName(), 1);
                    }
                }
            }

            List<BookMarkDTO> doubleBookmarks = new ArrayList<>();

            for (BookMarkDTO b : bookmarks) {
                if (!b.getName().isEmpty() && !doubleBookmarks.contains(b) && doubleBookmarkList.get(b.getName()) > 1) {
                    doubleBookmarks.add(b);
                }
            }

            for (XWPFAbstractSDT sdt : contentControlList) {
                contentControls.add(new ContentControlDTO(1, sdt.getTitle(), sdt.getContent().getClass().getTypeName(), sdt.getContent().getText()));
            }


            model.addAttribute(LIST_DOUBLE_BOOKMARKS, doubleBookmarks);
            model.addAttribute(LIST_ALL_BOOKMARKS, bookmarks);
            model.addAttribute(LIST_ALL_CONTENTCONTORLS, contentControls);
            model.addAttribute(TEMPLATE_ID, template.getTemplateId());
            model.addAttribute(TEMPLATE, template);

        } catch (Exception ex) {
            java.util.logging.Logger.getLogger(TemplateCheckController.class.getName()).log(Level.SEVERE, null, ex);
        }

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End validate template : " + id + " [" + timeElapsed + "]");

        return TEMPLATE_CHECK_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/listHistory")
    String findTemplateVersion(Principal principal, Model model, @RequestParam("docType") String docType) {

        long start = System.currentTimeMillis();
        logger.info("Begin find Template versions by doctype : " + docType);

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }

        try {
            List<TemplateDTO> templates;

            templates = templateService.findTemplateVersion(docType);

            model.addAttribute("doctype", docType);
            model.addAttribute(LIST_TEMPLATES, templates);
            model.addAttribute("nbrActiveTemplate", templateService.countActiveTemplateBy(docType));

        } catch (Exception e) {
            model.addAttribute(ERROR_MESSAGE, e.getMessage());
        }

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("find Template versions by doctype : " + docType + " [" + timeElapsed + "]");

        return TEMPLATE_HISTORY_PAGE;
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("template/build")
    String build(Principal principal, Model model, @RequestParam("id") Long id) {
        Template aTemplate = templateService.get(id);
        model.addAttribute("template", aTemplate);
        return "config/template/templateBuild";
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @PostMapping("template/save")
    String save(Principal principal, Model model, @Valid TemplateForm templateForm, Errors errors) throws IOException {

        long start = System.currentTimeMillis();
        logger.info("Begin save template " + templateForm.getDocType());

        Template updatedTemplate;
        Template nwTemplate;

        if (principal == null) {
            return SIGNIN_PAGE;
        }

        if (errors.hasErrors()) {
            return TEMPLATE_EDIT_PAGE;
        }

        if (templateForm.getTemplateId() >= 1 && null != templateForm.getTemplate() && !templateForm.getTemplate().isEmpty()) {

            Template aTemplate = new Template();
            String updDescription;
            String updDonfigMngtAction;
            if (null == templateForm.getReplaceTemplate()) {
                updatedTemplate = templateService.get(templateForm.getTemplateId());
                updatedTemplate.setTemplateStatus(TEMPLATE_DELETED);
                templateService.save(updatedTemplate);
                // delete the existing version
                String description = " Delete Template Version : " + updatedTemplate.getOwordDocType().getDocTypeCode() + " [ID:" + updatedTemplate.getTemplateId() + "]";
                String configMngtAction = "tmpdel";

                LogConfigMngt logConfigMngt;
                logConfigMngt = new LogConfigMngt(null, TABLE, configMngtAction, principal.getName(), LocalDateTime.now(), Long.toString(updatedTemplate.getTemplateId()), description);
                logConfigMngtService.save(logConfigMngt);
                aTemplate.setCreateDate(new Date());
                updDescription = "[" + TEMPLATE_UPDATE_01 + "] Create template new version (new ID) (" + updatedTemplate.getOwordDocType().getDocTypeCode() + ")";
                updDonfigMngtAction = TEMPLATE_UPDATE_01;

            } else {
                aTemplate = templateService.get(templateForm.getTemplateId());
                updDescription = "[" + TEMPLATE_UPDATE_02 + "] Update template version (same ID : " + aTemplate.getTemplateId() + " [" + aTemplate.getOwordDocType().getDocTypeCode() + "] ";
                updDonfigMngtAction = TEMPLATE_UPDATE_02;
            }
            /* create the new version */
            aTemplate.setTemplate(templateForm.getTemplate().getBytes());
            aTemplate.setTemplateStatus(TEMPLATE_ACTIVE);
            Optional<DocType> theDocType = Optional.ofNullable(docTypeService.get(templateForm.getDocType()));
            Optional<Classification> theClassification = Optional.ofNullable(classificationService.get(templateForm.getClassification()));
            Optional<DocumentInstance> theDocumentInstance = Optional.ofNullable(documentInstanceService.get(templateForm.getDocumentInstance()));
            Optional<EncounterTypeCode> theEncounterTypeCode = Optional.ofNullable(encounterTypeCodeService.get(templateForm.getEncounterType()));

            Optional<ServiceCode> theServiceCode;
            if (templateForm.getServiceCode() != null && !"NULL".equals(templateForm.getServiceCode()) && !templateForm.getServiceCode().isEmpty()) {
                theServiceCode = serviceCodeService.get(templateForm.getServiceCode());
                aTemplate.setOwordServiceCode(theServiceCode.get());
            }

            aTemplate.setOwordDocumentInstance(theDocumentInstance.get());
            aTemplate.setOwordDocType(theDocType.get());
            aTemplate.setOwordClassification(theClassification.isPresent() ? theClassification.get() : null);
            aTemplate.setPermission(templateForm.getPermission());

            if (theDocumentInstance != null && !theDocumentInstance.isEmpty() && !theDocumentInstance.equals("NULL")) {
                aTemplate.setOwordDocumentInstance(theDocumentInstance.get());
            }

            if (theDocumentInstance != null && !theEncounterTypeCode.isEmpty() && !theDocumentInstance.equals("NULL")) {
                aTemplate.setOwordEncounterTypeCode(theEncounterTypeCode.get());
            }

            aTemplate.setComments(templateForm.getComment());
            aTemplate.setAlias(templateForm.getAlias());
            aTemplate.setAliasDescription(templateForm.getAliasDescription());

            if (null != templateForm.getAllowDraft() && templateForm.getAllowDraft().equalsIgnoreCase("on")) {
                aTemplate.setAllowDraft("1");
            } else {
                aTemplate.setAllowDraft("0");
            }

            if (null != templateForm.getAllowPreliminary() && templateForm.getAllowPreliminary().equalsIgnoreCase("on")) {
                aTemplate.setAllowPreliminary("1");
            } else {
                aTemplate.setAllowPreliminary("0");
            }

            if (null != templateForm.getAllowPrinting() && templateForm.getAllowPrinting().equalsIgnoreCase("on")) {
                aTemplate.setAllowPrinting("1");
            } else {
                aTemplate.setAllowPrinting("0");
            }

            if (null != templateForm.getCopyPreviousByDefault() && templateForm.getCopyPreviousByDefault().equalsIgnoreCase("on")) {
                aTemplate.setCopyPreviousByDefault("1");
            } else {
                aTemplate.setCopyPreviousByDefault("0");
            }

            if (null != templateForm.getCopyPreviousMandatory() && templateForm.getCopyPreviousMandatory().equalsIgnoreCase("on")) {
                aTemplate.setCopyPreviousMandatory("1");
            } else {
                aTemplate.setCopyPreviousMandatory("0");
            }

            if (null != templateForm.getAllowCopyPrevious() && templateForm.getAllowCopyPrevious().equalsIgnoreCase("on")) {
                aTemplate.setAllowCopyPrevious("1");
            } else {
                aTemplate.setAllowCopyPrevious("0");
            }

            if (null != templateForm.getAllowCopyPreviousUser() && templateForm.getAllowCopyPreviousUser().equalsIgnoreCase("on")) {
                aTemplate.setAllowCopyFromPreviousForUser("1");
            } else {
                aTemplate.setAllowCopyFromPreviousForUser("0");
            }

            if (null != templateForm.getPrintOnly() && templateForm.getPrintOnly().equalsIgnoreCase("on")) {
                aTemplate.setPrintOnly("1");
            } else {
                aTemplate.setPrintOnly("0");
            }

            if (null != templateForm.getDisablePrintingDraft() && templateForm.getDisablePrintingDraft().equalsIgnoreCase("on")) {
                aTemplate.setDisablePrintingDraft("1");
            } else {
                aTemplate.setDisablePrintingDraft("0");
            }

            if (null != templateForm.getAllowWorkGroup() && templateForm.getAllowWorkGroup().equalsIgnoreCase("on")) {
                aTemplate.setAllowWorkgroup("1");
            } else {
                aTemplate.setAllowWorkgroup("0");
            }

            if (null != templateForm.getReviewable() && templateForm.getReviewable().equalsIgnoreCase("on")) {
                aTemplate.setReviewable("1");
            } else {
                aTemplate.setReviewable("0");
            }

            if (null != templateForm.getAllowNoVisit() && templateForm.getAllowNoVisit().equalsIgnoreCase("on")) {
                aTemplate.setAllowNoVisitID("1");
            } else {
                aTemplate.setAllowNoVisitID("0");
            }

            if (null != templateForm.getEditableByArchivist() && templateForm.getEditableByArchivist().equalsIgnoreCase("on")) {
                aTemplate.setEditableByArchivist("1");
            } else {
                aTemplate.setEditableByArchivist("0");
            }
            templateService.save(aTemplate);
            // Create a new Template

            LogConfigMngt logConfigMngt;
            logConfigMngt = new LogConfigMngt(null, TABLE, updDonfigMngtAction, principal.getName(), LocalDateTime.now(), Long.toString(aTemplate.getTemplateId()), updDescription);
            logConfigMngtService.save(logConfigMngt);

        } else if (templateForm.getTemplateId() >= 1) {
            // update parameters
            updatedTemplate = templateService.get(templateForm.getTemplateId());
            // Optional<DocType> theDocType = Optional.ofNullable(docTypeService.get(templateForm.getDocType()));
            Optional<Classification> theClassification = Optional.ofNullable(classificationService.get(templateForm.getClassification()));
            Optional<DocumentInstance> theDocumentInstance = Optional.ofNullable(documentInstanceService.get(templateForm.getDocumentInstance()));

            Optional<ServiceCode> theServiceCode;
            if (templateForm.getServiceCode() != null && templateForm.getServiceCode() != "NULL" && !templateForm.getServiceCode().isEmpty()) {
                theServiceCode = serviceCodeService.get(templateForm.getServiceCode());
                updatedTemplate.setOwordServiceCode(theServiceCode.get());
            }

            // updatedTemplate.setOwordDocType(theDocType.get());
            updatedTemplate.setOwordClassification(theClassification.orElse(null));

            updatedTemplate.setPermission(templateForm.getPermission());

            updatedTemplate.setOwordDocumentInstance(theDocumentInstance.get());
            updatedTemplate.setComments(templateForm.getComment());
            updatedTemplate.setAlias(templateForm.getAlias());
            updatedTemplate.setAliasDescription(templateForm.getAliasDescription());

            if (null != templateForm.getAllowDraft() && templateForm.getAllowDraft().equalsIgnoreCase("on")) {
                updatedTemplate.setAllowDraft("1");
            } else {
                updatedTemplate.setAllowDraft("0");
            }

            if (null != templateForm.getAllowPreliminary() && templateForm.getAllowPreliminary().equalsIgnoreCase("on")) {
                updatedTemplate.setAllowPreliminary("1");
            } else {
                updatedTemplate.setAllowPreliminary("0");
            }

            if (null != templateForm.getAllowPrinting() && templateForm.getAllowPrinting().equalsIgnoreCase("on")) {
                updatedTemplate.setAllowPrinting("1");
            } else {
                updatedTemplate.setAllowPrinting("0");
            }

            if (null != templateForm.getCopyPreviousByDefault() && templateForm.getCopyPreviousByDefault().equalsIgnoreCase("on")) {
                updatedTemplate.setCopyPreviousByDefault("1");
            } else {
                updatedTemplate.setCopyPreviousByDefault("0");
            }

            if (null != templateForm.getCopyPreviousMandatory() && templateForm.getCopyPreviousMandatory().equalsIgnoreCase("on")) {
                updatedTemplate.setCopyPreviousMandatory("1");
            } else {
                updatedTemplate.setCopyPreviousMandatory("0");
            }

            if (null != templateForm.getAllowCopyPrevious() && templateForm.getAllowCopyPrevious().equalsIgnoreCase("on")) {
                updatedTemplate.setAllowCopyPrevious("1");
            } else {
                updatedTemplate.setAllowCopyPrevious("0");
            }

            if (null != templateForm.getAllowCopyPreviousUser() && templateForm.getAllowCopyPreviousUser().equalsIgnoreCase("on")) {
                updatedTemplate.setAllowCopyFromPreviousForUser("1");
            } else {
                updatedTemplate.setAllowCopyFromPreviousForUser("0");
            }

            if (null != templateForm.getPrintOnly() && templateForm.getPrintOnly().equalsIgnoreCase("on")) {
                updatedTemplate.setPrintOnly("1");
            } else {
                updatedTemplate.setPrintOnly("0");
            }

            if (null != templateForm.getDisablePrintingDraft() && templateForm.getDisablePrintingDraft().equalsIgnoreCase("on")) {
                updatedTemplate.setDisablePrintingDraft("1");
            } else {
                updatedTemplate.setDisablePrintingDraft("0");
            }

            if (null != templateForm.getAllowWorkGroup() && templateForm.getAllowWorkGroup().equalsIgnoreCase("on")) {
                updatedTemplate.setAllowWorkgroup("1");
            } else {
                updatedTemplate.setAllowWorkgroup("0");
            }

            if (null != templateForm.getReviewable() && templateForm.getReviewable().equalsIgnoreCase("on")) {
                updatedTemplate.setReviewable("1");
            } else {
                updatedTemplate.setReviewable("0");
            }

            if (null != templateForm.getAllowNoVisit() && templateForm.getAllowNoVisit().equalsIgnoreCase("on")) {
                updatedTemplate.setAllowNoVisitID("1");
            } else {
                updatedTemplate.setAllowNoVisitID("0");
            }

            if (null != templateForm.getEditableByArchivist() && templateForm.getEditableByArchivist().equalsIgnoreCase("on")) {
                updatedTemplate.setEditableByArchivist("1");
            } else {
                updatedTemplate.setEditableByArchivist("0");
            }
            nwTemplate = templateService.save(updatedTemplate);

            String description = "[" + TEMPLATE_UPDATE_03 + "] Update template parameters  " + updatedTemplate.getOwordDocType().getDocTypeCode() + " (" + updatedTemplate.getTemplateId() + ")";
            String configMngtAction = TEMPLATE_UPDATE_03;

            LogConfigMngt logConfigMngt;
            logConfigMngt = new LogConfigMngt(null, TABLE, configMngtAction, principal.getName(), LocalDateTime.now(), Long.toString(updatedTemplate.getTemplateId()), description);
            logConfigMngtService.save(logConfigMngt);
            // update the template
        } else if (null != templateForm.getTemplate()) {
            /* new template */
            Template aTemplate = new Template();
            aTemplate.setTemplate(templateForm.getTemplate().getBytes());
            aTemplate.setTemplateStatus(TEMPLATE_ACTIVE);
            Optional<DocType> theDocType = Optional.ofNullable(docTypeService.get(templateForm.getDocType()));
            Optional<Classification> theClassification = Optional.ofNullable(classificationService.get(templateForm.getClassification()));
            Optional<DocumentInstance> theDocumentInstance = Optional.ofNullable(documentInstanceService.get(templateForm.getDocumentInstance()));

            Optional<ServiceCode> theServiceCode;
            if (templateForm.getServiceCode() != null && !"NULL".equals(templateForm.getServiceCode()) && !templateForm.getServiceCode().isEmpty()) {
                theServiceCode = serviceCodeService.get(templateForm.getServiceCode());
                aTemplate.setOwordServiceCode(theServiceCode.get());
            }

            aTemplate.setOwordDocType(theDocType.get());
            aTemplate.setOwordClassification(theClassification.isPresent() ? theClassification.get() : null);

            aTemplate.setPermission(templateForm.getPermission());

            aTemplate.setOwordDocumentInstance(theDocumentInstance.get());
            aTemplate.setComments(templateForm.getComment());
            aTemplate.setAlias(templateForm.getAlias());
            aTemplate.setAliasDescription(templateForm.getAliasDescription());

            if (null != templateForm.getAllowDraft() && templateForm.getAllowDraft().equalsIgnoreCase("on")) {
                aTemplate.setAllowDraft("1");
            } else {
                aTemplate.setAllowDraft("0");
            }

            if (null != templateForm.getAllowPreliminary() && templateForm.getAllowPreliminary().equalsIgnoreCase("on")) {
                aTemplate.setAllowPreliminary("1");
            } else {
                aTemplate.setAllowPreliminary("0");
            }

            if (null != templateForm.getAllowPrinting() && templateForm.getAllowPrinting().equalsIgnoreCase("on")) {
                aTemplate.setAllowPrinting("1");
            } else {
                aTemplate.setAllowPrinting("0");
            }

            if (null != templateForm.getCopyPreviousByDefault() && templateForm.getCopyPreviousByDefault().equalsIgnoreCase("on")) {
                aTemplate.setCopyPreviousByDefault("1");
            } else {
                aTemplate.setCopyPreviousByDefault("0");
            }

            if (null != templateForm.getCopyPreviousMandatory() && templateForm.getCopyPreviousMandatory().equalsIgnoreCase("on")) {
                aTemplate.setCopyPreviousMandatory("1");
            } else {
                aTemplate.setCopyPreviousMandatory("0");
            }

            if (null != templateForm.getAllowCopyPrevious() && templateForm.getAllowCopyPrevious().equalsIgnoreCase("on")) {
                aTemplate.setAllowCopyPrevious("1");
            } else {
                aTemplate.setAllowCopyPrevious("0");
            }

            if (null != templateForm.getAllowCopyPreviousUser() && templateForm.getAllowCopyPreviousUser().equalsIgnoreCase("on")) {
                aTemplate.setAllowCopyFromPreviousForUser("1");
            } else {
                aTemplate.setAllowCopyFromPreviousForUser("0");
            }

            if (null != templateForm.getPrintOnly() && templateForm.getPrintOnly().equalsIgnoreCase("on")) {
                aTemplate.setPrintOnly("1");
            } else {
                aTemplate.setPrintOnly("0");
            }

            if (null != templateForm.getDisablePrintingDraft() && templateForm.getDisablePrintingDraft().equalsIgnoreCase("on")) {
                aTemplate.setDisablePrintingDraft("1");
            } else {
                aTemplate.setDisablePrintingDraft("0");
            }

            if (null != templateForm.getAllowWorkGroup() && templateForm.getAllowWorkGroup().equalsIgnoreCase("on")) {
                aTemplate.setAllowWorkgroup("1");
            } else {
                aTemplate.setAllowWorkgroup("0");
            }

            if (null != templateForm.getReviewable() && templateForm.getReviewable().equalsIgnoreCase("on")) {
                aTemplate.setReviewable("1");
            } else {
                aTemplate.setReviewable("0");
            }

            if (null != templateForm.getAllowNoVisit() && templateForm.getAllowNoVisit().equalsIgnoreCase("on")) {
                aTemplate.setAllowNoVisitID("1");
            } else {
                aTemplate.setAllowNoVisitID("0");
            }

            if (null != templateForm.getEditableByArchivist() && templateForm.getEditableByArchivist().equalsIgnoreCase("on")) {
                aTemplate.setEditableByArchivist("1");
            } else {
                aTemplate.setEditableByArchivist("0");
            }

            nwTemplate = templateService.save(aTemplate);

            // Save a new template
            String description = "[" + TEMPLATE_NEW_01 + "] Create New Template (" + nwTemplate.getOwordDocType().getDocTypeCode() + ")";
            String configMngtAction = TEMPLATE_NEW_01;

            LogConfigMngt logConfigMngt;
            logConfigMngt = new LogConfigMngt(null, TABLE, configMngtAction, principal.getName(), LocalDateTime.now(), Long.toString(nwTemplate.getTemplateId()), description);
            logConfigMngtService.save(logConfigMngt);

            long finish = System.currentTimeMillis();
            long timeElapsed = finish - start;

            logger.info("End save template : " + nwTemplate.getTemplateId() + " [" + timeElapsed + "]");
        }

        return "redirect:../templates";
    }

    @PreAuthorize("hasAnyRole('ROLE_ADMIN', 'ROLE_SUP_ADMIN')")
    @GetMapping("templates")
    String getAllTemplate(Principal principal, Model model) {

        long start = System.currentTimeMillis();
        logger.info("Begin get all templates");

        if (principal == null) {
            return RETURN_SIGN_IN_PAGE;
        }
        try {
            List<TemplateDTO> templates;

            templates = templateService.getActiveTemplate();

            model.addAttribute(LIST_TEMPLATES, templates);

        } catch (Exception e) {
            model.addAttribute(ERROR_MESSAGE, e.getMessage());
        }

        long finish = System.currentTimeMillis();
        long timeElapsed = finish - start;

        logger.info("End get all templates [" + timeElapsed + "]");

        return "config/template/templateList";
    }

}
