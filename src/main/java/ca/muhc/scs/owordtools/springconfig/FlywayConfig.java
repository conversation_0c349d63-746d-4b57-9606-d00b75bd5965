package ca.muhc.scs.owordtools.springconfig;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Profile;

import javax.sql.DataSource;

@Configuration
@Profile("!test")
public class FlywayConfig {

    @Value("${spring.profiles.active}")
    private String env;

    @Bean
    @DependsOn("owordtoolsDataSource")
    public Flyway flyway(@Qualifier("owordtoolsDataSource") DataSource specificDataSource) {
        Flyway flyway = Flyway.configure()
                .dataSource(specificDataSource)
                .locations("classpath:db/migrations/oracle/" + env)
                .baselineOnMigrate(true)
                .load();

        // Lancer la migration au démarrage
        flyway.migrate();

        return flyway;
    }
}