package ca.muhc.scs.owordtools.springconfig;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@Configuration
public class OwordToolsDataSourceConfig {


    @Bean
    @ConfigurationProperties("spring.datasource.owordtools")
    public DataSourceProperties owordtoolsDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean
    public DataSource owordtoolsDataSource() {
        return owordtoolsDataSourceProperties()
                .initializeDataSourceBuilder()
                .build();
    }
}
