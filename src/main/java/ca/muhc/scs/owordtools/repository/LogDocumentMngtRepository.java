package ca.muhc.scs.owordtools.repository;

import ca.muhc.scs.owordtools.model.LogDocumentMngt;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.datatables.repository.DataTablesRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface LogDocumentMngtRepository extends DataTablesRepository<LogDocumentMngt, Integer> { 
    
   @Query("select ld from LogDocumentMngt ld order by ld.documentMngtDate DESC")
   public List<LogDocumentMngt> getAll();
   
   @Query("SELECT ld FROM LogDocumentMngt ld WHERE ld.documentMngtDate >= :beginDate ORDER BY ld.documentMngtDate DESC ")
   public List<LogDocumentMngt> getLastActivities(@Param("beginDate") LocalDateTime beginDate);
   
   @Query("select ld from LogDocumentMngt ld where ld.id = ?1")
    public LogDocumentMngt get(String id);

}
