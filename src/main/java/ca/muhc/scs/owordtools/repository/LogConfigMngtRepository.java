package ca.muhc.scs.owordtools.repository;

import ca.muhc.scs.owordtools.model.LogConfigMngt;
import java.time.LocalDateTime;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import org.springframework.data.jpa.datatables.repository.DataTablesRepository;
import org.springframework.data.repository.query.Param;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface LogConfigMngtRepository extends DataTablesRepository<LogConfigMngt, Integer> {
    
    @Query("select cl from LogConfigMngt cl order by cl.configMngtDate DESC")
    public List<LogConfigMngt> getAll();
    
    @Query("SELECT cl FROM LogConfigMngt cl WHERE cl.configMngtDate >= :beginDate ORDER BY cl.configMngtDate DESC ")
    public List<LogConfigMngt> getLastActivities(@Param("beginDate") LocalDateTime beginDate);
   
    @Query("select cl from LogConfigMngt cl where cl.id = ?1")
    public LogConfigMngt get(String id);
    
}
