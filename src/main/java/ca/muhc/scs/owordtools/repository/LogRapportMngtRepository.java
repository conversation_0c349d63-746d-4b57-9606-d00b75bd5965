package ca.muhc.scs.owordtools.repository;

import ca.muhc.scs.owordtools.model.LogRapportMngt;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.datatables.repository.DataTablesRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface LogRapportMngtRepository extends DataTablesRepository<LogRapportMngt, Integer> { 
    
   @Query("SELECT lr FROM LogRapportMngt lr order by lr.rapportMngtDate ASC")
   public List<LogRapportMngt> getAll();
   
   @Query("SELECT lr FROM LogRapportMngt lr WHERE lr.rapportMngtDate >= :beginDate order by lr.rapportMngtDate ASC")
   public List<LogRapportMngt> getLastActivities(@Param("beginDate") LocalDateTime beginDate);
   
   @Query("select lr from LogRapportMngt lr where lr.id = ?1")
    public LogRapportMngt get(String id);

}
