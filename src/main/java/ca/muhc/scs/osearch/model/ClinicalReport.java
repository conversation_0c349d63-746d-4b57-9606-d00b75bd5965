package ca.muhc.scs.osearch.model;

import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class ClinicalReport {
    
    public static final String NONE = "0";
    public static final String YESTERDAY = "1";
    public static final String TO_DAY = "2";
    public static final String LAST_SEVEN_DAYS = "3";
    public static final String THIS_MONTH = "4";
    public static final String LAST_MONTH = "5";
    public static final String EVERY_TIME = "6";
    private static final String[] PERIODE = new String[]{NONE, TO_DAY, YESTERDAY, LAST_SEVEN_DAYS, THIS_MONTH, LAST_MONTH, EVERY_TIME};

    private LocalDate beginDate;
    private LocalDate endDate;
    private String dateInterval;
    private String user;
    private int categoryId;
    private String docType;        
    private String isFin;
    private String isPre;
    private String isDra;
    private String isExp;
    private String isDel;
    private String revisionStatus;

    public ClinicalReport(LocalDate beginDate, LocalDate endDate, String user, int categoryId, String docType, String isFin, String isPre, String isDra, String isExp, String isDel, String dateInterval, String revisionStatus) {
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.user = user;
        this.categoryId = categoryId;
        this.docType = docType;
        this.isFin = isFin;
        this.isPre = isPre;
        this.isDra = isDra;
        this.isExp = isExp;
        this.isDel = isDel;
        this.revisionStatus = revisionStatus;
        this.dateInterval = dateInterval;
    }

    public static String[] getPERIODE() {
        return PERIODE;
    }
    
    public LocalDate getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDate beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getDateInterval() {
        return dateInterval;
    }

    public void setDateInterval(String dateInterval) {
        this.dateInterval = dateInterval;
    }
    
    public void intializeDateInterval() {
        this.dateInterval = NONE;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getIsFin() {
        return isFin;
    }

    public void setIsFin(String isFin) {
        this.isFin = isFin;
    }

    public String getIsPre() {
        return isPre;
    }

    public void setIsPre(String isPre) {
        this.isPre = isPre;
    }

    public String getIsDra() {
        return isDra;
    }

    public void setIsDra(String isDra) {
        this.isDra = isDra;
    }

    public String getIsExp() {
        return isExp;
    }

    public void setIsExp(String isExp) {
        this.isExp = isExp;
    }

    public String getIsDel() {
        return isDel;
    }

    public void setIsDel(String isDel) {
        this.isDel = isDel;
    }

    public String getRevisionStatus() {
        return revisionStatus;
    }

    public void setRevisionStatus(String revisionStatus) {
        this.revisionStatus = revisionStatus;
    }

    public String concactFilter() {
        if (null == this.docType || this.docType.equals("")) this.docType = "null";
        if (null == this.revisionStatus   || this.revisionStatus.equals("")) this.revisionStatus = "null";
        if (null == this.user || this.user.equals("")) this.revisionStatus = "null";
        return this.isFin + "@" + this.isPre + "@" + this.isDra + "@" + this.isDel + "@" + this.isExp + "@" + this.dateInterval + "@" + this.beginDate + "@" + this.endDate + "@" + this.user + "@" + this.categoryId + "@" + this.docType + "@" + this.revisionStatus;
    }

}
