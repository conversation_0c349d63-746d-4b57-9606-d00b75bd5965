package ca.muhc.scs.osearch.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "OWORD_DOCUMENT_INSTANCE")
public class DocumentInstance {

    private String instanceCode;
    private String nameEn;
    private String nameFr;

    public DocumentInstance() {
    }

    public DocumentInstance(String instanceCode, String nameEn, String nameFr) {
        this.instanceCode = instanceCode;
        this.nameEn = nameEn;
        this.nameFr = nameFr;
    }

    @Id
    @Column(name = "INSTANCE_CODE", unique = true, nullable = false, length = 10)
    public String getInstanceCode() {
        return this.instanceCode;
    }

    public void setInstanceCode(String instanceCode) {
        this.instanceCode = instanceCode;
    }

    @Column(name = "NAME_EN", nullable = false)
    public String getNameEn() {
        return this.nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    @Column(name = "NAME_FR", nullable = false)
    public String getNameFr() {
        return this.nameFr;
    }

    public void setNameFr(String nameFr) {
        this.nameFr = nameFr;
    }
}
