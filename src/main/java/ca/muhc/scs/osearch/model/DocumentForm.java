/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package ca.muhc.scs.osearch.model;


import java.util.Date;
import java.util.Objects;


public class DocumentForm {
    private long documentId;
    private String mrn;
    private String site;
    private String visitNumber;
    private Date admitDate;
    private String statusCode;
    private long templateId;
    private String finalBy;
    private String updateBy;
    private String preliminaryBy;
    private String createdBy;
    private Date finalDate;
    private Date preliminaryDate;
    private Date updateDate;
    private Date createDate;
    private String reservedBy;
    
    public DocumentForm() {
    }

    public DocumentForm(long documentId, String mrn, String site, String visitNumber, Date admitDate, String statusCode, long templateId, String finalBy, String updateBy, String preliminaryBy, String createdBy, Date finalDate, Date preliminaryDate, Date updateDate, Date createDate, String reservedBy) {
        this.documentId = documentId;
        this.mrn = mrn;
        this.site = site;
        this.visitNumber = visitNumber;
        this.admitDate = admitDate;
        this.statusCode = statusCode;
        this.templateId = templateId;
        this.finalBy = finalBy;
        this.updateBy = updateBy;
        this.preliminaryBy = preliminaryBy;
        this.createdBy = createdBy;
        this.finalDate = finalDate;
        this.preliminaryDate = preliminaryDate;
        this.updateDate = updateDate;
        this.createDate = createDate;
        this.reservedBy = reservedBy;
    }

    public long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(long documentId) {
        this.documentId = documentId;
    }

    public String getMrn() {
        return mrn;
    }

    public void setMrn(String mrn) {
        this.mrn = mrn;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getVisitNumber() {
        return visitNumber;
    }

    public void setVisitNumber(String visitNumber) {
        this.visitNumber = visitNumber;
    }

    public Date getAdmitDate() {
        return admitDate;
    }

    public void setAdmitDate(Date admitDate) {
        this.admitDate = admitDate;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(long templateId) {
        this.templateId = templateId;
    }

    public String getFinalBy() {
        return finalBy;
    }

    public void setFinalBy(String finalBy) {
        this.finalBy = finalBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getPreliminaryBy() {
        return preliminaryBy;
    }

    public void setPreliminaryBy(String preliminaryBy) {
        this.preliminaryBy = preliminaryBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getFinalDate() {
        return finalDate;
    }

    public void setFinalDate(Date finalDate) {
        this.finalDate = finalDate;
    }

    public Date getPreliminaryDate() {
        return preliminaryDate;
    }

    public void setPreliminaryDate(Date preliminaryDate) {
        this.preliminaryDate = preliminaryDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getReservedBy() {
        return reservedBy;
    }

    public void setReservedBy(String reservedBy) {
        this.reservedBy = reservedBy;
    }

    
    
    @Override
    public int hashCode() {
        int hash = 3;
        hash = 47 * hash + Objects.hashCode(this.documentId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DocumentForm other = (DocumentForm) obj;
        if (!Objects.equals(this.documentId, other.documentId)) {
            return false;
        }
        return true;
    }

}
