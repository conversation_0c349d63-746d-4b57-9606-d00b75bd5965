package ca.muhc.scs.osearch.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Classe pour représenter les résultats de la requête de rapport de documents de template
 * Cette classe n'est pas mappée directement à une table mais utilisée pour les résultats de requête
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemplateDocumentReport {
    private String etat;
    private String templateId;
    private String docType;
    private String classificationCode;
    private String nameFr;
    private String nameEn;
    private String templateStatus;
    private Date createDate;
    private String aliasDescription;
    private Long finalDocuments;
}