package ca.muhc.scs.osearch.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "OWORD_TEMPLATE")
public class Template {
    
        private long templateId;
        private DocType owordDocType;
	private Classification owordClassification;
	private byte[] template;
	private Date createDate;
	private String templateStatus;
        private String createdBy;
        private String deletedBy;
	private String allowDraft;
	private String allowPreliminary;
	private String allowPrinting;
	private String alias;
	private String aliasDescription;
	private DocumentInstance owordDocumentInstance;
	private String comments;
	private ServiceCode owordServiceCode;
	private String printOnly;
	private String disablePrintingDraft;
	private String allowCopyPrevious;
	private String permission;
	private String allowWorkgroup;
	private String copyPreviousByDefault;
	private String copyPreviousMandatory;
	private String reviewable;
	private String editableByArchivist;
	private EncounterTypeCode owordEncounterTypeCode;
	private String allowNoVisitID;
	private String allowCopyFromPreviousForUser;

        
	public Template() {
	}

	public Template(long templateId, DocType owordDocType, Classification owordClassification,
			 String allowDraft, String allowPreliminary, String allowPrinting, String alias, String aliasDescription,
			 DocumentInstance owordDocumentInstance, String comments, ServiceCode owordServiceCode,
			 String printOnly, String disablePrintingDraft, String allowCopyPrevious, String permission,
			 String allowWorkgroup, String copyPreviousByDefault, String copyPreviousMandatory,
			 String reviewable, String editableByArchivist, EncounterTypeCode owordEncounterTypeCode, String allowNoVisitID, String allowCopyFromPreviousForUser) {
		this.templateId = templateId;
		this.owordDocType = owordDocType;
		this.owordClassification = owordClassification;
		this.allowDraft = allowDraft;
		this.allowPreliminary = allowPreliminary;
		this.allowPrinting = allowPrinting;
		this.alias = alias;
		this.aliasDescription = aliasDescription;
		this.owordDocumentInstance = owordDocumentInstance;
		this.comments = comments;
		this.owordServiceCode = owordServiceCode;
		this.printOnly = printOnly;
		this.disablePrintingDraft = disablePrintingDraft;
		this.allowCopyPrevious = allowCopyPrevious;
		this.permission = permission;
		this.allowWorkgroup = allowWorkgroup;
		this.copyPreviousByDefault = copyPreviousByDefault;
		this.copyPreviousMandatory = copyPreviousMandatory;
		this.reviewable = reviewable;
		this.editableByArchivist = editableByArchivist;
		this.owordEncounterTypeCode = owordEncounterTypeCode;
		this.allowNoVisitID = allowNoVisitID;
		this.allowCopyFromPreviousForUser = allowCopyFromPreviousForUser;
	}

	public Template(long templateId, DocType owordDocType, Classification owordClassification,
						 byte[] content, Date createDate, String templateStatus, String allowDraft,
						 String allowPreliminary, String allowPrinting,	String alias, String aliasDescription,
						 DocumentInstance owordDocumentInstance, String comments, ServiceCode owordServiceCode,
						 String printOnly, String disablePrintingDraft, String allowCopyPrevious,
						 String permission, String allowWorkgroup, String copyPreviousByDefault, String copyPreviousMandatory,
						 String reviewable, String editableByArchivist, EncounterTypeCode owordEncounterTypeCode, String allowNoVisitID, String allowCopyFromPreviousForUser) {
		this.templateId = templateId;
		this.owordDocType = owordDocType;
		this.owordClassification = owordClassification;
		this.template = content;
		this.createDate = createDate;
		this.templateStatus = templateStatus;
		this.allowDraft = allowDraft;
		this.allowPreliminary = allowPreliminary;
		this.allowPrinting = allowPrinting;
		this.alias = alias;
		this.aliasDescription = aliasDescription;
		this.owordDocumentInstance = owordDocumentInstance;
		this.comments = comments;
		this.owordServiceCode = owordServiceCode;
		this.printOnly = printOnly;
		this.disablePrintingDraft = disablePrintingDraft;
		this.allowCopyPrevious = allowCopyPrevious;
		this.permission = permission;
		this.allowWorkgroup = allowWorkgroup;
		this.copyPreviousByDefault = copyPreviousByDefault;
		this.copyPreviousMandatory = copyPreviousMandatory;
		this.reviewable = reviewable;
		this.editableByArchivist = editableByArchivist;
		this.owordEncounterTypeCode = owordEncounterTypeCode;
		this.allowNoVisitID = allowNoVisitID;
		this.allowCopyFromPreviousForUser = allowCopyFromPreviousForUser;
	}

	
        @Id
	@Column(name = "TEMPLATE_ID", unique = true, nullable = false, precision = 38, scale = 0)
        @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HIBERNATE_SEQUENCE")
	@SequenceGenerator(name = "HIBERNATE_SEQUENCE", sequenceName = "HIBERNATE_SEQUENCE", allocationSize = 1)
        public long getTemplateId() {
		return this.templateId;
	}

	public void setTemplateId(long templateId) {
		this.templateId = templateId;
	}

	@ManyToOne
	@JoinColumn(name = "DOC_TYPE", nullable = false)
	public DocType getOwordDocType() {
		return this.owordDocType;
	}

	public void setOwordDocType(DocType owordDocType) {
		this.owordDocType = owordDocType;
	}

	@ManyToOne
	@JoinColumn(name = "CLASSIFICATION_CODE")
	public Classification getOwordClassification() {
		return this.owordClassification;
	}

	public void setOwordClassification(Classification owordClassification) {
		this.owordClassification = owordClassification;
	}

	@Lob
	@Column(name = "TEMPLATE", nullable = false)
	public byte[] getTemplate() {
		return this.template;
	}

	public void setTemplate(byte[] template) {
		this.template = template;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE", nullable = false)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "TEMPLATE_STATUS", nullable = false)
	public String getTemplateStatus() {
		return this.templateStatus;
	}

	public void setTemplateStatus(String templateStatus) {
		this.templateStatus = templateStatus;
	}

        @Column(name = "CREATED_BY", nullable = true, length = 30)
        public String getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
        }

        @Column(name = "DELETED_BY", nullable = true, length = 30)
        public String getDeletedBy() {
            return deletedBy;
        }

        public void setDeletedBy(String deletedBy) {
            this.deletedBy = deletedBy;
        }
        
        

	@Column(name = "ALLOW_DRAFT", nullable = false, length = 1)
	public String getAllowDraft() {
		return this.allowDraft;
	}

	public void setAllowDraft(String allowDraft) {
		this.allowDraft = allowDraft;
	}

	@Column(name = "ALLOW_PRELIMINARY", nullable = false, length = 1)
	public String getAllowPreliminary() {
		return this.allowPreliminary;
	}

	public void setAllowPreliminary(String allowPreliminary) {
		this.allowPreliminary = allowPreliminary;
	}

	@Column(name = "ALLOW_PRINTING", nullable = false, length = 1)
	public String getAllowPrinting() {
		return this.allowPrinting;
	}

	public void setAllowPrinting(String allowPrinting) {
		this.allowPrinting = allowPrinting;
	}

	@Column(name = "ALIAS", unique = true, length = 100)
	public String getAlias() {
		return this.alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	@Column(name = "ALIAS_DESCRIPTION", length = 255)
	public String getAliasDescription() {
		return this.aliasDescription;
	}

	public void setAliasDescription(String aliasDescription) {
		this.aliasDescription = aliasDescription;
	}

	@ManyToOne
	@JoinColumn(name = "INSTANCE_CODE")
	public DocumentInstance getOwordDocumentInstance() {
		return this.owordDocumentInstance;
	}

	public void setOwordDocumentInstance(DocumentInstance owordDocumentInstance) {
		this.owordDocumentInstance = owordDocumentInstance;
	}

	@Column(name = "COMMENTS", length = 1000)
	public String getComments() {
		return this.comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	@ManyToOne
	@JoinColumn(name = "SERVICE_CODE")
	public ServiceCode getOwordServiceCode() {
		return this.owordServiceCode;
	}

	public void setOwordServiceCode(ServiceCode owordServiceCode) {
		this.owordServiceCode = owordServiceCode;
	}

	@Column(name = "PRINT_ONLY", nullable = false, length = 1)
	public String getPrintOnly() {
		return this.printOnly;
	}

	public void setPrintOnly(String printOnly) {
		this.printOnly = printOnly;
	}

	@Column(name = "DISABLE_PRINTING_DRAFT", nullable = false, length = 1)
	public String getDisablePrintingDraft() {
		return this.disablePrintingDraft;
	}

	public void setDisablePrintingDraft(String disablePrintingDraft) {
		this.disablePrintingDraft = disablePrintingDraft;
	}

	@Column(name = "ALLOW_COPY_PREVIOUS", nullable = false, length = 1)
	public String getAllowCopyPrevious() {
		return this.allowCopyPrevious;
	}

	public void setAllowCopyPrevious(String allowCopyPrevious) {
		this.allowCopyPrevious = allowCopyPrevious;
	}

	@Column(name = "PERMISSION", length = 1)
	public String getPermission() {
		return this.permission;
	}

	public void setPermission(String permission) {
		this.permission = permission;
	}

	@Column(name = "ALLOW_WORKGROUP", length = 1)
	public String getAllowWorkgroup() {
		return this.allowWorkgroup;
	}

	public void setAllowWorkgroup(String allowWorkgroup) {
		this.allowWorkgroup = allowWorkgroup;
	}

	@Column(name = "COPY_PREVIOUS_BY_DEFAULT", length = 1)
	public String getCopyPreviousByDefault() {
		return this.copyPreviousByDefault;
	}

	public void setCopyPreviousByDefault(String copyPreviousByDefault) {
		this.copyPreviousByDefault = copyPreviousByDefault;
	}

	@Column(name = "COPY_PREVIOUS_MANDATORY", length = 1)
	public String getCopyPreviousMandatory() {
		return copyPreviousMandatory;
	}

	public void setCopyPreviousMandatory(String copyPreviousMandatory) {
		this.copyPreviousMandatory = copyPreviousMandatory;
	}

	@Column(name = "REVIEWABLE", length = 1)
	public String getReviewable() {
		return reviewable;
	}

	public void setReviewable(String reviewable) {
		this.reviewable = reviewable;
	}

	@Column(name = "EDITABLE_BY_ARCHIVIST", length = 1)
	public String getEditableByArchivist() {
		return editableByArchivist;
	}

	public void setEditableByArchivist(String editableByArchivist) {
		this.editableByArchivist = editableByArchivist;
	}

	@ManyToOne
	@JoinColumn(name = "ENCOUNTER_TYPE_CODE")
	public EncounterTypeCode getOwordEncounterTypeCode() {
		return this.owordEncounterTypeCode;
	}

	public void setOwordEncounterTypeCode(EncounterTypeCode owordEncounterTypeCode) {
		this.owordEncounterTypeCode = owordEncounterTypeCode;
	}

	@PrePersist
	public void updateUpdateDate() {
		this.createDate = new Date();
	}

	public void setAllowNoVisitID(String allowNoVisitID) {
		this.allowNoVisitID = allowNoVisitID;
	}

	@Column(name = "ALLOW_NOVISIT_ID", length = 1)
	public String getAllowNoVisitID() {
		return allowNoVisitID;
	}

	public void setAllowCopyFromPreviousForUser(
			String allowCopyFromPreviousForUser) {
		this.allowCopyFromPreviousForUser = allowCopyFromPreviousForUser;
	}

	@Column(name = "ALLOW_COPY_FROM_PREVIOUS_USER", length = 1)
	public String getAllowCopyFromPreviousForUser() {
		return allowCopyFromPreviousForUser;
	}

}
