/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package ca.muhc.scs.osearch.model;

import jakarta.persistence.*;

import java.util.Objects;


@Entity
@Table(name = "OWORD_CATEGORY")
public class Category {

    @Id
    @GeneratedValue (strategy = GenerationType.AUTO)
    @Column(name = "CATEGORY_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "NAME_EN", nullable = false)
    //@Size(min = 2, max = 128)
    private String nameEn;

    @Column(name = "NAME_FR", nullable = false)
    //@Size(min = 2, max = 512)
    private String nameFr;

    public Category(Integer id, String nameEn, String nameFr) {
        this.id = id;
        this.nameEn = nameEn;
        this.nameFr = nameFr;
    }

    public Category() {
        this(null, "", "");
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameFr() {
        return nameFr;
    }

    public void setNameFr(String nameFr) {
        this.nameFr = nameFr;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 47 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Category other = (Category) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return nameEn;
    }

    public String toJson() {
        return "Category{\"id\"=\"" + id + "\",\"nameEn\"=\"" + nameEn + "\", \"nameFr\"=\"" + nameFr + "\"}";
    }

}
