/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package ca.muhc.scs.osearch.model;


import jakarta.persistence.*;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "OWORD_DOCUMENT")
public class Document {
    private long documentId;
    private Patient owordPatient;
    private StatusCode owordStatusCode;
    private Template owordTemplate;
    private User owordUserCreateBy;
    private User owordUserPreliminaryBy;
    private User owordUserUpdateBy;
    private User owordUserFinalBy;
    private byte[] documentBlb;
    private byte[] preliminaryDocument;
    private byte[] finalDocument;
    private String mrn;
    private SiteMapping site;
    private String visitNumber;
    private String visitType;
    private String physicianFirstName;
    private String physicianLastName;
    private String physicianLicense;
    private Date createDate;
    private Date updateDate;
    private Date preliminaryDate;
    private Date finalDate;
    private Date admitDate;
    private Date dischargeDate;
    private String wordVersion;
    private String orderNumber;
    private Classification owordClassification;
    private ServiceCode owordServiceCode;
    private Date documentDate;
    private String privateDocument;
    private String workgroup;
    private RevisionStatusCode owordRevisionStatusCode;
    private String finalRevisionCompleted;
    private String confirmedDirectives;
    private User owordUserInUseBy;
    private User owordUserAssignedTo;
    private String sendingApp;
    private String userTitle;
    private Long respLicenceId;

    public Document() {
    }

    public Document(long documentId, SiteMapping site, String mrn, String visitNumber, String visitType, String workgroup,
                    String finalRevisionCompleted, String confirmedDirectives, String wordVersion, String orderNumber, String privateDocument,
                    Date updateDate, Date createDate, Date admitDate, StatusCode owordStatusCode, User owordUserUpdateBy,
                    User owordUserCreateBy, Template owordTemplate, RevisionStatusCode owordRevisionStatusCode,
                    User owordUserInUseBy, Patient owordPatient, Classification owordClassification, ServiceCode owordServiceCode, String sendingApp, String userTitle) {
        this.documentId = documentId;
        this.site = site;
        this.owordStatusCode = owordStatusCode;
        this.owordTemplate = owordTemplate;
        this.mrn = mrn;
        this.visitNumber = visitNumber;
        this.visitType = visitType;
        this.workgroup = workgroup;
        this.finalRevisionCompleted = finalRevisionCompleted;
        this.confirmedDirectives = confirmedDirectives;
        this.wordVersion = wordVersion;
        this.orderNumber = orderNumber;
        this.privateDocument = privateDocument;
        this.updateDate = updateDate;
        this.createDate = createDate;
        this.admitDate = admitDate;
        this.owordUserUpdateBy = owordUserUpdateBy;
        this.owordUserCreateBy = owordUserCreateBy;
        this.owordRevisionStatusCode = owordRevisionStatusCode;
        this.owordUserInUseBy = owordUserInUseBy;
        this.owordPatient = owordPatient;
        this.owordClassification = owordClassification;
        this.owordServiceCode = owordServiceCode;
        this.sendingApp = sendingApp;
        this.userTitle = userTitle;
    }

    public Document(long documentId, User UserFinalBy, Patient owordPatient, StatusCode owordStatusCode,
                    User owordUserCreateBy, User owordUserPreliminaryBy, User owordUserUpdateBy, Template owordTemplate,
                    byte[] document, byte[] preliminaryDocument, byte[] finalDocument, String mrn, SiteMapping site, String visitNumber,
                    String visitType, String physicianFirstName, String physicianLastName, String physicianLicense, Date createDate,
                    Date updateDate, Date preliminaryDate, Date finalDate, Date admitDate, Date dischargeDate, String wordVersion,
                    String orderNumber, Classification owordClassification, ServiceCode owordServiceCode, Date documentDate,
                    String privateDocument, String workgroup, RevisionStatusCode owordRevisionStatusCode, String finalRevisionCompleted,
                    String confirmedDirectives, User owordUserInUseBy, User owordUserAssignedTo, String sendingApp, String userTitle) {
        this.documentId = documentId;
        this.owordUserFinalBy = owordUserFinalBy;
        this.owordPatient = owordPatient;
        this.owordStatusCode = owordStatusCode;
        this.owordUserCreateBy = owordUserCreateBy;
        this.owordUserPreliminaryBy = owordUserPreliminaryBy;
        this.owordUserUpdateBy = owordUserUpdateBy;
        this.owordTemplate = owordTemplate;
        this.documentBlb = document;
        this.preliminaryDocument = preliminaryDocument;
        this.finalDocument = finalDocument;
        this.mrn = mrn;
        this.site = site;
        this.visitNumber = visitNumber;
        this.visitType = visitType;
        this.physicianFirstName = physicianFirstName;
        this.physicianLastName = physicianLastName;
        this.physicianLicense = physicianLicense;
        this.createDate = createDate;
        this.updateDate = updateDate;
        this.preliminaryDate = preliminaryDate;
        this.finalDate = finalDate;
        this.admitDate = admitDate;
        this.dischargeDate = dischargeDate;
        this.wordVersion = wordVersion;
        this.orderNumber = orderNumber;
        this.owordClassification = owordClassification;
        this.owordServiceCode = owordServiceCode;
        this.documentDate = documentDate;
        this.privateDocument = privateDocument;
        this.workgroup = workgroup;
        this.owordRevisionStatusCode = owordRevisionStatusCode;
        this.finalRevisionCompleted = finalRevisionCompleted;
        this.confirmedDirectives = confirmedDirectives;
        this.owordUserInUseBy = owordUserInUseBy;
        this.owordUserAssignedTo = owordUserAssignedTo;
        this.sendingApp = sendingApp;
        this.userTitle = userTitle;
    }

    @Id
    @Column(name = "DOCUMENT_ID", unique = true, nullable = false, precision = 38, scale = 0)
    @GeneratedValue(strategy = GenerationType.AUTO)
    public long getDocumentId() {
        return this.documentId;
    }

    public void setDocumentId(long documentId) {
        this.documentId = documentId;
    }

    @ManyToOne
    @JoinColumn(name = "PATIENT_ID", nullable = false)
    public Patient getOwordPatient() {
        return this.owordPatient;
    }

    public void setOwordPatient(Patient owordPatient) {
        this.owordPatient = owordPatient;
    }

    @ManyToOne
    @JoinColumn(name = "STATUS_CODE", nullable = false)
    public StatusCode getOwordStatusCode() {
        return this.owordStatusCode;
    }

    public void setOwordStatusCode(StatusCode owordStatusCode) {
        this.owordStatusCode = owordStatusCode;
    }

    @ManyToOne
    @JoinColumn(name = "TEMPLATE_ID", nullable = false)
    public Template getOwordTemplate() {
        return this.owordTemplate;
    }

    public void setOwordTemplate(Template owordTemplate) {
        this.owordTemplate = owordTemplate;
    }

    @ManyToOne
    @JoinColumn(name = "CREATE_BY", nullable = false)
    public User getOwordUserCreateBy() {
        return this.owordUserCreateBy;
    }

    public void setOwordUserCreateBy(User owordUserCreateBy) {
        this.owordUserCreateBy = owordUserCreateBy;
    }

    @ManyToOne
    @JoinColumn(name = "PRELIMINARY_BY")
    public User getOwordUserPreliminaryBy() {
        return this.owordUserPreliminaryBy;
    }

    public void setOwordUserPreliminaryBy(User owordUserPreliminaryBy) {
        this.owordUserPreliminaryBy = owordUserPreliminaryBy;
    }

    @ManyToOne
    @JoinColumn(name = "FINAL_BY")
    public User getOwordUserFinalBy() {
        return this.owordUserFinalBy;
    }

    public void setOwordUserFinalBy(User owordUserFinalBy) {
        this.owordUserFinalBy = owordUserFinalBy;
    }

    @ManyToOne
    @JoinColumn(name = "UPDATE_BY")
    public User getOwordUserUpdateBy() {
        return this.owordUserUpdateBy;
    }

    public void setOwordUserUpdateBy(User owordUserUpdateBy) {
        this.owordUserUpdateBy = owordUserUpdateBy;
    }

    @Lob
    @Column(name = "DOCUMENT", nullable = false)
    public byte[] getDocument() {
        return this.documentBlb;
    }

    public void setDocument(byte[] document) {
        this.documentBlb = document;
    }

    @Lob
    @Column(name = "PRELIMINARY_DOCUMENT")
    public byte[] getPreliminaryDocument() {
        return this.preliminaryDocument;
    }

    public void setPreliminaryDocument(byte[] preliminaryDocument) {
        this.preliminaryDocument = preliminaryDocument;
    }

    @Lob
    @Column(name = "FINAL_DOCUMENT")
    public byte[] getFinalDocument() {
        return this.finalDocument;
    }

    public void setFinalDocument(byte[] finalDocument) {
        this.finalDocument = finalDocument;
    }

    @Column(name = "MRN", nullable = false, length = 15)
    public String getMrn() {
        return this.mrn;
    }

    public void setMrn(String mrn) {
        this.mrn = mrn;
    }

    @ManyToOne
    @JoinColumn(name = "SITE", nullable = false)
    public SiteMapping getSite() {
        return this.site;
    }

    public void setSite(SiteMapping site) {
        this.site = site;
    }

    @Column(name = "VISIT_NUMBER", length = 15)
    public String getVisitNumber() {
        return this.visitNumber;
    }

    public void setVisitNumber(String visitNumber) {
        this.visitNumber = visitNumber;
    }

    @Column(name = "VISIT_TYPE", length = 50)
    public String getVisitType() {
        return this.visitType;
    }

    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }

    @Column(name = "PHYSICIAN_FIRST_NAME", length = 20)
    public String getPhysicianFirstName() {
        return this.physicianFirstName;
    }

    public void setPhysicianFirstName(String physicianFirstName) {
        this.physicianFirstName = physicianFirstName;
    }

    @Column(name = "PHYSICIAN_LAST_NAME", length = 40)
    public String getPhysicianLastName() {
        return this.physicianLastName;
    }

    public void setPhysicianLastName(String physicianLastName) {
        this.physicianLastName = physicianLastName;
    }

    @Column(name = "PHYSICIAN_LICENSE", length = 20)
    public String getPhysicianLicense() {
        return this.physicianLicense;
    }

    public void setPhysicianLicense(String physicianLicense) {
        this.physicianLicense = physicianLicense;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE", nullable = false, length = 7)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_DATE", length = 7)
    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "PRELIMINARY_DATE", length = 7)
    public Date getPreliminaryDate() {
        return this.preliminaryDate;
    }

    public void setPreliminaryDate(Date preliminaryDate) {
        this.preliminaryDate = preliminaryDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "FINAL_DATE", length = 7)
    public Date getFinalDate() {
        return this.finalDate;
    }

    public void setFinalDate(Date finalDate) {
        this.finalDate = finalDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ADMIT_DATE", length = 7)
    public Date getAdmitDate() {
        return this.admitDate;
    }

    public void setAdmitDate(Date admitDate) {
        this.admitDate = admitDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DISCHARGE_DATE", length = 7)
    public Date getDischargeDate() {
        return this.dischargeDate;
    }

    public void setDischargeDate(Date dischargeDate) {
        this.dischargeDate = dischargeDate;
    }

    @Column(name = "WORD_VERSION", length = 10)
    public String getWordVersion() {
        return this.wordVersion;
    }

    public void setWordVersion(String wordVersion) {
        this.wordVersion = wordVersion;
    }

    @Column(name = "ORDER_NUMBER", length = 100)
    public String getOrderNumber() {
        return this.orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    @ManyToOne
    @JoinColumn(name = "CLASSIFICATION_CODE")
    public Classification getOwordClassification() {
        return this.owordClassification;
    }

    public void setOwordClassification(Classification owordClassification) {
        this.owordClassification = owordClassification;
    }

    @ManyToOne
    @JoinColumn(name = "SERVICE_CODE")
    public ServiceCode getOwordServiceCode() {
        return this.owordServiceCode;
    }

    public void setOwordServiceCode(ServiceCode owordServiceCode) {
        this.owordServiceCode = owordServiceCode;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DOCUMENT_DATE", length = 7)
    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    @Column(name = "PRIVATE_DOCUMENT", length = 1)
    public String getPrivateDocument() {
        return privateDocument;
    }

    public void setPrivateDocument(String privateDocument) {
        this.privateDocument = privateDocument;
    }

    @Column(name = "WORKGROUP", length = 100)
    public String getWorkgroup() {
        return this.workgroup;
    }

    public void setWorkgroup(String workgroup) {
        this.workgroup = workgroup;
    }

    @ManyToOne
    @JoinColumn(name = "REVISION_STATUS_CODE", nullable = true)
    public RevisionStatusCode getOwordRevisionStatusCode() {
        return owordRevisionStatusCode;
    }

    public void setOwordRevisionStatusCode(RevisionStatusCode owordRevisionStatusCode) {
        this.owordRevisionStatusCode = owordRevisionStatusCode;
    }

    @Column(name = "FINAL_REVISION_COMPLETED", length = 1)
    public String getFinalRevisionCompleted() {
        return finalRevisionCompleted;
    }

    public void setFinalRevisionCompleted(String finalRevisionCompleted) {
        this.finalRevisionCompleted = finalRevisionCompleted;
    }

    @Column(name = "CONFIRMED_DIRECTIVES", length = 1)
    public String getConfirmedDirectives() {
        return confirmedDirectives;
    }

    public void setConfirmedDirectives(String confirmedDirectives) {
        this.confirmedDirectives = confirmedDirectives;
    }

    @ManyToOne
    @JoinColumn(name = "IN_USE_BY")
    public User getOwordUserInUseBy() {
        return owordUserInUseBy;
    }

    public void setOwordUserInUseBy(User owordUserInUseBy) {
        this.owordUserInUseBy = owordUserInUseBy;
    }

    @ManyToOne
    @JoinColumn(name = "ASSIGNED_TO")
    public User getOwordUserAssignedTo() {
        return owordUserAssignedTo;
    }

    public void setOwordUserAssignedTo(User owordUserAssignedTo) {
        this.owordUserAssignedTo = owordUserAssignedTo;
    }

    @PrePersist
    @PreUpdate
    public void updateUpdateDate() {
        this.updateDate = new Date();
    }

    public void setSendingApp(String sendingApp) {
        this.sendingApp = sendingApp;
    }

    @Column(name = "SENDING_APP", length = 20)
    public String getSendingApp() {
        return sendingApp;
    }

    public void setUserTitle(String userTitle) {
        this.userTitle = userTitle;
    }

    @Column(name = "USER_TITLE", length = 20)
    public String getUserTitle() {
        return userTitle;
    }

    public void setUserLicenceId(Long userLicenceId) {
        this.respLicenceId = userLicenceId;
    }

    @Column(name = "RESP_LICENCE_ID", length = 10)
    public Long getUserLicenceId() {
        return respLicenceId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 47 * hash + Objects.hashCode(this.documentId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Document other = (Document) obj;
        if (!Objects.equals(this.documentId, other.documentId)) {
            return false;
        }
        return true;
    }

}
