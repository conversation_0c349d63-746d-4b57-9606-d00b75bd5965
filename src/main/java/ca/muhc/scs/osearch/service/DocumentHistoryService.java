package ca.muhc.scs.osearch.service;

import ca.muhc.scs.osearch.model.DocumentHistory;
import ca.muhc.scs.osearch.repository.DocumentHistoryRepository;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;



@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class DocumentHistoryService {
    private DocumentHistoryRepository documentHistoryRepository;

    public DocumentHistoryService(DocumentHistoryRepository documentHistoryRepository) {
        this.documentHistoryRepository = documentHistoryRepository;
    }

    @Transactional
    public DocumentHistory save(DocumentHistory aDocumentHistory) {
        return documentHistoryRepository.save(aDocumentHistory);
    }
    
}
