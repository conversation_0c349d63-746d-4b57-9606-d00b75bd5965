/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Categorys
 * and open the template in the editor.
 */
package ca.muhc.scs.osearch.service;


import ca.muhc.scs.osearch.model.Category;
import ca.muhc.scs.osearch.repository.CategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class CategoryService {

    @Autowired
    private CategoryRepository categoryRepository;

    @Transactional
    public Category save(Category aCategory) {
        return categoryRepository.save(aCategory);
    }

    @Transactional
    public Category update(Category aCategory) {
        return categoryRepository.save(aCategory);
    }

    @Transactional
    public Optional<Category> get(Integer id) {
        return categoryRepository.findById(id);
    }

    public List<Category> getAll() {
        return categoryRepository.getAll();
    }

    public List<Category> getAllEn() {
        return categoryRepository.getAllEn();
    }

    public List<Category> getAllFr() {
        return categoryRepository.getAllFr();
    }

    public List<Category> getAllForFilterEn() {
        List<Category> categoryList = categoryRepository.getAllEn();
        Category category = new Category();
        category.setId(-1);
        category.setNameEn("");
        category.setNameFr("");
        categoryList.add(0, category);
        return categoryList;
    }

    public List<Category> getAllForFilterFr() {
        List<Category> categoryList = categoryRepository.getAllFr();
        Category category = new Category();
        category.setId(-1);
        category.setNameEn("");
        category.setNameFr("");
        categoryList.add(0, category);
        return categoryList;
    }

    /*     public DataTablesOutput<Category> findAll(DataTablesInput input) {
            return categoryRepository.findAll(input);
        }
     */
    public List<Category> findAll() {
        return categoryRepository.findAll();
    }

    public Page<Category> findCategoriesPageable(Pageable pageable) {
        // Création d'un Pageable Spring
        int pageSize = pageable.getPageSize();
        int currentPage = pageable.getPageNumber();
        int startItem = currentPage * pageSize;
        List<Category> list;
        List<Category> listCategories = categoryRepository.findAll();


        if (listCategories.size() < startItem) {
            list = Collections.emptyList();
        } else {
            int toIndex = Math.min(startItem + pageSize, listCategories.size());
            list = listCategories.subList(startItem, toIndex);
        }

        Page<Category> categoryPage
                = new PageImpl<Category>(list, PageRequest.of(currentPage, pageSize), listCategories.size());
        return categoryPage;

    }
}
