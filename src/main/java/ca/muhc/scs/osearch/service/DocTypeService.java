/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | DocTypes
 * and open the template in the editor.
 */
package ca.muhc.scs.osearch.service;

import ca.muhc.scs.osearch.model.DocType;
import ca.muhc.scs.osearch.repository.DocTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class DocTypeService {
    @Autowired
    private DocTypeRepository docTypeRepository;
    
    @Transactional
    public DocType save(DocType aDocType) {
        return docTypeRepository.save(aDocType);
    }

    @Transactional
    public DocType update(DocType aDocType) {
        return docTypeRepository.save(aDocType);
    }

    @Transactional
    public DocType get(String id) {
        return docTypeRepository.get(id);
    }
    
    @Transactional
    public Optional<DocType> findOne(String id) {
        return docTypeRepository.findById(id);
    }

     
    public DataTablesOutput<DocType> findAll(DataTablesInput input) {
        return docTypeRepository.findAll(input);
    }
    
    public Page<DocType> findAll(Pageable pageable) {
        return docTypeRepository.findAll(pageable);
    }
     
    public List<DocType> getAll() {
        return docTypeRepository.getAll();
    }
    
    public List<DocType> getAllForFilter() {
        List<DocType> docTypeList = docTypeRepository.getAll();
        DocType docType = new DocType();
        docType.setDocTypeCode("");
        docType.setNameEn("");
        docType.setNameFr("");
        docTypeList.add(0, docType);
        return docTypeList;
    }
    
}
