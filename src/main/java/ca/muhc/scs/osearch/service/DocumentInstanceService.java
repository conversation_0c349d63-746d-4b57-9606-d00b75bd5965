package ca.muhc.scs.osearch.service;


import ca.muhc.scs.osearch.model.DocumentInstance;
import ca.muhc.scs.osearch.repository.DocumentInstanceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class DocumentInstanceService {
    @Autowired
    private  DocumentInstanceRepository  documentInstanceRepository;

    @Transactional
    public DocumentInstance get(String id) {
        return documentInstanceRepository.get(id);
    }

    public List<DocumentInstance> getAll() {
        return documentInstanceRepository.getAll();
    }
    
    public DataTablesOutput<DocumentInstance> findAll(DataTablesInput input) {
        return documentInstanceRepository.findAll(input);
    }
}
