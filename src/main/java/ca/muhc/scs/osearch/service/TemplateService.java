/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | DocTypes
 * and open the template in the editor.
 */
package ca.muhc.scs.osearch.service;

import ca.muhc.scs.osearch.model.ServiceCode;
import ca.muhc.scs.osearch.model.Template;
import ca.muhc.scs.osearch.dto.TemplateDTO;
import ca.muhc.scs.osearch.model.DocType;
import ca.muhc.scs.osearch.repository.TemplateRepository;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.data.jpa.datatables.mapping.DataTablesInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class TemplateService {
    @Autowired
    private TemplateRepository templateRepository;
    
    @Autowired
    private DocTypeService docTypeService;

    @Transactional
    public Template save(Template aTemplate) {
        return templateRepository.save(aTemplate);
    }

    @Transactional
    public Template update(Template aTemplate) {
        return templateRepository.save(aTemplate);
    }

    public Template get(Long id) {
        return templateRepository.get(id);
    }
    
    public Optional<Template> findOne(Integer id) {
        return templateRepository.findById(id);
    }

    public DataTablesOutput<Template> findAll(DataTablesInput input) {
        return templateRepository.findAll(input,TemplateSpec.isActive("ACTIVE"));
    }
    
    public List<Template> getAll() {
        return templateRepository.getAll();
    }

    public List<TemplateDTO>  findTemplateVersion(String docType) {
        List<String[]> templates = templateRepository.findTemplateVersion(docType);
        List<TemplateDTO> np = new ArrayList<TemplateDTO>();
        for (String[] t : templates) {
            np.add(new TemplateDTO(Long.parseLong(t[0]), t[1], t[2], t[3], t[11], t[4], t[5], t[6], null, t[7], null, null,  null,  null, t[8], t[9], null, null, null, null, null, null, null, t[10]));
        }
        
        return np;
    }
    
    public  int countActiveTemplateBy(String docType) {
        return (templateRepository.countActiveTemplateBy(docType));
    }
    
    public List<Template> getActiveTemplateBy(String docType) {
        return (templateRepository.getActiveTemplateBy(docType));
    }
    
    
    public List<Template> findByDocType(String docType) {
        return templateRepository.findByDocType(docType);
    }
    
    public List<Template> findByClassication(String code) {
        return templateRepository.findByClassication(code);
    }
    
    public List<ServiceCode> findByServiceCode(String serviceCode) {
        return templateRepository.findByServiceCode(serviceCode);
    }
    
    public List<Template> findByStatus(String status) {
        return templateRepository.findByStatus(status);
    }
    
    public Page<Template> getActiveTemplateByPage(Pageable pageable) {
        return templateRepository.getActiveTemplateByPage(pageable);
    } 
    
    
    public List<TemplateDTO> getActiveTemplate() {
        List<String[]> templates = templateRepository.getActiveTemplate();
        List<TemplateDTO> np = new ArrayList<TemplateDTO>();
        for (String[] t : templates) {
            np.add(new TemplateDTO(Long.parseLong(t[0]), t[1], t[2], t[3], null, t[4], t[5], t[6], null, t[7], null, null,  null,  null, t[8], t[9], null, null, null, null, null, null, null, t[10]));
        }
        
        return np;
    }
    
    public List<DocType> getListOfOrphenDocType() {
        
        List<DocType> docTypeList = docTypeService.getAll();
        
        List<DocType> orphanDocType = new ArrayList<DocType>();
        List<String> listActiveDocType = templateRepository.getActiveDocType();
        
        for (DocType docType : docTypeList) {
            if (! listActiveDocType.contains(docType.getDocTypeCode())) {
                orphanDocType.add(docType);
            }
        }
        
        return orphanDocType;
        
        
        
    }
    

}
