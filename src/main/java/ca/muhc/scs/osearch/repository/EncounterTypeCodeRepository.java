package ca.muhc.scs.osearch.repository;

import ca.muhc.scs.osearch.model.DocumentInstance;
import ca.muhc.scs.osearch.model.EncounterTypeCode;
import java.util.List;
import org.springframework.data.jpa.datatables.repository.DataTablesRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */

@Repository
public interface EncounterTypeCodeRepository extends DataTablesRepository<EncounterTypeCode, String> {

    @Query("select et from EncounterTypeCode et order by et.encounterType ")
    public List<EncounterTypeCode> getAll();
    
    @Query("select et from EncounterTypeCode et where et.encounterType = ?1")
    public EncounterTypeCode get(String encounterType);
    
}
