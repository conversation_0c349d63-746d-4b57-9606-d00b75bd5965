package ca.muhc.scs.osearch.dto;

import ca.muhc.scs.osearch.model.*;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;

public class TemplateDTO {

    private long templateId;
    private String owordDocType;
    private String owordClassification;
    private String createDate;
    private String templateStatus;
    private String allowDraft;
    private String allowPreliminary;
    private String allowPrinting;
    private String alias;
    private String aliasDescription;
    private String comments;
    private String owordServiceCode;
    private String printOnly;
    private String disablePrintingDraft;
    private String allowCopyPrevious;
    private String permission;
    private String allowWorkgroup;
    private String copyPreviousByDefault;
    private String copyPreviousMandatory;
    private String reviewable;
    private String editableByArchivist;
    private String owordEncounterTypeCode;
    private String allowNoVisitID;
    private String allowCopyFromPreviousForUser;

    public TemplateDTO(long templateId, String owordDocType, String owordClassification, String createDate, String templateStatus, 
            String allowDraft, String allowPreliminary, String allowPrinting, String alias, String aliasDescription, String comments, 
            String owordServiceCode, String printOnly, String disablePrintingDraft, String allowCopyPrevious, 
            String permission, String allowWorkgroup, String copyPreviousByDefault, String copyPreviousMandatory, String reviewable, String editableByArchivist, String owordEncounterTypeCode, String allowNoVisitID, String allowCopyFromPreviousForUser) {
        this.templateId = templateId;
        this.owordDocType = owordDocType;
        this.owordClassification = owordClassification;
        this.createDate = createDate;
        this.templateStatus = templateStatus;
        this.allowDraft = allowDraft;
        this.allowPreliminary = allowPreliminary;
        this.allowPrinting = allowPrinting;
        this.alias = alias;
        this.aliasDescription = aliasDescription;
        this.comments = comments;
        this.owordServiceCode = owordServiceCode;
        this.printOnly = printOnly;
        this.disablePrintingDraft = disablePrintingDraft;
        this.allowCopyPrevious = allowCopyPrevious;
        this.permission = permission;
        this.allowWorkgroup = allowWorkgroup;
        this.copyPreviousByDefault = copyPreviousByDefault;
        this.copyPreviousMandatory = copyPreviousMandatory;
        this.reviewable = reviewable;
        this.editableByArchivist = editableByArchivist;
        this.owordEncounterTypeCode = owordEncounterTypeCode;
        this.allowNoVisitID = allowNoVisitID;
        this.allowCopyFromPreviousForUser = allowCopyFromPreviousForUser;
    }

    

    public long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(long templateId) {
        this.templateId = templateId;
    }

    public String getOwordDocType() {
        return owordDocType;
    }

    public void setOwordDocType(String owordDocType) {
        this.owordDocType = owordDocType;
    }

    public String getOwordClassification() {
        return owordClassification;
    }

    public void setOwordClassification(String owordClassification) {
        this.owordClassification = owordClassification;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getTemplateStatus() {
        return templateStatus;
    }

    public void setTemplateStatus(String templateStatus) {
        this.templateStatus = templateStatus;
    }

    public String getAllowDraft() {
        return allowDraft;
    }

    public void setAllowDraft(String allowDraft) {
        this.allowDraft = allowDraft;
    }

    public String getAllowPreliminary() {
        return allowPreliminary;
    }

    public void setAllowPreliminary(String allowPreliminary) {
        this.allowPreliminary = allowPreliminary;
    }

    public String getAllowPrinting() {
        return allowPrinting;
    }

    public void setAllowPrinting(String allowPrinting) {
        this.allowPrinting = allowPrinting;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getAliasDescription() {
        return aliasDescription;
    }

    public void setAliasDescription(String aliasDescription) {
        this.aliasDescription = aliasDescription;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getOwordServiceCode() {
        return owordServiceCode;
    }

    public void setOwordServiceCode(String owordServiceCode) {
        this.owordServiceCode = owordServiceCode;
    }

    public String getPrintOnly() {
        return printOnly;
    }

    public void setPrintOnly(String printOnly) {
        this.printOnly = printOnly;
    }

    public String getDisablePrintingDraft() {
        return disablePrintingDraft;
    }

    public void setDisablePrintingDraft(String disablePrintingDraft) {
        this.disablePrintingDraft = disablePrintingDraft;
    }

    public String getAllowCopyPrevious() {
        return allowCopyPrevious;
    }

    public void setAllowCopyPrevious(String allowCopyPrevious) {
        this.allowCopyPrevious = allowCopyPrevious;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getAllowWorkgroup() {
        return allowWorkgroup;
    }

    public void setAllowWorkgroup(String allowWorkgroup) {
        this.allowWorkgroup = allowWorkgroup;
    }

    public String getCopyPreviousByDefault() {
        return copyPreviousByDefault;
    }

    public void setCopyPreviousByDefault(String copyPreviousByDefault) {
        this.copyPreviousByDefault = copyPreviousByDefault;
    }

    public String getCopyPreviousMandatory() {
        return copyPreviousMandatory;
    }

    public void setCopyPreviousMandatory(String copyPreviousMandatory) {
        this.copyPreviousMandatory = copyPreviousMandatory;
    }

    public String getReviewable() {
        return reviewable;
    }

    public void setReviewable(String reviewable) {
        this.reviewable = reviewable;
    }

    public String getEditableByArchivist() {
        return editableByArchivist;
    }

    public void setEditableByArchivist(String editableByArchivist) {
        this.editableByArchivist = editableByArchivist;
    }

    public String getOwordEncounterTypeCode() {
        return owordEncounterTypeCode;
    }

    public void setOwordEncounterTypeCode(String owordEncounterTypeCode) {
        this.owordEncounterTypeCode = owordEncounterTypeCode;
    }

    public String getAllowNoVisitID() {
        return allowNoVisitID;
    }

    public void setAllowNoVisitID(String allowNoVisitID) {
        this.allowNoVisitID = allowNoVisitID;
    }

    public String getAllowCopyFromPreviousForUser() {
        return allowCopyFromPreviousForUser;
    }

    public void setAllowCopyFromPreviousForUser(String allowCopyFromPreviousForUser) {
        this.allowCopyFromPreviousForUser = allowCopyFromPreviousForUser;
    }

}
