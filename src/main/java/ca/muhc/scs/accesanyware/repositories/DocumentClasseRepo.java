package ca.muhc.scs.accesanyware.repositories;

import ca.muhc.scs.accesanyware.entities.DocumentClasse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;


public interface DocumentClasseRepo extends PagingAndSortingRepository<DocumentClasse, Long> {

    @Query(
            value = """
                    
                    select t.document_class_id as classid,t.code as classcode,
                     sce.string_value as name_fr, sce1.string_value as name_en
                    from HIMS_DOCUMENT_CLASS t,
                             strm_config_entry sce,
                             strm_config_entry sce1,
                             strm_config_entry sce2,
                             strm_config_entry sce3
                    where sce.path = 'DocumentClass.'||to_char(t.document_class_id)||'.fr-CA.Name' and sce1.path = 'DocumentClass.'||to_char(t.document_class_id)||'.en-US.Name'
                    and sce2.path = 'DocumentClass.'||to_char(t.document_class_id)||'.fr-CA.Description' and sce3.path = 'DocumentClass.'||to_char(t.document_class_id)||'.en-US.Description'
                    order by 1
                    """,
            nativeQuery = true)
    Page<DocumentClasse> getAllDocumentClassification(Pageable pageable);

    @Query("select dc from DocumentClasse dc where dc.classid = ?1")
    Optional<DocumentClasse> findById(Long id);


}
