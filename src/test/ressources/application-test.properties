app.version=1.0.1.Test
# Primary DataSource OWord QA
spring.datasource.osearch.url=******************************************************
spring.datasource.osearch.username=OWORD_QA
spring.datasource.osearch.driver-class-name=oracle.jdbc.driver.OracleDriver
# Secondary DataSource OWord_Tools QA
spring.datasource.owordtools.url=*********************************************
spring.datasource.owordtools.username=root
spring.datasource.owordtools.driver-class-name=org.mariadb.jdbc.Driver
#Accesanyware stest datasource
spring.datasource.aaw.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.aaw.url=*******************************************************
spring.datasource.aaw.username=STRM_GUA

server.port=9797
spring.config.import=file:c:/data/input/config/qa/secret.properties
