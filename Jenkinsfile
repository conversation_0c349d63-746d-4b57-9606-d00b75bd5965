pipeline {
    agent {
        label "OwordTools-QA-Agent"
    }

    options {
      buildDiscarder logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '5', daysToKeepStr: '', numToKeepStr: '5')
    }
    stages {
            stage('Preparer ENV'){
                steps{
                    sh 'java --version'
                    sh 'gradle -version'
                }
            }
                stage('SonarQube Static Analysis') {
                    steps{
                            withSonarQubeEnv(installationName: 'MuhcSonarQube') {
                              sh "./gradlew sonar \
                                   -Dsonar.projectKey=CCHOS_cchospital_AY_oMGPI9ZybecCsyfY- \
                                   -Dsonar.projectName='cchospital' \
                                   -Dsonar.host.url=https://sonarqube.muhcad.muhcfrd.ca \
                                   -Dsonar.token=sqp_f2c201dfb9bc9f9b9f9df4015e50fc2121888abe"
                            }
                    }
                }



                stage('Test Unitaire et test Intégration') {
                    parallel {
                            stage('Test Unitaire') {
                                steps {
                                    sh "./gradlew test"
                                    }
                                }


                          stage('Test Intégration') {
                              steps{
                                    echo "In progress"

                              }
                          }
                    }
                }

              stage('Build OwordTools Artifact') {
                  steps {
                    sh './gradlew clean bootJar'
                  }
              }
              stage('Deploy OwordTools Service') {
                  steps {
                                    script {
                                        def output = sh(returnStdout: true, script: 'chmod u+x $WORKSPACE/ci/deploy_service.sh ;$WORKSPACE/ci/deploy_service.sh')
                                        echo "Output: ${output}"
                                    }
                  }
              }
            stage('Start CCHOSPITAL Service') {
                steps {
                  sh """
                  echo ********************
                  echo * Starting service *
                  echo ********************
                  sudo systemctl start cchospital.service
                  """
                }
            }

    }

}