pipeline {
    agent {
        label "OwordTools-QA-Agent"
    }
   tools {
        maven 'maven-3.9.0'
    }
    options {
      buildDiscarder logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '5', daysToKeepStr: '', numToKeepStr: '5')
    }
    stages {
            stage('Preparer ENV'){
                steps{
                    bat 'java --version'
                    bat 'mvn --version'
                }
            }

                stage('Test Unitaire et test Intégration') {
                    parallel {
                            stage('Test Unitaire') {
                                steps {
                                    bat "mvn test"
                                    }
                                }


                          stage('Test Intégration') {
                              steps{
                                    echo "In progress"

                              }
                          }
                    }
                }

              stage('Build OwordTools Artifact') {
                  steps {
                    script {
                        try {
                            echo "Building OwordTools JAR artifact..."
                            def buildResult = bat(returnStatus: true, script: 'mvn clean package -DskipTests')
                            if (buildResult != 0) {
                                error "Maven build failed with exit code: ${buildResult}"
                            }
                            echo "Maven build completed successfully!"

                            // Debug: Show target directory contents
                            echo "Contents of target directory:"
                            bat 'dir "%WORKSPACE%\\target"'

                            // Verify that the JAR was created
                            def jarFiles = bat(returnStdout: true, script: 'dir /b "%WORKSPACE%\\target\\*.jar" 2>nul || echo NONE').trim()
                            if (jarFiles == 'NONE' || jarFiles.isEmpty()) {
                                echo "ERROR: No JAR files found in target directory"
                                echo "Listing all files in target directory:"
                                bat 'dir "%WORKSPACE%\\target\\*.*" /s'
                                echo "Checking workspace path:"
                                bat 'echo Workspace: %WORKSPACE%'
                                error "JAR file was not created in target directory. Maven build may have failed."
                            }
                            echo "JAR file(s) found: ${jarFiles}"

                            // Additional verification for the specific JAR we expect
                            def expectedJar = 'oAdmin-1.0.1-SNAPSHOT.jar'
                            def specificJarExists = bat(returnStdout: true, script: "if exist \"%WORKSPACE%\\target\\${expectedJar}\" (echo EXISTS) else (echo NOT_FOUND)").trim()
                            if (specificJarExists == 'EXISTS') {
                                echo "✅ Expected JAR file found: ${expectedJar}"
                            } else {
                                echo "⚠️  Expected JAR ${expectedJar} not found, but other JAR files exist: ${jarFiles}"
                            }
                            echo "JAR artifact built successfully!"

                        } catch (Exception e) {
                            echo "ERROR: Build failed with exception: ${e.getMessage()}"
                            currentBuild.result = 'FAILURE'
                            throw e
                        }
                    }
                  }
              }
              stage('Deploy OwordTools Service') {
                  steps {
                                    script {
                                        try {
                                            echo "Starting deployment process..."
                                            def result = bat(returnStdout: true, returnStatus: true, script: '%WORKSPACE%\\ci\\deploy_owordtools.bat')

                                            if (result.status != 0) {
                                                error "Deployment script failed with exit code: ${result.status}"
                                            }

                                            echo "Deployment script output:"
                                            echo "${result.stdout}"
                                            echo "Deployment completed successfully!"

                                        } catch (Exception e) {
                                            echo "ERROR: Deployment failed with exception: ${e.getMessage()}"
                                            echo "Pipeline will be marked as FAILED"
                                            currentBuild.result = 'FAILURE'
                                            throw e
                                        }
                                    }
                  }
              }
            stage('Start OwordTools Service') {
                steps {
                  bat """
                  echo ********************
                  echo * Service started via deploy script *
                  echo ********************
                  echo OwordTools service has been started by the deployment script
                  """
                }
            }

    }

    post {
        always {
            echo 'Pipeline execution completed.'
        }
        success {
            echo 'Pipeline executed successfully!'
            echo 'OwordTools has been deployed and started successfully.'
        }
        failure {
            echo 'Pipeline failed!'
            echo 'Please check the logs above for error details.'
            script {
                if (currentBuild.result == 'FAILURE') {
                    echo 'Deployment script returned an error. Please check the deployment logs.'
                }
            }
        }
        unstable {
            echo 'Pipeline is unstable. Please review the warnings.'
        }
    }

}