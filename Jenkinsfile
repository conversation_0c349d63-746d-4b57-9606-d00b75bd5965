pipeline {
    agent {
        label "OwordTools-QA-Agent"
    }
   tools {
        maven 'maven-3.9.0'
    }
    options {
      buildDiscarder logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '5', daysToKeepStr: '', numToKeepStr: '5')
    }
    stages {
            stage('Preparer ENV'){
                steps{
                    bat 'java --version'
                    bat 'mvn --version'
                }
            }

                stage('Test Unitaire et test Intégration') {
                    parallel {
                            stage('Test Unitaire') {
                                steps {
                                    bat "mvn test"
                                    }
                                }


                          stage('Test Intégration') {
                              steps{
                                    echo "In progress"

                              }
                          }
                    }
                }

              stage('Build OwordTools Artifact') {
                  steps {
                    script {
                        try {
                            echo "Building OwordTools JAR artifact..."
                            bat 'mvn clean package -DskipTests'

                            // Verify that the JAR was created
                            def jarExists = fileExists('target/*.jar')
                            if (!jarExists) {
                                error "JAR file was not created in target directory"
                            }
                            echo "JAR artifact built successfully!"

                        } catch (Exception e) {
                            echo "ERROR: Build failed with exception: ${e.getMessage()}"
                            currentBuild.result = 'FAILURE'
                            throw e
                        }
                    }
                  }
              }
              stage('Deploy OwordTools Service') {
                  steps {
                                    script {
                                        try {
                                            echo "Starting deployment process..."
                                            def result = bat(returnStdout: true, returnStatus: true, script: '%WORKSPACE%\\ci\\deploy_owordtools.bat')

                                            if (result.status != 0) {
                                                error "Deployment script failed with exit code: ${result.status}"
                                            }

                                            echo "Deployment script output:"
                                            echo "${result.stdout}"
                                            echo "Deployment completed successfully!"

                                        } catch (Exception e) {
                                            echo "ERROR: Deployment failed with exception: ${e.getMessage()}"
                                            echo "Pipeline will be marked as FAILED"
                                            currentBuild.result = 'FAILURE'
                                            throw e
                                        }
                                    }
                  }
              }
            stage('Start OwordTools Service') {
                steps {
                  bat """
                  echo ********************
                  echo * Service started via deploy script *
                  echo ********************
                  echo OwordTools service has been started by the deployment script
                  """
                }
            }

    }

    post {
        always {
            echo 'Pipeline execution completed.'
        }
        success {
            echo 'Pipeline executed successfully!'
            echo 'OwordTools has been deployed and started successfully.'
        }
        failure {
            echo 'Pipeline failed!'
            echo 'Please check the logs above for error details.'
            script {
                if (currentBuild.result == 'FAILURE') {
                    echo 'Deployment script returned an error. Please check the deployment logs.'
                }
            }
        }
        unstable {
            echo 'Pipeline is unstable. Please review the warnings.'
        }
    }

}